<template>
  <el-container style="min-height: 100vh; min-width: 100vw">
    <el-aside :width="imageSettings.containerWidth" class="aside" :style="sideContainerStyle">
      <div class="image-container" :style="imageContainerStyle">
        <el-image 
          :fit="currentImageSettings.fit" 
          :src="currentBg" 
          alt="" 
          :style="currentImageStyle" 
        />
      </div>
    </el-aside>
    <el-main class="main">
      <div class="main-content-wrapper">
        <el-space direction="vertical" fill style="width: 100%; max-width: 700px;" size="large">
          <div class="title-container">
            <h1 style="font-size: 34px; text-align: center; margin-bottom: 8px; font-weight: 600; letter-spacing: 2px; color: #303133;" @click="goSetting">
              骨振识息系统
            </h1>
            <h2 style="font-size: 18px; color: #909399; text-align: center; font-weight: normal; margin-bottom: 30px;">
              {{ route.name === 'Login' ? '账号登录' : (currentStepValue === '8' ? '注册成功' : '注册') }}
            </h2>
          </div>
          <RouterView />
        </el-space>
      </div>
      <div class="footer">
        <router-link v-if="route.name === 'Login'" to="/register"> 注册账号 </router-link>
        <router-link v-else-if="route.name === 'Register' && currentStepValue !== '8'" to="/">账号登录</router-link>
        <router-link v-if="route.name !== 'Login' && currentStepValue === '8'" to="/">返回登录</router-link>
      </div>
    </el-main>
  </el-container>
  <el-dialog
    title="设置"
    v-model="settingDialog"
    width="60%" 
    >
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本与容器" name="basic">
        <el-form label-width="150px" :inline="false" size="normal">
          <el-form-item label="是否造假">
            <el-switch v-model="switcher" active-value="1" inactive-value="0"></el-switch>
          </el-form-item>
          <el-form-item label="造假结果">
            <el-input v-model="key" placeholder="请输入当前造假值类似：1100" />
          </el-form-item>
          <el-form-item label="当前顺序">
            <el-input v-model="current" placeholder="请输入当前造假到第几个了" />
          </el-form-item>
          
          <el-divider>图片容器设置</el-divider>
          <el-form-item label="容器宽度">
            <el-input v-model="imageSettings.containerWidth" placeholder="例如：45%" />
          </el-form-item>
          <el-form-item label="容器背景颜色">
            <el-color-picker v-model="imageSettings.bgColor" />
          </el-form-item>
          <el-form-item label="容器内边距">
            <el-input v-model="imageSettings.padding" placeholder="例如：10px" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="特定页面图片样式" name="specificPages">
        <el-form label-width="150px" :inline="false" size="normal">
          <el-divider>登录页面图片</el-divider>
          <el-form-item label="登录页图片路径">
            <el-input v-model="loginImagePath" placeholder="例如：/pic/login.png" />
          </el-form-item>
          <el-form-item label="填充方式">
            <el-select v-model="loginPageImageStyle.fit">
              <el-option label="覆盖 (cover)" value="cover" />
              <el-option label="包含 (contain)" value="contain" />
              <el-option label="填充 (fill)" value="fill" />
              <el-option label="无 (none)" value="none" />
              <el-option label="缩小 (scale-down)" value="scale-down" />
            </el-select>
          </el-form-item>
          <el-form-item label="宽度"><el-input v-model="loginPageImageStyle.width" placeholder="100%" /></el-form-item>
          <el-form-item label="高度"><el-input v-model="loginPageImageStyle.height" placeholder="100%" /></el-form-item>
          <el-form-item label="对象位置"><el-input v-model="loginPageImageStyle.objectPosition" placeholder="center center" /></el-form-item>
          <el-form-item label="圆角"><el-input v-model="loginPageImageStyle.borderRadius" placeholder="0px" /></el-form-item>

          <el-divider>注册完成页图片</el-divider>
          <el-form-item label="注册完成页图片路径">
            <el-input v-model="registrationCompleteImagePath" placeholder="例如：/pic/ok.png" />
          </el-form-item>
            <el-form-item label="填充方式">
            <el-select v-model="regCompletePageImageStyle.fit">
              <el-option label="覆盖 (cover)" value="cover" />
              <el-option label="包含 (contain)" value="contain" />
              <el-option label="填充 (fill)" value="fill" />
              <el-option label="无 (none)" value="none" />
              <el-option label="缩小 (scale-down)" value="scale-down" />
            </el-select>
          </el-form-item>
          <el-form-item label="宽度"><el-input v-model="regCompletePageImageStyle.width" placeholder="80%" /></el-form-item>
          <el-form-item label="高度"><el-input v-model="regCompletePageImageStyle.height" placeholder="80%" /></el-form-item>
          <el-form-item label="对象位置"><el-input v-model="regCompletePageImageStyle.objectPosition" placeholder="center center" /></el-form-item>
          <el-form-item label="圆角"><el-input v-model="regCompletePageImageStyle.borderRadius" placeholder="0px" /></el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane 
        v-for="i in 7" 
        :key="i" 
        :label="`步骤 ${i} 图片样式`" 
        :name="`image${i}`"
      >
        <el-form label-width="120px" :inline="false" size="normal">
          <el-form-item label="预览">
            <el-image 
              style="width: 200px; height: 150px; border: 1px solid #ddd;" 
              :src="`/pic/step${i}.png`" 
              :fit="perImageSettings[i].fit"
            />
          </el-form-item>
          <el-form-item label="填充方式">
            <el-select v-model="perImageSettings[i].fit">
              <el-option label="覆盖 (cover)" value="cover" />
              <el-option label="包含 (contain)" value="contain" />
              <el-option label="填充 (fill)" value="fill" />
              <el-option label="无 (none)" value="none" />
              <el-option label="缩小 (scale-down)" value="scale-down" />
            </el-select>
          </el-form-item>
          <el-form-item label="宽度">
            <el-input v-model="perImageSettings[i].width" placeholder="例如：100%" />
          </el-form-item>
          <el-form-item label="高度">
            <el-input v-model="perImageSettings[i].height" placeholder="例如：100%" />
          </el-form-item>
          <el-form-item label="对象位置">
            <el-input v-model="perImageSettings[i].objectPosition" placeholder="例如：center center" />
          </el-form-item>
          <el-form-item label="边框圆角">
            <el-input v-model="perImageSettings[i].borderRadius" placeholder="例如：10px" />
          </el-form-item>
          <el-form-item label="边框">
            <el-input v-model="perImageSettings[i].border" placeholder="例如：1px solid #ddd" />
          </el-form-item>
          <el-form-item label="阴影">
            <el-input v-model="perImageSettings[i].boxShadow" placeholder="例如：0 2px 12px rgba(0,0,0,0.1)" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="步骤图片映射" name="mapping">
        <el-form label-width="150px" :inline="false" size="normal">
          <el-form-item 
            v-for="step in 7" 
            :key="`step${step-1}`" 
            :label="`步骤 ${step} 使用图片`"
          >
            <el-select v-model="stepImageMapping[step-1]">
              <el-option 
                v-for="i in 7" 
                :key="i" 
                :label="`图片 ${i} (step${i}.png)`" 
                :value="i" 
              />
            </el-select>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    
    <template #footer>
    <span>
      <el-button @click="settingDialog = false">取消</el-button>
      <el-button type="primary" @click="saveSetting">确定</el-button>
      <el-button type="success" @click="resetSettings">重置所有图片设置</el-button>
    </span>
    </template>
  </el-dialog>
  
</template>

<script setup>
import { watchEffect, ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()

// General App State & Query Params
watchEffect(() => {
  if (route.query.switch) localStorage.setItem('switch', route.query.switch)
  if (route.query.current) localStorage.setItem('current', route.query.current)
  if (route.query.key?.match(/^[01]+$/)) {
    localStorage.setItem('key', route.query.key)
    if (!localStorage.getItem('current')) localStorage.setItem('current', '1')
  }
})
const switcher = ref(localStorage.getItem('switch') || '0')
const current = ref(localStorage.getItem('current') || '1')
const key = ref(localStorage.getItem('key') || '')
const activeTab = ref('basic') // Default tab in settings

// Image Container Settings
const imageSettings = ref({
  containerWidth: localStorage.getItem('imgContainerWidth') || '45%',
  padding: localStorage.getItem('imgPadding') || '0px',
  bgColor: localStorage.getItem('imgBgColor') || '#ffffff'
})

// Specific Page Image Paths
const loginImagePath = ref(localStorage.getItem('loginImagePath') || '/pic/login.png')
const registrationCompleteImagePath = ref(localStorage.getItem('registrationCompleteImagePath') || '/pic/ok.png')

// Default Style Object for an image
const defaultIndImageStyle = {
  fit: 'contain',
  width: '100%', // Default to full width within its constrained container
  height: '100%',// Default to full height
  objectPosition: 'center center',
  borderRadius: '0px',
  border: 'none',
  boxShadow: 'none'
}

// Login Page Image Style Settings
const loginPageImageStyle = ref(
  JSON.parse(localStorage.getItem('loginPageImageStyle')) || 
  { ...defaultIndImageStyle, fit: 'cover' } // Login often uses cover
)

// Registration Complete Page Image Style Settings
const regCompletePageImageStyle = ref(
  JSON.parse(localStorage.getItem('regCompletePageImageStyle')) || 
  { ...defaultIndImageStyle, width: '80%', height: '80%' } // ok.png might be smaller
)

// Per-Step Image Style Settings (for step1.png to step7.png)
const perImageSettings = ref({})
for (let i = 1; i <= 7; i++) {
  try {
    const savedSettings = localStorage.getItem(`imgSettings_step${i}`)
    perImageSettings.value[i] = savedSettings ? JSON.parse(savedSettings) : { ...defaultIndImageStyle }
  } catch (e) {
    console.error(`加载图片 step${i} 设置失败`, e)
    perImageSettings.value[i] = { ...defaultIndImageStyle }
  }
}

// Mapping of Registration UI Step (0-6) to Image Number (1-7 for stepX.png)
const stepImageMapping = ref({ 0: 1, 1: 2, 2: 3, 3: 4, 4: 1, 5: 6, 6: 7 })
try {
  const savedMapping = localStorage.getItem('stepImageMapping')
  if (savedMapping) stepImageMapping.value = JSON.parse(savedMapping)
} catch (e) { console.error('加载步骤图片映射失败', e) }

// Current Registration Step (from HomeView, stored in localStorage)
const currentStepValue = ref(localStorage.getItem('currentStep') || '0')

// Determine which image settings to apply based on route and step
const currentImageSettings = computed(() => {
  if (route.name === 'Login') {
    return loginPageImageStyle.value
  }
  if (currentStepValue.value === '8') { // Registration successful
    return regCompletePageImageStyle.value
  }
  // For registration steps 0-6 (UI steps 1-7)
  const stepNumForStyle = parseInt(currentStepValue.value) // 0 to 6
  const imageNumToUse = stepImageMapping.value[stepNumForStyle] || 1 // Default to image 1 if mapping fails
  return perImageSettings.value[imageNumToUse] || defaultIndImageStyle
})

// Determine the src for the main image
const currentBg = computed(() => {
  if (route.name === 'Login') {
    return loginImagePath.value
  }
  if (currentStepValue.value === '8') { // Registration successful
    return registrationCompleteImagePath.value
  }
  // For registration steps 0-6 (UI steps 1-7) and processing (step 7 in HomeView)
  const stepNumForBg = parseInt(currentStepValue.value) // 0 to 7
  if (stepNumForBg === 7) { // Processing step, use the image for the 6th mapping (UI step 7)
    const imageNumForProcessing = stepImageMapping.value[6] || 1 // stepImageMapping is 0-indexed for UI steps
    return `/pic/step${imageNumForProcessing}.png`
  }
  const imageNum = stepImageMapping.value[stepNumForBg] || 1
  return `/pic/step${imageNum}.png`
})

// Dynamically generate the style object for the el-image
const currentImageStyle = computed(() => ({
  width: currentImageSettings.value.width,
  height: currentImageSettings.value.height,
  objectPosition: currentImageSettings.value.objectPosition,
  borderRadius: currentImageSettings.value.borderRadius,
  border: currentImageSettings.value.border,
  boxShadow: currentImageSettings.value.boxShadow,
  // fit is a prop, not a style
}))

// Style for the <el-aside> container
const sideContainerStyle = computed(() => ({
  background: imageSettings.value.bgColor || '#ffffff',
  padding: imageSettings.value.padding
}))

// Style for the inner div that directly wraps el-image, for centering
const imageContainerStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  background: imageSettings.value.bgColor || '#ffffff' // Ensure this also has white background
}))

// Watch localStorage for currentStep changes from HomeView
let checkInterval = null
onMounted(() => {
  currentStepValue.value = localStorage.getItem('currentStep') || '0'
  checkInterval = setInterval(() => {
    const newStepValue = localStorage.getItem('currentStep') || '0'
    if (newStepValue !== currentStepValue.value) {
      currentStepValue.value = newStepValue
    }
  }, 100)
})
onUnmounted(() => {
  if (checkInterval) clearInterval(checkInterval)
})

// Settings Dialog Logic
let count = 0
let delay = 3000
let interval = null
const goSetting = () => {
  count++
  if (interval) {
    if(count > 5) {
      clearInterval(interval); interval = null; count = 0; openDialog()
    }
  } else {
    interval = setInterval(() => { count = 0; clearInterval(interval); interval = null; }, delay)
  }
};
const settingDialog = ref(false)
const openDialog = () => {
  settingDialog.value = true
  // Load existing general settings
  switcher.value = localStorage.getItem('switch') || '0'
  current.value = localStorage.getItem('current') || '1'
  key.value = localStorage.getItem('key') || ''
  // Load image container settings
  imageSettings.value = {
    containerWidth: localStorage.getItem('imgContainerWidth') || '45%',
    padding: localStorage.getItem('imgPadding') || '0px',
    bgColor: localStorage.getItem('imgBgColor') || '#ffffff'
  }
  // Load specific page image paths
  loginImagePath.value = localStorage.getItem('loginImagePath') || '/pic/login.png'
  registrationCompleteImagePath.value = localStorage.getItem('registrationCompleteImagePath') || '/pic/ok.png'
  // Load specific page image styles
  loginPageImageStyle.value = JSON.parse(localStorage.getItem('loginPageImageStyle')) || { ...defaultIndImageStyle, fit: 'cover' }
  regCompletePageImageStyle.value = JSON.parse(localStorage.getItem('regCompletePageImageStyle')) || { ...defaultIndImageStyle, width: '80%', height: '80%' }
  // Load per-step image styles
  for (let i = 1; i <= 7; i++) {
    try {
      const savedSettings = localStorage.getItem(`imgSettings_step${i}`)
      perImageSettings.value[i] = savedSettings ? JSON.parse(savedSettings) : { ...defaultIndImageStyle }
    } catch (e) { perImageSettings.value[i] = { ...defaultIndImageStyle } }
  }
  // Load step mapping
  try {
    const savedMapping = localStorage.getItem('stepImageMapping')
    if (savedMapping) stepImageMapping.value = JSON.parse(savedMapping)
  } catch (e) { /* Use default */ }
}

watchEffect(() => { // Ensure key is only 0s and 1s
  key.value = key.value.replace(/[^01]/g, '');
})

// Reset all image-related settings to their defaults
const resetSettings = () => {
  imageSettings.value = { containerWidth: '45%', padding: '0px', bgColor: '#ffffff' }
  loginImagePath.value = '/pic/login.png'
  registrationCompleteImagePath.value = '/pic/ok.png'
  loginPageImageStyle.value = { ...defaultIndImageStyle, fit: 'cover' }
  regCompletePageImageStyle.value = { ...defaultIndImageStyle, width: '80%', height: '80%' }
  for (let i = 1; i <= 7; i++) {
    perImageSettings.value[i] = { ...defaultIndImageStyle }
  }
  stepImageMapping.value = { 0: 1, 1: 2, 2: 3, 3: 4, 4: 1, 5: 6, 6: 7 }
  saveSetting(true) // Pass true to indicate it's a reset operation
}

const saveSetting = (isResetting = false) => {
  localStorage.setItem('switch', switcher.value)
  localStorage.setItem('current', current.value)
  localStorage.setItem('key', key.value)
  
  localStorage.setItem('imgContainerWidth', imageSettings.value.containerWidth)
  localStorage.setItem('imgPadding', imageSettings.value.padding)
  localStorage.setItem('imgBgColor', imageSettings.value.bgColor)
  
  localStorage.setItem('loginImagePath', loginImagePath.value)
  localStorage.setItem('registrationCompleteImagePath', registrationCompleteImagePath.value)
  
  localStorage.setItem('loginPageImageStyle', JSON.stringify(loginPageImageStyle.value))
  localStorage.setItem('regCompletePageImageStyle', JSON.stringify(regCompletePageImageStyle.value))
  
  for (let i = 1; i <= 7; i++) {
    if (perImageSettings.value[i]) {
      localStorage.setItem(`imgSettings_step${i}`, JSON.stringify(perImageSettings.value[i]))
    }
  }
  
  // Only save mapping if not called from reset, to prevent potential race conditions
  // or overwriting with a stale state before full load in openDialog.
  // For a reset, stepImageMapping is already set to default in resetSettings.
  if (!isResetting) {
    localStorage.setItem('stepImageMapping', JSON.stringify(stepImageMapping.value))
  }
  
  currentStepValue.value = localStorage.getItem('currentStep') || '0' // Refresh current step
  settingDialog.value = false
}

// Watch route changes to potentially update currentStep or other logic if needed
watch(() => route.name, (newName) => {
  // This ensures that when navigating away from Login/Register, 
  // currentStepValue reflects the actual registration step if applicable.
  if (newName !== 'Login') {
    currentStepValue.value = localStorage.getItem('currentStep') || '0'
  }
  // Add any other route-specific logic here if necessary
});

</script>

<style lang="less" scoped>
* {
  box-sizing: border-box;
}

.main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 10px 4px rgba(0, 0, 0, 0.02);
  position: relative;
  padding: 20px;
}

.main-content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title-container {
  margin-bottom: 20px;
}

.aside {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: #ffffff; 

  @media (max-width: 768px) {
    display: none;
  }
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff; 
}

.dark .aside {
  background: #ffffff; 
}

.footer {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 15px;
  color: #606266;
  text-align: center;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 12px;

  .copyright {
    @media (max-height: 450px) {
      & {
        display: none;
      }
    }
  }

  .divider {
    height: 12px;
    width: 0;
    border-left: 1px solid #aaa;
  }

  a {
    transition: color 0.3s;
    cursor: pointer;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}

.dark .footer {
  color: #ccc;
}
</style>
