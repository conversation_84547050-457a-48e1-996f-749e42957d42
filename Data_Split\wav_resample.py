# -*- coding:utf-8 -*-
import scipy.io.wavfile as wav
from scipy.signal import resample

if __name__ == '__main__':


    # 指定原始音频文件路径和目标采样率
    input_file = "50_800_W.wav"
    output_file = "wav.txt"
    target_sampling_rate = 467

    # 读取原始音频文件
    original_sampling_rate, audio_data = wav.read(input_file)

    # 计算采样率的比例
    sampling_rate_ratio = target_sampling_rate / original_sampling_rate
    print("sampling_rate_ratio:", sampling_rate_ratio)

    # 计算目标音频数据的长度
    target_length = int(len(audio_data) * sampling_rate_ratio)

    # 执行音频重采样
    resampled_audio = resample(audio_data, target_length)
    print("resampled_audio:", resampled_audio)



    # # 将重采样后的音频保存为WAV文件
    # wav.write(output_file, target_sampling_rate, resampled_audio.astype('int16'))
