{"name": "electron-builder", "description": "A complete solution to package and build a ready for distribution Electron app for MacOS, Windows and Linux with “auto update” support out of the box", "version": "26.0.12", "main": "out/index.js", "files": ["out"], "bin": {"electron-builder": "./cli.js", "install-app-deps": "./install-app-deps.js"}, "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/electron-builder"}, "engines": {"node": ">=14.0.0"}, "keywords": ["electron", "builder", "build", "installer", "install", "packager", "pack", "nsis", "app", "dmg", "pkg", "msi", "exe", "setup", "Windows", "OS X", "MacOS", "<PERSON>", "appx", "snap", "flatpak", "portable"], "author": "<PERSON>", "contributors": ["<PERSON>", "<PERSON>"], "license": "MIT", "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "dependencies": {"chalk": "^4.1.2", "fs-extra": "^10.1.0", "is-ci": "^3.0.0", "lazy-val": "^1.0.5", "simple-update-notifier": "2.0.0", "yargs": "^17.6.2", "app-builder-lib": "26.0.12", "builder-util-runtime": "9.3.1", "dmg-builder": "26.0.12", "builder-util": "26.0.11"}, "devDependencies": {"@types/fs-extra": "9.0.13", "@types/is-ci": "3.0.0", "@types/yargs": "^17.0.16"}, "typings": "./out/index.d.ts", "publishConfig": {"tag": "next"}}