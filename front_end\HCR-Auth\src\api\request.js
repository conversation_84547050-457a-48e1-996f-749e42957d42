import axios from 'axios'

const host = '/api'

export const request = new axios.create({
  baseURL: host,
  timeout: 600000,
  headers: {
    'Content-Type': 'application/json'
  }
})

request.interceptors.request.use(
  (config) => {
    // do something before request is sent
    return config
  },
  (error) => {
    // do something with request error
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  (response) => {
    // do something with response data
    // console.log(response)
    return response
  },
  (error) => {
    // do something with response error
    return Promise.reject(error)
  }
)
