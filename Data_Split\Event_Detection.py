# -*- coding:utf-8 -*-
import scipy.io.wavfile as wav
from scipy.signal import resample
import numpy as np
from scipy.signal import butter, lfilter
import matplotlib.pyplot as plt

def zeroMeanNorm(data):
    mean = np.mean(data)
    std = np.std(data)
    for i in range(len(data)):
        data[i] = (data[i]-mean)/std
    return data

def read_date(txt_root):
    f = open(txt_root) #需要读取内容的文件路径, 如果路径中含有单\的话,需要在最前面添加r进行字符转义
    line = f.readline()
    data_list = []
    while line:
        data_split = line.split()
        temp = list(map(float, data_split))
        if(len(temp) == 0):
            pass
        else:
            data_list.append(temp)
        line = f.readline()
    f.close()

    data_matrix = np.asmatrix(data_list)
    # print("data_matrix:", data_matrix)
    dataX = data_matrix[:, 0]
    dataY = data_matrix[:, 1]
    dataZ = data_matrix[:, 2]

    dataX_ = np.matrix.tolist(dataX.T)
    dataX = dataX_[0]
    dataY_ = np.matrix.tolist(dataY.T)
    dataY = dataY_[0]
    dataZ_ = np.matrix.tolist(dataZ.T)
    dataZ = dataZ_[0]

    return dataX, dataY, dataZ

def high_Path_Filter(data, cutoff_freq, sampling_rate):
    # 定义高通滤波器参数
    filter_order = 4  # 滤波器阶数
    # 设计高通滤波器
    nyquist_freq = 0.5 * sampling_rate
    cutoff = cutoff_freq / nyquist_freq
    b, a = butter(filter_order, cutoff, btype='highpass')
    # 对数据向量应用高通滤波器
    filtered_data = lfilter(b, a, data)
    return filtered_data


def signal_event_detection(data_axis, template_axis, frameRate, cycleNum):

    # resampled_audio = template_axis
    # current_axis = data_axis
    # 零均值归一化
    data_axis = zeroMeanNorm(data_axis)

    #高通滤波
    data_axis = high_Path_Filter(data_axis, 20, frameRate)
    template_axis = high_Path_Filter(template_axis, 20, frameRate)

    # #计算交叉相关性
    # print("data_axis:", data_axis)
    # print("template_axis:", template_axis)

    cross_correlation = np.correlate(data_axis, template_axis, "valid") #计算相关系数，dataZ-resampled_audio+1
    cross_correlation = cross_correlation.tolist()

    #寻找一个最大值点,这个点应该是某个分段信号的起点
    max_index = cross_correlation.index(max(cross_correlation))
    half_index = max_index + frameRate #得到分段信号的中间一点
    search_start = half_index % (frameRate*2)
    if search_start - frameRate*2 < 0:
        search_start = 0
    # print(search_start)
    # print("search_start:", search_start)

    #寻找临近的周期中的最大值【周期是cycle个点】
    #标记每个周期的起始点
    cycle = frameRate * 2
    index = []
    for i in range(cycleNum):
        index.append(i*cycle + search_start) #让每个循环的起始点大致为上个信号的中间点
    # print("start_index:", index)
    #找到每个周期中最大值的下标
    list_max_index = []
    list_max_value = []
    for i in index:
        if i+cycle < len(cross_correlation):
            max_value = np.max(cross_correlation[i:i+cycle])
            # 额外步骤
            min_value = np.min(cross_correlation[i:i+cycle])
            if abs(max_value) >= abs(min_value):
                result = max_value
            else:
                result = min_value
            # result = max_value
            result_index = cross_correlation.index(result)
        elif i <= len(cross_correlation) <= i+cycle:
            break
            # max_value = np.max(cross_correlation[i:len(cross_correlation)])
            # # 额外步骤
            # min_value = np.min(cross_correlation[i:len(cross_correlation)])
            # if abs(max_value) >= abs(min_value):
            #     result = max_value
            # else:
            #     result = min_value
            # # result = max_value
            # result_index = cross_correlation.index(result)
        else:
            break
        if result_index+cycle <= len(data_axis):
            list_max_index.append(result_index) #获取对应的原始数据序列最大值起始下标
            list_max_value.append(result)

            # list_max_value.append(max_value)
    # print("list_max_index:", list_max_index)
    # print("list_max_value:", list_max_value)

    # 获得前50个最大数字的索引
    top_50_indices = sorted(range(len(list_max_value)), key=lambda i: abs(list_max_value[i]), reverse=True)[:50]
    # 根据索引获取第二个列表对应的值
    max_50_index_shuffle = [list_max_index[i] for i in top_50_indices]
    # 将对应的index按照原有顺序重新组织
    max_50_index_ordered = []
    for i in list_max_index:
        if i in max_50_index_shuffle:
            max_50_index_ordered.append(i)

    # 获得最大索引对应的值
    max_50_value_ordered = []
    for i in max_50_index_ordered:
        index = list_max_index.index(i)
        max_50_value_ordered.append(list_max_value[index])
    #作图
    # 接下来要把得到的点作在时序图上
    # 作总的交叉相关性图
    index = []
    for i in range(len(cross_correlation)):
        index.append(i)
    plt.plot(index, cross_correlation)
    # 标记特定的点
    plt.scatter(max_50_index_ordered, max_50_value_ordered, color='red', marker='o')
    plt.show()

    #返回data_axis中各Event对应的下标，共cycleNum个Event
    return max_50_index_ordered


if __name__ == '__main__':



    input_file = r"G:\HCR-Auth\code\Data_Split\50_800_W.wav"
    frameRate = 467
    # 读取原始音频文件
    original_sampling_rate, audio_data = wav.read(input_file)
    # 计算采样率的比例
    sampling_rate_ratio = frameRate / original_sampling_rate
    # 计算目标音频数据的长度
    target_length = int(len(audio_data) * sampling_rate_ratio)
    # 执行音频重采样
    resampled_audio = resample(audio_data, target_length)

    # 读取数据
    dataX, dataY, dataZ = read_date("L2.txt")
    current_axis = dataZ

    # 零均值归一化
    current_axis = zeroMeanNorm(current_axis)

    #高通滤波
    current_axis = high_Path_Filter(current_axis, 20, 467)
    resampled_audio = high_Path_Filter(resampled_audio, 20, 467)

    #计算交叉相关性
    cross_correlation = np.correlate(current_axis, resampled_audio, "valid") #计算相关系数，dataZ-resampled_audio+1
    cross_correlation = cross_correlation.tolist()

    #寻找一个最大值点,这个点应该是某个分段信号的起点
    max_index = cross_correlation.index(max(cross_correlation))
    half_index = max_index + frameRate #得到分段信号的中间一点
    search_start = half_index % (frameRate*2)
    print("search_start:", search_start)



    #寻找临近的周期中的最大值【周期是cycle个点】
    #标记每个周期的起始点
    cycle = frameRate * 2

    index = []
    for i in range(55):
        index.append(i*cycle + search_start) #让每个循环的起始点大致为上个信号的中间点
    #找到每个周期中最大值的下标
    list_max_index = []
    list_max_value = []
    for i in index:
        if i+cycle < len(cross_correlation):
            max_value = np.max(cross_correlation[i:i+cycle])
            max_index = cross_correlation.index(max_value)
        elif i <= len(cross_correlation) <= i+cycle:
            max_value = np.max(cross_correlation[i:len(cross_correlation)])
            max_index = cross_correlation.index(max_value)
        else:
            break
        if max_index+cycle <= len(current_axis):
            list_max_index.append(max_index) #获取对应的原始数据序列最大值起始下标
            list_max_value.append(max_value)
    print("list_max_index:", list_max_index)
    print("list_max_value:", list_max_value)


    # 获得前50个最大数字的索引
    top_50_indices = sorted(range(len(list_max_value)), key=lambda i: list_max_value[i], reverse=True)[:50]
    # 根据索引获取第二个列表对应的值
    max_50_index_shuffle = [list_max_index[i] for i in top_50_indices]
    # 将对应的index按照原有顺序重新组织
    max_50_index_ordered = []
    for i in list_max_index:
        if i in max_50_index_shuffle:
            max_50_index_ordered.append(i)
    print(max_50_index_ordered)


    # 接下来要把得到的点作在时序图上
    # 作总的交叉相关性图
    index = []
    for i in range(len(cross_correlation)):
        index.append(i)
    plt.plot(index, cross_correlation)
    # 标记特定的点
    plt.scatter(list_max_index, list_max_value, color='red', marker='o')
    plt.show()


