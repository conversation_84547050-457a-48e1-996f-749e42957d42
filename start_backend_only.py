#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
只启动后端服务的脚本
跳过前端检查，适用于环境检查有问题的情况
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("=" * 60)
    print("🦴 骨振识息系统 (HCR-Auth)")
    print("🚀 后端服务启动器")
    print("=" * 60)
    
    # 检查Python版本
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.8或更高版本")
        return 1
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    
    # 项目路径
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    if not backend_dir.exists():
        print(f"❌ 后端目录不存在: {backend_dir}")
        return 1
    
    # 添加后端目录到Python路径
    sys.path.insert(0, str(backend_dir))
    
    # 检查关键依赖
    print("\n📦 检查关键依赖...")
    required_modules = ['flask', 'numpy', 'scipy']
    missing = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module}")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  缺少依赖: {', '.join(missing)}")
        print("建议运行: pip install flask numpy scipy torch matplotlib")
        print("但系统仍会尝试启动...")
    
    try:
        # 切换到后端目录
        os.chdir(backend_dir)

        # 修复相对导入问题
        import sys
        if str(backend_dir) not in sys.path:
            sys.path.insert(0, str(backend_dir))

        # 导入并启动Flask应用
        import app
        from utils.config import Config
        
        print(f"\n🚀 启动后端服务...")
        print(f"📍 服务地址: http://localhost:{Config.PORT}")
        print("📋 API端点:")
        print(f"   - 创建用户: http://localhost:{Config.PORT}/api/create/")
        print(f"   - 测试API: 在另一个终端运行 'python backend/test_api.py'")
        print("\n💡 前端启动说明:")
        print("   1. 打开新的终端")
        print("   2. cd front_end/HCR-Auth")
        print("   3. npm install")
        print("   4. npm run dev")
        print("\n🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        app.app.run(
            host=Config.HOST,
            port=Config.PORT,
            debug=True,
            threaded=True
        )
        
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print("请安装必要的依赖:")
        print("pip install flask flask-cors numpy scipy torch matplotlib werkzeug")
        return 1
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
        sys.exit(0)
