var Module=typeof Module!=="undefined"?Module:{};!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.DecoderWorker=t():e.<PERSON>=t()}("undefined"!=typeof self?self:this,function(){return function(e){var t={};function r(s){if(t[s])return t[s].exports;var i=t[s]={i:s,l:!1,exports:{}};return e[s].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,s){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:s})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var s=Object.create(null);if(r.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(s,i,function(t){return e[t]}.bind(null,i));return s},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,r){null;(function(t){var r,s,i=new Promise(function(e){s=e});t.onmessage=function(e){i.then(function(){switch(e.data.command){case"decode":r&&r.decode(e.data.pages);break;case"done":r&&(r.sendLastBuffer(),t.close());break;case"init":r=new o(e.data,Module)}})};var o=function(e,t){if(!t)throw new Error("Module with exports required to initialize a decoder instance");this.mainReady=i,this.config=Object.assign({bufferLength:4096,decoderSampleRate:48e3,outputBufferSampleRate:48e3,resampleQuality:3},e),this._opus_decoder_create=t._opus_decoder_create,this._opus_decoder_destroy=t._opus_decoder_destroy,this._speex_resampler_process_interleaved_float=t._speex_resampler_process_interleaved_float,this._speex_resampler_init=t._speex_resampler_init,this._speex_resampler_destroy=t._speex_resampler_destroy,this._opus_decode_float=t._opus_decode_float,this._free=t._free,this._malloc=t._malloc,this.HEAPU8=t.HEAPU8,this.HEAP32=t.HEAP32,this.HEAPF32=t.HEAPF32,this.outputBuffers=[]};o.prototype.decode=function(e){var t=new DataView(e.buffer);this.getPageBoundaries(t).map(function(r){var s=t.getUint8(r+5,!0),i=t.getUint32(r+18,!0);if(2&s&&(this.numberOfChannels=t.getUint8(r+37,!0),this.init()),i>1){for(var o=t.getUint8(r+26,!0),u=r+27+o,n=0;n<o;n++){var f=t.getUint8(r+27+n,!0);if(this.decoderBuffer.set(e.subarray(u,u+=f),this.decoderBufferIndex),this.decoderBufferIndex+=f,f<255){var h=this._opus_decode_float(this.decoder,this.decoderBufferPointer,this.decoderBufferIndex,this.decoderOutputPointer,this.decoderOutputMaxLength,0),a=Math.ceil(h*this.config.outputBufferSampleRate/this.config.decoderSampleRate);this.HEAP32[this.decoderOutputLengthPointer>>2]=h,this.HEAP32[this.resampleOutputLengthPointer>>2]=a,this._speex_resampler_process_interleaved_float(this.resampler,this.decoderOutputPointer,this.decoderOutputLengthPointer,this.resampleOutputBufferPointer,this.resampleOutputLengthPointer),this.sendToOutputBuffers(this.HEAPF32.subarray(this.resampleOutputBufferPointer>>2,(this.resampleOutputBufferPointer>>2)+a*this.numberOfChannels)),this.decoderBufferIndex=0}}4&s&&this.sendLastBuffer()}},this)},o.prototype.getPageBoundaries=function(e){for(var t=[],r=0;r<e.byteLength-4;r++)1399285583==e.getUint32(r,!0)&&t.push(r);return t},o.prototype.init=function(){this.resetOutputBuffers(),this.initCodec(),this.initResampler()},o.prototype.initCodec=function(){this.decoder&&(this._opus_decoder_destroy(this.decoder),this._free(this.decoderBufferPointer),this._free(this.decoderOutputLengthPointer),this._free(this.decoderOutputPointer));var e=this._malloc(4);this.decoder=this._opus_decoder_create(this.config.decoderSampleRate,this.numberOfChannels,e),this._free(e),this.decoderBufferMaxLength=4e3,this.decoderBufferPointer=this._malloc(this.decoderBufferMaxLength),this.decoderBuffer=this.HEAPU8.subarray(this.decoderBufferPointer,this.decoderBufferPointer+this.decoderBufferMaxLength),this.decoderBufferIndex=0,this.decoderOutputLengthPointer=this._malloc(4),this.decoderOutputMaxLength=this.config.decoderSampleRate*this.numberOfChannels*120/1e3,this.decoderOutputPointer=this._malloc(4*this.decoderOutputMaxLength)},o.prototype.initResampler=function(){this.resampler&&(this._speex_resampler_destroy(this.resampler),this._free(this.resampleOutputLengthPointer),this._free(this.resampleOutputBufferPointer));var e=this._malloc(4);this.resampler=this._speex_resampler_init(this.numberOfChannels,this.config.decoderSampleRate,this.config.outputBufferSampleRate,this.config.resampleQuality,e),this._free(e),this.resampleOutputLengthPointer=this._malloc(4),this.resampleOutputMaxLength=Math.ceil(this.decoderOutputMaxLength*this.config.outputBufferSampleRate/this.config.decoderSampleRate),this.resampleOutputBufferPointer=this._malloc(4*this.resampleOutputMaxLength)},o.prototype.resetOutputBuffers=function(){this.outputBuffers=[],this.outputBufferArrayBuffers=[],this.outputBufferIndex=0;for(var e=0;e<this.numberOfChannels;e++)this.outputBuffers.push(new Float32Array(this.config.bufferLength)),this.outputBufferArrayBuffers.push(this.outputBuffers[e].buffer)},o.prototype.sendLastBuffer=function(){this.sendToOutputBuffers(new Float32Array((this.config.bufferLength-this.outputBufferIndex)*this.numberOfChannels)),t.postMessage(null)},o.prototype.sendToOutputBuffers=function(e){for(var r=0,s=e.length/this.numberOfChannels;r<s;){var i=Math.min(s-r,this.config.bufferLength-this.outputBufferIndex);if(1===this.numberOfChannels)this.outputBuffers[0].set(e.subarray(r,r+i),this.outputBufferIndex);else for(var o=0;o<i;o++)this.outputBuffers.forEach(function(t,s){t[this.outputBufferIndex+o]=e[(r+o)*this.numberOfChannels+s]},this);r+=i,this.outputBufferIndex+=i,this.outputBufferIndex==this.config.bufferLength&&(t.postMessage(this.outputBuffers,this.outputBufferArrayBuffers),this.resetOutputBuffers())}},Module||(Module={}),Module.mainReady=i,Module.OggOpusDecoder=o,Module.onRuntimeInitialized=s,e.exports=Module}).call(this,r(1))},function(e,t){var r;r=function(){return this}();try{r=r||new Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r}])});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=typeof window==="object";var ENVIRONMENT_IS_WORKER=typeof importScripts==="function";var ENVIRONMENT_IS_NODE=typeof process==="object"&&typeof process.versions==="object"&&typeof process.versions.node==="string";var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;function logExceptionOnExit(e){if(e instanceof ExitStatus)return;var toLog=e;err("exiting due to exception: "+toLog)}var nodeFS;var nodePath;if(ENVIRONMENT_IS_NODE){if(ENVIRONMENT_IS_WORKER){scriptDirectory=require("path").dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=function shell_read(filename,binary){if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);return nodeFS["readFileSync"](filename,binary?null:"utf8")};readBinary=function readBinary(filename){var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};readAsync=function readAsync(filename,onload,onerror){if(!nodeFS)nodeFS=require("fs");if(!nodePath)nodePath=require("path");filename=nodePath["normalize"](filename);nodeFS["readFile"](filename,function(err,data){if(err)onerror(err);else onload(data.buffer)})};if(process["argv"].length>1){thisProgram=process["argv"][1].replace(/\\/g,"/")}arguments_=process["argv"].slice(2);if(typeof module!=="undefined"){module["exports"]=Module}process["on"]("uncaughtException",function(ex){if(!(ex instanceof ExitStatus)){throw ex}});process["on"]("unhandledRejection",function(reason){throw reason});quit_=function(status,toThrow){if(keepRuntimeAlive()){process["exitCode"]=status;throw toThrow}logExceptionOnExit(toThrow);process["exit"](status)};Module["inspect"]=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;if(typeof WebAssembly!=="object"){abort("no native wasm support detected")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed: "+text)}}var UTF8Decoder=typeof TextDecoder!=="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str="";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module["HEAP8"]=HEAP8=new Int8Array(buf);Module["HEAP16"]=HEAP16=new Int16Array(buf);Module["HEAP32"]=HEAP32=new Int32Array(buf);Module["HEAPU8"]=HEAPU8=new Uint8Array(buf);Module["HEAPU16"]=HEAPU16=new Uint16Array(buf);Module["HEAPU32"]=HEAPU32=new Uint32Array(buf);Module["HEAPF32"]=HEAPF32=new Float32Array(buf);Module["HEAPF64"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module["preloadedImages"]={};Module["preloadedAudios"]={};function abort(what){{if(Module["onAbort"]){Module["onAbort"](what)}}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}function isFileURI(filename){return filename.startsWith("file://")}var wasmBinaryFile;wasmBinaryFile="decoderWorker.min.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw"both async and sync fetching of the wasm failed"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==="function"&&!isFileURI(wasmBinaryFile)){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"]){throw"failed to load wasm binary file at '"+wasmBinaryFile+"'"}return response["arrayBuffer"]()}).catch(function(){return getBinary(wasmBinaryFile)})}else{if(readAsync){return new Promise(function(resolve,reject){readAsync(wasmBinaryFile,function(response){resolve(new Uint8Array(response))},reject)})}}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={"a":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmMemory=Module["asm"]["g"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module["asm"]["q"];addOnInit(Module["asm"]["h"]);removeRunDependency("wasm-instantiate")}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming==="function"&&!isDataURI(wasmBinaryFile)&&!isFileURI(wasmBinaryFile)&&typeof fetch==="function"){return fetch(wasmBinaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module["instantiateWasm"]){try{var exports=Module["instantiateWasm"](info,receiveInstance);return exports}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync();return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback=="function"){callback(Module);continue}var func=callback.func;if(typeof func==="number"){if(callback.arg===undefined){wasmTable.get(func)()}else{wasmTable.get(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}function _abort(){abort("")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function abortOnCannotGrowMemory(requestedSize){abort("OOM")}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;abortOnCannotGrowMemory(requestedSize)}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _fd_close(fd){return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov+i*8>>2];var len=HEAP32[iov+(i*8+4)>>2];for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}var asmLibraryArg={"c":_abort,"e":_emscripten_memcpy_big,"f":_emscripten_resize_heap,"d":_fd_close,"b":_fd_seek,"a":_fd_write};var asm=createWasm();var ___wasm_call_ctors=Module["___wasm_call_ctors"]=function(){return(___wasm_call_ctors=Module["___wasm_call_ctors"]=Module["asm"]["h"]).apply(null,arguments)};var _opus_decoder_create=Module["_opus_decoder_create"]=function(){return(_opus_decoder_create=Module["_opus_decoder_create"]=Module["asm"]["i"]).apply(null,arguments)};var _opus_decode_float=Module["_opus_decode_float"]=function(){return(_opus_decode_float=Module["_opus_decode_float"]=Module["asm"]["j"]).apply(null,arguments)};var _opus_decoder_destroy=Module["_opus_decoder_destroy"]=function(){return(_opus_decoder_destroy=Module["_opus_decoder_destroy"]=Module["asm"]["k"]).apply(null,arguments)};var _speex_resampler_init=Module["_speex_resampler_init"]=function(){return(_speex_resampler_init=Module["_speex_resampler_init"]=Module["asm"]["l"]).apply(null,arguments)};var _speex_resampler_destroy=Module["_speex_resampler_destroy"]=function(){return(_speex_resampler_destroy=Module["_speex_resampler_destroy"]=Module["asm"]["m"]).apply(null,arguments)};var _speex_resampler_process_interleaved_float=Module["_speex_resampler_process_interleaved_float"]=function(){return(_speex_resampler_process_interleaved_float=Module["_speex_resampler_process_interleaved_float"]=Module["asm"]["n"]).apply(null,arguments)};var _malloc=Module["_malloc"]=function(){return(_malloc=Module["_malloc"]=Module["asm"]["o"]).apply(null,arguments)};var _free=Module["_free"]=function(){return(_free=Module["_free"]=Module["asm"]["p"]).apply(null,arguments)};var calledRun;function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}Module["run"]=run;if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();
