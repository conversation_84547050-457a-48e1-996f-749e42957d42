#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HCR-Auth 系统启动脚本
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# 项目路径
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "front_end" / "HCR-Auth"

# 进程列表
processes = []

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    print("\n🛑 正在停止所有服务...")
    
    for process in processes:
        if process.poll() is None:  # 进程仍在运行
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    
    print("✅ 所有服务已停止")
    sys.exit(0)

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    try:
        # 检查Python依赖
        requirements_file = BACKEND_DIR / "requirements.txt"
        if requirements_file.exists():
            print("📦 检查Python依赖...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], capture_output=True, text=True, cwd=BACKEND_DIR)
            
            if result.returncode != 0:
                print(f"⚠️  依赖安装警告: {result.stderr}")
        
        # 启动后端
        backend_process = subprocess.Popen([
            sys.executable, "run.py"
        ], cwd=BACKEND_DIR)
        
        processes.append(backend_process)
        print("✅ 后端服务启动成功 (http://localhost:5000)")
        
        return backend_process
        
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🚀 启动前端服务...")
    
    try:
        # 检查Node.js依赖
        package_json = FRONTEND_DIR / "package.json"
        node_modules = FRONTEND_DIR / "node_modules"
        
        if package_json.exists() and not node_modules.exists():
            print("📦 安装Node.js依赖...")
            install_process = subprocess.run([
                "npm", "install"
            ], cwd=FRONTEND_DIR)
            
            if install_process.returncode != 0:
                print("❌ 前端依赖安装失败")
                return None
        
        # 启动前端开发服务器
        frontend_process = subprocess.Popen([
            "npm", "run", "dev"
        ], cwd=FRONTEND_DIR)
        
        processes.append(frontend_process)
        print("✅ 前端服务启动成功 (http://localhost:5173)")
        
        return frontend_process
        
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def check_dependencies():
    """检查系统依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print("❌ 需要Python 3.8或更高版本")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except Exception as e:
        print(f"❌ Python检查失败: {e}")
        return False
    
    # 检查Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            node_version = result.stdout.strip()
            print(f"✅ Node.js {node_version}")
        else:
            print("❌ 未找到Node.js，请安装Node.js 16+")
            return False
    except Exception as e:
        print("❌ 未找到Node.js，请安装Node.js 16+")
        return False
    
    # 检查npm
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            npm_version = result.stdout.strip()
            print(f"✅ npm {npm_version}")
        else:
            print("❌ 未找到npm")
            return False
    except Exception as e:
        print("❌ 未找到npm")
        return False
    
    return True

def wait_for_services():
    """等待服务启动"""
    print("⏳ 等待服务启动...")
    time.sleep(5)
    
    # 检查后端服务
    try:
        import requests
        response = requests.get("http://localhost:5000/api/create/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务运行正常")
        else:
            print("⚠️  后端服务可能未完全启动")
    except Exception as e:
        print("⚠️  无法连接到后端服务")

def main():
    """主函数"""
    print("=" * 60)
    print("🦴 骨振识息系统 (HCR-Auth)")
    print("🚀 系统启动器")
    print("=" * 60)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装必要的依赖")
        sys.exit(1)
    
    print()
    
    # 启动后端
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端启动失败")
        sys.exit(1)
    
    # 等待后端启动
    time.sleep(3)
    
    # 启动前端
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 前端启动失败")
        backend_process.terminate()
        sys.exit(1)
    
    # 等待服务完全启动
    wait_for_services()
    
    print()
    print("=" * 60)
    print("🎉 系统启动完成!")
    print("📍 前端地址: http://localhost:5173")
    print("📍 后端地址: http://localhost:5000")
    print("💡 按 Ctrl+C 停止所有服务")
    print("=" * 60)
    
    # 等待进程结束
    try:
        while True:
            # 检查进程状态
            for process in processes:
                if process.poll() is not None:
                    print(f"⚠️  进程意外退出: {process.pid}")
            
            time.sleep(1)
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)

if __name__ == "__main__":
    main()
