const { app, BrowserWindow, protocol } = require('electron')
const path = require('node:path')
const fs = require('node:fs')

// 判断是否为开发环境 - 在打包后的应用中强制为 false
const isDev = process.env.NODE_ENV !== 'production' && !app.isPackaged

// 在应用启动前注册自定义协议
if (!isDev) {
  protocol.registerSchemesAsPrivileged([
    {
      scheme: 'app',
      privileges: {
        secure: true,
        standard: true,
        supportFetchAPI: true,
        corsEnabled: true
      }
    }
  ])
}

function createWindow() {
  // 创建浏览器窗口
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    title: '骨振识息',
    webPreferences: {
      // 禁用 web 安全, 修复白屏
      webSecurity: false,
      preload: path.join(__dirname, 'preload.cjs')
    }
  })

  // 强制显示调试信息
  console.log('isDev:', isDev)
  console.log('NODE_ENV:', process.env.NODE_ENV)
  console.log('app.isPackaged:', app.isPackaged)

  if (isDev) {
    win.loadURL('http://localhost:5173')
    win.webContents.openDevTools()
  } else {
    // 使用自定义协议加载应用
    win.loadURL('app://./index.html')
  }
}

// Electron 应用准备就绪后创建窗口
app.whenReady().then(() => {
  if (!isDev) {
    // 注册自定义协议处理器
    protocol.registerFileProtocol('app', (request, callback) => {
      const url = request.url.slice('app://./'.length)
      console.log('Protocol request for:', url)
      
      // 确定文件路径
      let filePath
      
      if (url.startsWith('pic/')) {
        // 处理 pic 目录中的文件
        filePath = path.join(__dirname, '../', url)
      } else {
        // 处理 dist 目录中的文件
        filePath = path.join(__dirname, '../dist', url)
      }
      
      console.log('Resolved file path:', filePath)
      
      // 检查文件是否存在
      if (fs.existsSync(filePath)) {
        console.log('File found:', filePath)
        callback({ path: filePath })
      } else {
        console.log('File not found:', filePath)
        callback({ error: -6 }) // ERR_FILE_NOT_FOUND
      }
    })
  }
  
  createWindow()
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// 当所有窗口都关闭时退出应用（macOS 除外）
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
}) 