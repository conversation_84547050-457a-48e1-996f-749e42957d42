# -*- coding:utf-8 -*-
from matplotlib.mlab import psd, csd
import numpy as np
import scipy.io.wavfile as wav
from scipy import signal
from scipy.signal import resample
import matplotlib.pyplot as plt
from scipy.signal import butter, lfilter

from get_Corr import getRangeFreqFFT, getSegCorr
from get_TransFunction import zeroMeanNorm, read_date, calcuTransFunction


def high_Path_Filter(data, cutoff_freq, sampling_rate):
    # 定义高通滤波器参数
    filter_order = 4  # 滤波器阶数
    # 设计高通滤波器
    nyquist_freq = 0.5 * sampling_rate
    cutoff = cutoff_freq / nyquist_freq
    b, a = butter(filter_order, cutoff, btype='highpass')
    # 对数据向量应用高通滤波器
    filtered_data = lfilter(b, a, data)
    return filtered_data


def get_Feature(path1, path2, resampleAuido, frameRate):

    # 获得两个数据序列
    dataX, dataY, dataZ = read_date(path1)
    dataX_, dataY_, dataZ_ = read_date(path2)
    print("path1:", path1)
    print("path2:", path2)

    # 零均值归一化
    dataX = zeroMeanNorm(dataX)
    dataY = zeroMeanNorm(dataY)
    dataZ = zeroMeanNorm(dataZ)
    dataX_ = zeroMeanNorm(dataX_)
    dataY_ = zeroMeanNorm(dataY_)
    dataZ_ = zeroMeanNorm(dataZ_)
    resampled_audio = zeroMeanNorm(resampleAuido)

    threshFrequency = 20

    # 噪声处理，20Hz高通滤波
    dataX = high_Path_Filter(dataX, threshFrequency, frameRate)
    dataY = high_Path_Filter(dataY, threshFrequency, frameRate)
    dataZ = high_Path_Filter(dataZ, threshFrequency, frameRate)
    dataX_ = high_Path_Filter(dataX_, threshFrequency, frameRate)
    dataY_ = high_Path_Filter(dataY_, threshFrequency, frameRate)
    dataZ_ = high_Path_Filter(dataZ_, threshFrequency, frameRate)
    resampled_audio = high_Path_Filter(resampled_audio, threshFrequency, frameRate)

    # print(len(resampled_audio), len(dataX))

    # 特征计算
    # 转换函数
    fre, tfX = calcuTransFunction(resampled_audio, dataX, frameRate, threshFrequency)
    fre2, tfY = calcuTransFunction(resampled_audio, dataY, frameRate, threshFrequency)
    fre3, tfZ = calcuTransFunction(resampled_audio, dataZ, frameRate, threshFrequency)
    fre4, tfX_ = calcuTransFunction(resampled_audio, dataX_, frameRate, threshFrequency)
    fre5, tfY_ = calcuTransFunction(resampled_audio, dataY_, frameRate, threshFrequency)
    fre6, tfZ_ = calcuTransFunction(resampled_audio, dataZ_, frameRate, threshFrequency)
    # print("fre:", fre)

    # 分段相关性
    corrX = getSegCorr(dataX, dataX_, frameRate)
    corrY = getSegCorr(dataY, dataY_, frameRate)
    corrZ = getSegCorr(dataZ, dataZ_, frameRate)


    print("len(tfX), len(tfY), len(tfZ), len(tfX_), len(tfY_), len(tfZ_), len(corrX), len(corrY), len(corrZ):",
          len(tfX), len(tfY), len(tfZ), len(tfX_), len(tfY_), len(tfZ_), len(corrX), len(corrY), len(corrZ))

    tfL = tfX + tfY + tfZ
    tfR = tfX_ + tfY_ + tfZ_
    corr = corrX + corrY + corrZ


    return tfL, tfR, corr

if __name__ == '__main__':

    input_file = "50_800_W.wav"
    # frameRate = 467
    frameRate = 467
    # 读取原始音频文件
    original_sampling_rate, audio_data = wav.read(input_file)
    # 计算采样率的比例
    sampling_rate_ratio = frameRate / original_sampling_rate
    # 计算目标音频数据的长度
    target_length = int(len(audio_data) * sampling_rate_ratio)
    # 执行音频重采样
    resampled_audio = resample(audio_data, target_length)

    tfL, tfR, corr = get_Feature("L_1.txt", "R_1.txt", resampled_audio, frameRate)
    print("len(tfL):", len(tfL))
    print("len(tfR):", len(tfR))
    print("len(corr):", len(corr))

    # # 获得两个数据序列
    # dataX, dataY, dataZ = read_date("L_1.txt")
    # dataX_, dataY_, dataZ_ = read_date("R_1.txt")
    #
    # #零均值归一化
    # dataX = zeroMeanNorm(dataX)
    # dataY = zeroMeanNorm(dataY)
    # dataZ = zeroMeanNorm(dataZ)
    # dataX_ = zeroMeanNorm(dataX_)
    # dataY_ = zeroMeanNorm(dataY_)
    # dataZ_ = zeroMeanNorm(dataZ_)
    # resampled_audio = zeroMeanNorm(resampled_audio)
    #
    # #噪声处理，20Hz高通滤波
    # dataX = high_Path_Filter(dataX, 20, frameRate)
    # dataY = high_Path_Filter(dataY, 20, frameRate)
    # dataZ = high_Path_Filter(dataZ, 20, frameRate)
    # dataX_ = high_Path_Filter(dataX_, 20, frameRate)
    # dataY_ = high_Path_Filter(dataY_, 20, frameRate)
    # dataZ_ = high_Path_Filter(dataZ_, 20, frameRate)
    # resampled_audio = high_Path_Filter(resampled_audio, 20, frameRate)
    #
    # #特征计算
    # #转换函数
    # fre, tfX = calcuTransFunction(resampled_audio, dataX, frameRate)
    # fre2, tfY = calcuTransFunction(resampled_audio, dataY, frameRate)
    # fre3, tfZ = calcuTransFunction(resampled_audio, dataZ, frameRate)
    # fre4, tfX_ = calcuTransFunction(resampled_audio, dataX_, frameRate)
    # fre5, tfY_ = calcuTransFunction(resampled_audio, dataY_, frameRate)
    # fre6, tfZ_ = calcuTransFunction(resampled_audio, dataZ_, frameRate)
    #
    # #分段相关性
    # corrX = getSegCorr(dataX, dataX_, frameRate)
    # corrY = getSegCorr(dataY, dataY_, frameRate)
    # corrZ = getSegCorr(dataZ, dataZ_, frameRate)
    #
    # tfL = tfX + tfY + tfZ
    # tfR = tfX_ + tfY_ + tfZ_
    # corr = corrX + corrY + corrZ
    #
    # print("len(tfX):", len(tfX))
    # print("len(corrX):", len(corrX))


