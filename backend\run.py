#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HCR-Auth Backend Server Launcher
骨振识息系统后端服务启动器
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import app
from utils.config import Config

if __name__ == '__main__':
    # 确保必要的目录存在
    Config.ensure_directories()
    
    print("=" * 60)
    print("🦴 骨振识息系统后端服务")
    print("🚀 HCR-Auth Backend Server")
    print("=" * 60)
    print(f"📍 服务地址: http://{Config.HOST}:{Config.PORT}")
    print(f"🔧 调试模式: {'开启' if Config.DEBUG else '关闭'}")
    print(f"📁 上传目录: {Config.UPLOAD_FOLDER}")
    print(f"🤖 模型目录: {Config.MODEL_FOLDER}")
    print("=" * 60)
    print("✅ 服务启动中...")
    
    try:
        app.run(
            host=Config.HOST,
            port=Config.PORT,
            debug=Config.DEBUG,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        sys.exit(1)
