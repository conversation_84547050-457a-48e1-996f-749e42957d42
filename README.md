# 骨振识息系统 (HCR-Auth)

基于骨传导振动数据的生物特征认证系统，通过加速度计采集振动数据，使用孪生神经网络进行身份识别。

## 系统架构

```
前端 (Vue.js) ←→ 后端 (Flask) ←→ 数据处理 ←→ 孪生神经网络
     ↓                ↓              ↓            ↓
   用户界面        API接口        信号分割      特征提取
   音频录制        文件管理        事件检测      模型推理
```

## 功能特性

- 🎯 **用户注册**: 多步骤数据采集，建立用户生物特征模板
- 🔐 **身份认证**: 基于孪生神经网络的快速身份验证
- 📊 **信号处理**: 自动信号分割、特征提取和噪声过滤
- 🧠 **深度学习**: 使用预训练的孪生神经网络进行特征匹配
- 💻 **跨平台**: 支持Web和Electron桌面应用

## 技术栈

### 前端
- Vue.js 3 + Element Plus
- Vite 构建工具
- Electron (桌面应用)
- Web Audio API (音频录制)

### 后端
- Flask (Python Web框架)
- PyTorch (深度学习)
- NumPy/SciPy (科学计算)
- 信号处理算法

## 快速开始

### 环境要求

- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 方法一：一键启动 (推荐)

```bash
# 1. 检查系统环境
python check_system.py

# 2. 启动完整系统
python start_system.py

# Windows用户也可以双击
start_system.bat
```

### 方法二：手动启动

#### 1. 后端部署

```bash
# 进入后端目录
cd backend

# 安装Python依赖
pip install -r requirements.txt

# 启动后端服务
python run.py
```

后端服务将在 `http://localhost:5000` 启动

#### 2. 前端部署

```bash
# 进入前端目录
cd front_end/HCR-Auth

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 `http://localhost:5173` 启动

#### 3. 桌面应用 (可选)

```bash
# 在前端目录中
npm run electron:dev
```

### 测试API

```bash
# 测试后端API接口
cd backend
python test_api.py
```

## 使用说明

### 用户注册流程

1. **访问注册页面**: 点击"注册账号"
2. **佩戴设备**: 正确佩戴骨传导耳机
3. **数据采集**: 按照7个步骤进行数据采集
   - 步骤1: 保持不动
   - 步骤2: 摇晃头部
   - 步骤3: 晃动身体
   - 步骤4: 保持坐姿
   - 步骤5: 保持站立
   - 步骤6: 保持行走
   - 步骤7: 持续跑动
4. **处理完成**: 系统自动处理数据并生成用户ID

### 身份验证流程

1. **访问登录页面**: 输入用户ID
2. **佩戴设备**: 正确佩戴骨传导耳机
3. **开始验证**: 点击"开始采集"按钮
4. **等待结果**: 系统分析数据并返回验证结果

## API 接口文档

### 用户管理

- `GET /api/create/` - 创建新用户
- `GET /api/status/{user_id}/` - 获取注册状态

### 文件上传

- `POST /api/upload/{user_id}/{data_type}/` - 上传振动数据文件

### 注册流程

- `GET /api/register/{user_id}/` - 开始注册处理

### 身份验证

- `POST /api/login/{user_id}/` - 用户登录验证
- `GET /api/login/{user_id}/status/{status_id}/` - 获取验证状态

## 目录结构

```
HCR/
├── backend/                 # 后端代码
│   ├── app.py              # Flask应用主文件
│   ├── run.py              # 启动脚本
│   ├── requirements.txt    # Python依赖
│   ├── core/               # 核心模块
│   │   ├── data_processor.py      # 数据处理
│   │   ├── model_inference.py     # 模型推理
│   │   ├── user_manager.py        # 用户管理
│   │   └── signal_processing.py   # 信号处理
│   └── utils/              # 工具模块
│       ├── config.py       # 配置文件
│       └── logger.py       # 日志工具
├── front_end/              # 前端代码
│   └── HCR-Auth/          # Vue.js应用
├── Data_Split/             # 信号分割算法
├── Feature_Extraction/     # 特征提取算法
├── preTrain_Hyperparameter_Selection/  # 预训练模型
└── get_IncrementalParameter/           # 增量学习
```

## 配置说明

### 后端配置 (backend/utils/config.py)

```python
# 服务器配置
HOST = '127.0.0.1'
PORT = 5000
DEBUG = True

# 数据处理参数
FRAME_RATE = 467
THRESHOLD_FREQUENCY = 20
SIAMESE_THRESHOLD = 0.5
```

### 前端配置 (front_end/HCR-Auth/vite.config.js)

```javascript
proxy: {
  '/api': {
    target: 'http://localhost:5000',
    changeOrigin: true
  }
}
```

## 故障排除

### 常见问题

1. **后端启动失败**
   - 检查Python版本和依赖安装
   - 确保端口5000未被占用

2. **前端无法连接后端**
   - 确认后端服务正常运行
   - 检查代理配置是否正确

3. **模型推理失败**
   - 检查预训练模型文件是否存在
   - 确认PyTorch安装正确

4. **文件上传失败**
   - 检查文件格式 (支持.wav, .txt)
   - 确认文件大小不超过16MB

### 日志查看

- 后端日志: `backend/logs/`
- 前端控制台: 浏览器开发者工具

## 开发指南

### 添加新的数据类型

1. 在 `DataProcessor` 中添加处理逻辑
2. 更新前端的数据类型选项
3. 修改API接口支持新类型

### 模型优化

1. 调整网络结构 (`model_inference.py`)
2. 修改特征提取算法 (`signal_processing.py`)
3. 更新配置参数 (`config.py`)

## 许可证

本项目仅供学术研究使用。

## 联系方式

如有问题或建议，请联系开发团队。
