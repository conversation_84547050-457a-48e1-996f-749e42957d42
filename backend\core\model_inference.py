# -*- coding: utf-8 -*-
"""
模型推理模块
基于孪生神经网络进行身份认证
"""

import os
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import pickle

from ..utils.config import Config
from ..utils.logger import setup_logger

logger = setup_logger()

class EmbeddingNet(nn.Module):
    """嵌入网络，基于原有的networks.py"""
    
    def __init__(self, input_dim=172, dropout_rate=0.2):
        super(EmbeddingNet, self).__init__()
        
        # 由于输入是1D特征向量，我们使用全连接层而不是卷积层
        self.network = nn.Sequential(
            nn.Linear(input_dim, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(p=dropout_rate),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(p=dropout_rate),
            
            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(p=dropout_rate),
            
            nn.Linear(64, 32)  # 输出32维嵌入向量
        )
    
    def forward(self, x):
        # 确保输入是2D张量 (batch_size, features)
        if len(x.shape) == 1:
            x = x.unsqueeze(0)
        
        output = self.network(x)
        return output


class SiameseNet(nn.Module):
    """孪生网络，基于原有的networks.py"""
    
    def __init__(self, embedding_net):
        super(SiameseNet, self).__init__()
        self.embedding_net = embedding_net
    
    def forward(self, x1, x2):
        output1 = self.embedding_net(x1)
        output2 = self.embedding_net(x2)
        return output1, output2
    
    def get_embedding(self, x):
        return self.embedding_net(x)


class ModelInference:
    """模型推理类"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.threshold = Config.SIAMESE_THRESHOLD
        
        # 初始化基础模型
        self.base_embedding_net = EmbeddingNet(
            input_dim=172,
            dropout_rate=Config.DROPOUT_RATE
        )
        self.base_model = SiameseNet(self.base_embedding_net)
        
        # 尝试加载预训练模型
        self._load_pretrained_model()
        
        self.base_model.to(self.device)
        self.base_model.eval()
    
    def _load_pretrained_model(self):
        """加载预训练模型"""
        try:
            # 查找预训练模型文件
            pretrained_paths = [
                Config.BASE_DIR.parent / "preTrain_Hyperparameter_Selection" / "embedding_net_drop_0.pth",
                Config.MODEL_FOLDER / "pretrained_model.pth",
                Config.BASE_DIR / "models" / "pretrained_model.pth"
            ]
            
            for model_path in pretrained_paths:
                if model_path.exists():
                    logger.info(f"Loading pretrained model: {model_path}")
                    
                    # 加载模型状态
                    state_dict = torch.load(str(model_path), map_location=self.device)
                    
                    # 尝试加载到嵌入网络
                    try:
                        self.base_embedding_net.load_state_dict(state_dict)
                        logger.info("Successfully loaded pretrained embedding network")
                        return
                    except:
                        # 如果直接加载失败，尝试从孪生网络中提取嵌入网络
                        try:
                            embedding_state = {}
                            for key, value in state_dict.items():
                                if key.startswith('embedding_net.'):
                                    new_key = key.replace('embedding_net.', '')
                                    embedding_state[new_key] = value
                            
                            if embedding_state:
                                self.base_embedding_net.load_state_dict(embedding_state)
                                logger.info("Successfully loaded pretrained embedding network from siamese model")
                                return
                        except Exception as e:
                            logger.warning(f"Failed to load pretrained model from {model_path}: {e}")
                            continue
            
            logger.warning("No pretrained model found, using randomly initialized model")
            
        except Exception as e:
            logger.error(f"Error loading pretrained model: {e}")
    
    def train_user_model(self, user_id, features):
        """为用户训练个性化模型"""
        try:
            logger.info(f"Training model for user {user_id}")
            
            # 在实际应用中，这里应该实现增量学习或微调
            # 目前我们简化为保存用户的特征模板
            
            # 确保特征是numpy数组
            if isinstance(features, list):
                features = np.array(features)
            
            # 如果有多个特征样本，计算平均值作为用户模板
            if len(features.shape) > 1:
                user_template = np.mean(features, axis=0)
            else:
                user_template = features
            
            # 使用基础模型提取嵌入向量
            with torch.no_grad():
                feature_tensor = torch.FloatTensor(user_template).to(self.device)
                if len(feature_tensor.shape) == 1:
                    feature_tensor = feature_tensor.unsqueeze(0)
                
                user_embedding = self.base_model.get_embedding(feature_tensor)
                user_embedding = user_embedding.cpu().numpy()
            
            # 保存用户模型
            model_path = self._save_user_model(user_id, user_template, user_embedding)
            
            logger.info(f"User model trained and saved: {model_path}")
            return model_path
            
        except Exception as e:
            logger.error(f"Error training user model: {e}")
            raise
    
    def _save_user_model(self, user_id, user_template, user_embedding):
        """保存用户模型"""
        try:
            # 创建用户模型目录
            user_model_dir = Config.MODEL_FOLDER / user_id
            os.makedirs(user_model_dir, exist_ok=True)
            
            # 保存用户数据
            user_data = {
                'user_id': user_id,
                'template': user_template,
                'embedding': user_embedding,
                'model_version': '1.0'
            }
            
            model_path = user_model_dir / 'user_model.pkl'
            with open(model_path, 'wb') as f:
                pickle.dump(user_data, f)
            
            return str(model_path)
            
        except Exception as e:
            logger.error(f"Error saving user model: {e}")
            raise
    
    def _load_user_model(self, user_id):
        """加载用户模型"""
        try:
            model_path = Config.MODEL_FOLDER / user_id / 'user_model.pkl'
            
            if not model_path.exists():
                logger.warning(f"User model not found: {model_path}")
                return None
            
            with open(model_path, 'rb') as f:
                user_data = pickle.load(f)
            
            logger.info(f"Loaded user model for {user_id}")
            return user_data
            
        except Exception as e:
            logger.error(f"Error loading user model for {user_id}: {e}")
            return None
    
    def verify_user(self, user_id, test_features):
        """验证用户身份"""
        try:
            logger.info(f"Verifying user {user_id}")
            
            # 加载用户模型
            user_data = self._load_user_model(user_id)
            if user_data is None:
                logger.warning(f"User model not found for {user_id}")
                return False
            
            # 获取用户模板嵌入向量
            user_embedding = user_data['embedding']
            
            # 计算测试特征的嵌入向量
            with torch.no_grad():
                test_tensor = torch.FloatTensor(test_features).to(self.device)
                if len(test_tensor.shape) == 1:
                    test_tensor = test_tensor.unsqueeze(0)
                
                test_embedding = self.base_model.get_embedding(test_tensor)
                test_embedding = test_embedding.cpu().numpy()
            
            # 计算相似度（欧氏距离）
            distance = np.linalg.norm(user_embedding - test_embedding)
            
            # 判断是否通过验证
            is_verified = distance < self.threshold
            
            logger.info(f"User {user_id} verification: distance={distance:.4f}, threshold={self.threshold}, result={is_verified}")
            
            return is_verified
            
        except Exception as e:
            logger.error(f"Error verifying user {user_id}: {e}")
            return False
    
    def get_user_similarity(self, user_id, test_features):
        """获取用户相似度分数"""
        try:
            # 加载用户模型
            user_data = self._load_user_model(user_id)
            if user_data is None:
                return 0.0
            
            # 获取用户模板嵌入向量
            user_embedding = user_data['embedding']
            
            # 计算测试特征的嵌入向量
            with torch.no_grad():
                test_tensor = torch.FloatTensor(test_features).to(self.device)
                if len(test_tensor.shape) == 1:
                    test_tensor = test_tensor.unsqueeze(0)
                
                test_embedding = self.base_model.get_embedding(test_tensor)
                test_embedding = test_embedding.cpu().numpy()
            
            # 计算余弦相似度
            dot_product = np.dot(user_embedding.flatten(), test_embedding.flatten())
            norm_user = np.linalg.norm(user_embedding)
            norm_test = np.linalg.norm(test_embedding)
            
            if norm_user == 0 or norm_test == 0:
                return 0.0
            
            similarity = dot_product / (norm_user * norm_test)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Error calculating similarity for user {user_id}: {e}")
            return 0.0
