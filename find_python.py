#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查找系统中的Python安装
"""

import sys
import os
import subprocess
from pathlib import Path

def find_python_installations():
    """查找系统中的Python安装"""
    print("🔍 查找Python安装...")
    print("=" * 50)
    
    # 当前Python信息
    print(f"当前Python路径: {sys.executable}")
    print(f"当前Python版本: {sys.version}")
    print(f"是否conda环境: {'conda' in sys.executable.lower() or 'anaconda' in sys.executable.lower() or 'miniconda' in sys.executable.lower()}")
    print()
    
    # 查找所有Python安装
    python_paths = []
    
    # 常见的Python安装位置
    common_paths = [
        r"C:\Python*\python.exe",
        r"C:\Program Files\Python*\python.exe", 
        r"C:\Program Files (x86)\Python*\python.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\Python\Python*\python.exe",
        r"C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe"
    ]
    
    # 使用where命令查找
    try:
        result = subprocess.run(['where', 'python'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            paths = result.stdout.strip().split('\n')
            for path in paths:
                if path.strip():
                    python_paths.append(path.strip())
    except:
        pass
    
    # 手动搜索常见位置
    import glob
    for pattern in common_paths:
        try:
            matches = glob.glob(pattern)
            python_paths.extend(matches)
        except:
            pass
    
    # 去重并验证
    valid_pythons = []
    seen = set()
    
    for path in python_paths:
        if path not in seen and os.path.exists(path):
            seen.add(path)
            try:
                # 测试Python是否可用
                result = subprocess.run([path, '--version'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    version = result.stdout.strip() or result.stderr.strip()
                    is_conda = 'conda' in path.lower() or 'anaconda' in path.lower() or 'miniconda' in path.lower()
                    valid_pythons.append({
                        'path': path,
                        'version': version,
                        'is_conda': is_conda
                    })
            except:
                pass
    
    # 显示结果
    print("找到的Python安装:")
    print("-" * 50)
    
    if not valid_pythons:
        print("❌ 未找到有效的Python安装")
        return None
    
    system_python = None
    for i, python in enumerate(valid_pythons, 1):
        conda_mark = "🐍 (Conda)" if python['is_conda'] else "🐍 (系统)"
        print(f"{i}. {conda_mark}")
        print(f"   路径: {python['path']}")
        print(f"   版本: {python['version']}")
        print()
        
        if not python['is_conda'] and system_python is None:
            system_python = python['path']
    
    # 推荐使用的Python
    if system_python:
        print(f"✅ 推荐使用系统Python: {system_python}")
        print(f"启动命令: {system_python} simple_start.py")
    else:
        print("⚠️  建议安装系统Python: https://www.python.org/downloads/")
    
    return system_python

if __name__ == "__main__":
    print("🔍 Python环境检测工具")
    print("=" * 50)
    find_python_installations()
    
    print("\n" + "=" * 50)
    print("💡 解决方案:")
    print("1. 关闭当前终端，重新打开")
    print("2. 使用推荐的Python路径运行项目")
    print("3. 或者安装新的系统Python")
    input("\n按回车键退出...")
