#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API测试脚本
用于测试后端API接口的基本功能
"""

import requests
import json
import time
import os
from pathlib import Path

# 配置
BASE_URL = "http://localhost:5000/api"
TEST_DATA_DIR = Path(__file__).parent / "test_data"

def create_test_data():
    """创建测试数据文件"""
    os.makedirs(TEST_DATA_DIR, exist_ok=True)
    
    # 创建一个简单的测试音频文件（模拟）
    test_file = TEST_DATA_DIR / "test_audio.wav"
    if not test_file.exists():
        # 创建一个简单的二进制文件作为测试
        with open(test_file, 'wb') as f:
            f.write(b'RIFF' + b'\x00' * 100)  # 简单的WAV文件头
    
    return test_file

def test_create_user():
    """测试创建用户"""
    print("🧪 测试创建用户...")
    
    try:
        response = requests.get(f"{BASE_URL}/create/")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                user_id = data['data']['id']
                print(f"✅ 用户创建成功: {user_id}")
                return user_id
            else:
                print(f"❌ 用户创建失败: {data['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return None

def test_upload_file(user_id, test_file):
    """测试文件上传"""
    print("🧪 测试文件上传...")
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('test_audio.wav', f, 'audio/wav')}
            response = requests.post(
                f"{BASE_URL}/upload/{user_id}/chair/",
                files=files
            )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 文件上传成功: {data['data']['file_path']}")
                return True
            else:
                print(f"❌ 文件上传失败: {data['message']}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 上传错误: {e}")
        return False

def test_start_register(user_id):
    """测试开始注册"""
    print("🧪 测试开始注册...")
    
    try:
        response = requests.get(f"{BASE_URL}/register/{user_id}/")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                process_id = data['data']['id']
                print(f"✅ 注册开始成功: {process_id}")
                return process_id
            else:
                print(f"❌ 注册开始失败: {data['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return None

def test_get_register_status(user_id):
    """测试获取注册状态"""
    print("🧪 测试获取注册状态...")
    
    try:
        response = requests.get(f"{BASE_URL}/status/{user_id}/")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                status = data['data']['status']
                message = data['data']['message']
                print(f"✅ 状态查询成功: {status} - {message}")
                return status
            else:
                print(f"❌ 状态查询失败: {data['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return None

def test_login(user_id, test_file):
    """测试登录验证"""
    print("🧪 测试登录验证...")
    
    try:
        with open(test_file, 'rb') as f:
            files = {'file': ('test_login.wav', f, 'audio/wav')}
            # 使用fake参数进行测试
            response = requests.post(
                f"{BASE_URL}/login/{user_id}/?fake=1",
                files=files
            )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                process_id = data['data']['id']
                print(f"✅ 登录验证开始: {process_id}")
                return process_id
            else:
                print(f"❌ 登录验证失败: {data['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return None

def test_get_login_status(user_id, status_id):
    """测试获取登录状态"""
    print("🧪 测试获取登录状态...")
    
    try:
        response = requests.get(f"{BASE_URL}/login/{user_id}/status/{status_id}/")
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                status = data['data']['status']
                message = data['data']['message']
                print(f"✅ 登录状态查询成功: {status} - {message}")
                return status
            else:
                print(f"❌ 登录状态查询失败: {data['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return None

def main():
    """主测试流程"""
    print("=" * 60)
    print("🧪 HCR-Auth 后端API测试")
    print("=" * 60)
    
    # 创建测试数据
    test_file = create_test_data()
    print(f"📁 测试文件: {test_file}")
    
    # 1. 测试创建用户
    user_id = test_create_user()
    if not user_id:
        print("❌ 用户创建失败，终止测试")
        return
    
    print()
    
    # 2. 测试文件上传
    if not test_upload_file(user_id, test_file):
        print("❌ 文件上传失败，继续其他测试")
    
    print()
    
    # 3. 测试开始注册
    process_id = test_start_register(user_id)
    if process_id:
        print("⏳ 等待注册处理...")
        time.sleep(3)
        
        # 4. 测试获取注册状态
        status = test_get_register_status(user_id)
        print()
    
    # 5. 测试登录验证
    login_process_id = test_login(user_id, test_file)
    if login_process_id:
        print("⏳ 等待登录验证...")
        time.sleep(3)
        
        # 6. 测试获取登录状态
        login_status = test_get_login_status(user_id, login_process_id)
    
    print()
    print("=" * 60)
    print("🎉 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
