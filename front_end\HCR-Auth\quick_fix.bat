@echo off
title Frontend Quick Fix

echo ================================================================
echo HCR-Auth Frontend Quick Fix
echo ================================================================
echo.

echo Current directory: %CD%
echo.

echo Cleanup...
rmdir /s /q node_modules 2>nul
del package-lock.json 2>nul
del yarn.lock 2>nul
echo Done
echo.

echo Configure npm...
npm config set registry https://registry.npmmirror.com
echo Done
echo.

echo Installing dependencies...
npm install --legacy-peer-deps

if %errorlevel% equ 0 (
    echo.
    echo Success! Starting dev server...
    npm run dev
) else (
    echo.
    echo Install failed. Trying alternative...
    npm install --force
    
    if %errorlevel% equ 0 (
        echo.
        echo Success! Starting dev server...
        npm run dev
    ) else (
        echo.
        echo All methods failed.
        echo Try: npm cache clean --force
        echo Then: npm install --legacy-peer-deps --force
    )
)

pause
