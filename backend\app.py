# -*- coding: utf-8 -*-
"""
骨振识息系统后端API服务
基于Flask的RESTful API，提供用户注册、身份认证等功能
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import uuid
import json
import threading
import time
from datetime import datetime
import logging
from werkzeug.utils import secure_filename

# 导入自定义模块
from core.data_processor import DataProcessor
from core.model_inference import ModelInference
from core.user_manager import UserManager
from utils.config import Config
from utils.logger import setup_logger

# 初始化Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置
app.config.update(Config.get_flask_config())

# 设置日志
logger = setup_logger()

# 初始化核心组件
user_manager = UserManager()
data_processor = DataProcessor()
model_inference = ModelInference()

# 全局状态管理
processing_status = {}
processing_lock = threading.Lock()


@app.route('/api/create/', methods=['GET'])
def create_user():
    """创建新用户"""
    try:
        user_id = user_manager.create_user()
        logger.info(f"Created new user: {user_id}")
        
        return jsonify({
            'success': True,
            'data': {
                'id': user_id
            },
            'message': '用户创建成功'
        })
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'用户创建失败: {str(e)}'
        }), 500


@app.route('/api/upload/<user_id>/<data_type>/', methods=['POST'])
def upload_file(user_id, data_type):
    """上传振动数据文件"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有文件被上传'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        # 验证用户是否存在
        if not user_manager.user_exists(user_id):
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 保存文件
        filename = secure_filename(file.filename)
        file_path = user_manager.save_user_file(user_id, data_type, file, filename)
        
        logger.info(f"File uploaded for user {user_id}, type {data_type}: {filename}")
        
        return jsonify({
            'success': True,
            'data': {
                'file_path': file_path,
                'data_type': data_type
            },
            'message': '文件上传成功'
        })
        
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'文件上传失败: {str(e)}'
        }), 500


@app.route('/api/register/<user_id>/', methods=['GET'])
def start_register(user_id):
    """开始注册流程"""
    try:
        if not user_manager.user_exists(user_id):
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 启动后台注册处理
        process_id = str(uuid.uuid4())
        
        with processing_lock:
            processing_status[process_id] = {
                'status': 'analyzing',
                'user_id': user_id,
                'type': 'register',
                'start_time': datetime.now(),
                'message': '正在处理注册数据...'
            }
        
        # 启动后台线程处理注册
        thread = threading.Thread(
            target=process_registration,
            args=(user_id, process_id)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'data': {
                'id': process_id
            },
            'message': '注册处理已开始'
        })
        
    except Exception as e:
        logger.error(f"Error starting registration: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'注册启动失败: {str(e)}'
        }), 500


@app.route('/api/status/<user_id>/', methods=['GET'])
def get_register_status(user_id):
    """获取注册状态"""
    try:
        # 查找该用户的最新处理状态
        latest_status = None
        with processing_lock:
            for process_id, status in processing_status.items():
                if status['user_id'] == user_id and status['type'] == 'register':
                    if latest_status is None or status['start_time'] > latest_status['start_time']:
                        latest_status = status
        
        if latest_status is None:
            return jsonify({
                'success': False,
                'message': '未找到注册状态'
            }), 404
        
        return jsonify({
            'success': True,
            'data': {
                'status': latest_status['status'],
                'message': latest_status['message']
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting register status: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500


@app.route('/api/login/<user_id>/', methods=['POST'])
def login_user(user_id):
    """用户登录验证"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有文件被上传'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        # 验证用户是否存在且已注册
        if not user_manager.user_exists(user_id):
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        if not user_manager.is_user_registered(user_id):
            return jsonify({
                'success': False,
                'message': '用户尚未完成注册'
            }), 400
        
        # 获取fake参数（用于测试）
        fake = request.args.get('fake', None)
        
        # 保存登录文件
        filename = secure_filename(file.filename)
        file_path = user_manager.save_user_file(user_id, 'login', file, filename)
        
        # 启动后台登录验证处理
        process_id = str(uuid.uuid4())
        
        with processing_lock:
            processing_status[process_id] = {
                'status': 'analyzing',
                'user_id': user_id,
                'type': 'login',
                'start_time': datetime.now(),
                'message': '正在验证身份...',
                'fake': fake
            }
        
        # 启动后台线程处理登录验证
        thread = threading.Thread(
            target=process_login,
            args=(user_id, process_id, file_path, fake)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'data': {
                'id': process_id
            },
            'message': '身份验证已开始'
        })
        
    except Exception as e:
        logger.error(f"Error during login: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'登录失败: {str(e)}'
        }), 500


@app.route('/api/login/<user_id>/status/<status_id>/', methods=['GET'])
def get_login_status(user_id, status_id):
    """获取登录验证状态"""
    try:
        with processing_lock:
            if status_id not in processing_status:
                return jsonify({
                    'success': False,
                    'message': '状态ID不存在'
                }), 404
            
            status = processing_status[status_id]
            
            return jsonify({
                'success': True,
                'data': {
                    'status': status['status'],
                    'message': status['message']
                }
            })
        
    except Exception as e:
        logger.error(f"Error getting login status: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取状态失败: {str(e)}'
        }), 500


def process_registration(user_id, process_id):
    """后台处理注册流程"""
    try:
        logger.info(f"Starting registration processing for user {user_id}")
        
        # 模拟处理时间
        time.sleep(2)
        
        # 获取用户上传的所有文件
        user_files = user_manager.get_user_files(user_id)
        
        if not user_files:
            with processing_lock:
                processing_status[process_id]['status'] = 'failed'
                processing_status[process_id]['message'] = '没有找到用户数据文件'
            return
        
        # 处理数据并提取特征
        features = data_processor.process_registration_data(user_id, user_files)
        
        # 训练用户模型
        model_path = model_inference.train_user_model(user_id, features)
        
        # 保存用户注册信息
        user_manager.complete_registration(user_id, model_path)
        
        with processing_lock:
            processing_status[process_id]['status'] = 'success'
            processing_status[process_id]['message'] = '注册成功'
        
        logger.info(f"Registration completed for user {user_id}")
        
    except Exception as e:
        logger.error(f"Error processing registration for user {user_id}: {str(e)}")
        with processing_lock:
            processing_status[process_id]['status'] = 'failed'
            processing_status[process_id]['message'] = f'注册处理失败: {str(e)}'


def process_login(user_id, process_id, file_path, fake=None):
    """后台处理登录验证"""
    try:
        logger.info(f"Starting login processing for user {user_id}")
        
        # 模拟处理时间
        time.sleep(2)
        
        # 如果有fake参数，直接返回对应结果（用于测试）
        if fake is not None:
            result = fake == '1'  # '1'表示验证成功，'0'表示验证失败
            with processing_lock:
                processing_status[process_id]['status'] = 'success' if result else 'failed'
                processing_status[process_id]['message'] = '身份验证成功' if result else '身份验证失败'
            return
        
        # 处理登录数据
        features = data_processor.process_login_data(file_path)
        
        # 进行身份验证
        result = model_inference.verify_user(user_id, features)
        
        with processing_lock:
            processing_status[process_id]['status'] = 'success' if result else 'failed'
            processing_status[process_id]['message'] = '身份验证成功' if result else '身份验证失败'
        
        logger.info(f"Login verification completed for user {user_id}, result: {result}")
        
    except Exception as e:
        logger.error(f"Error processing login for user {user_id}: {str(e)}")
        with processing_lock:
            processing_status[process_id]['status'] = 'failed'
            processing_status[process_id]['message'] = f'验证处理失败: {str(e)}'


@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'message': '接口不存在'
    }), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'message': '服务器内部错误'
    }), 500


if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
    os.makedirs(Config.MODEL_FOLDER, exist_ok=True)
    os.makedirs(Config.LOG_FOLDER, exist_ok=True)
    
    logger.info("Starting HCR-Auth backend server...")
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=Config.DEBUG
    )
