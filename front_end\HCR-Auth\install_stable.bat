@echo off
chcp 65001 >nul
title 安装稳定版本

echo ================================================================
echo 🔒 安装稳定版本 (降级到兼容版本)
echo ================================================================
echo.

echo 🧹 清理现有安装...
rmdir /s /q node_modules 2>nul
del package-lock.json 2>nul
del yarn.lock 2>nul
echo ✅ 清理完成
echo.

echo 📦 备份当前package.json...
if exist package.json (
    copy package.json package_current.json >nul
    echo ✅ 已备份为 package_current.json
)

echo 📦 使用稳定版本配置...
copy package_stable.json package.json >nul
echo ✅ 已切换到稳定版本配置
echo.

echo 🔧 配置npm...
npm config set registry https://registry.npmmirror.com
echo ✅ 配置完成
echo.

echo 📦 安装稳定版本依赖...
npm install

if %errorlevel% equ 0 (
    echo.
    echo 🎉 稳定版本安装成功！
    echo.
    echo 🚀 启动开发服务器...
    npm run dev
) else (
    echo.
    echo ❌ 稳定版本安装也失败了
    echo.
    echo 📦 恢复原始配置...
    if exist package_current.json (
        copy package_current.json package.json >nul
        echo ✅ 已恢复原始配置
    )
)

pause
