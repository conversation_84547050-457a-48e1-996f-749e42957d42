@echo off
chcp 65001 >nul
title HCR-Auth 骨振识息系统

echo ================================================================
echo 🦴 骨振识息系统 (HCR-Auth) - Windows启动器
echo ================================================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 检查并安装Python依赖...
cd backend
pip install Flask Flask-CORS torch numpy scipy matplotlib werkzeug requests >nul 2>&1
if errorlevel 1 (
    echo ⚠️  依赖安装可能有问题，尝试继续...
)

echo ✅ 依赖检查完成
echo.

echo 🚀 启动后端服务...
cd ..
echo 📍 后端地址: http://localhost:5000
echo 💡 如需启动前端，请在另一个终端运行:
echo    cd front_end\HCR-Auth
echo    npm install
echo    npm run dev
echo.
echo 🛑 按 Ctrl+C 停止服务
echo ================================================================

python simple_start.py

pause
