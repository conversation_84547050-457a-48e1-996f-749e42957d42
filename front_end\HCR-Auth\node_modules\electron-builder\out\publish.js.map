{"version": 3, "file": "publish.js", "sourceRoot": "", "sources": ["../src/publish.ts"], "names": [], "mappings": ";;;AAaA,0DA0BC;AAED,0BAQC;AAED,kEAmBC;AApED,qDAA6J;AAE7J,2EAAsF;AACtF,mEAAkE;AAClE,+CAAgG;AAChG,+BAA8B;AAC9B,6BAA4B;AAC5B,+BAA8B;AAC9B,uCAA0D;AAE1D,gBAAgB;AAChB,SAAgB,uBAAuB,CAAC,KAAiB;IACvD,4CAA4C;IAC5C,qCAAqC;IACrC,OAAO,KAAK;SACT,mBAAmB,CAAC;QACnB,sBAAsB,EAAE,KAAK;KAC9B,CAAC;SACD,MAAM,CAAC,OAAO,EAAE;QACf,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,IAAI;QACZ,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,IAAI;QACjB,WAAW,EAAE,yCAAyC;KACvD,CAAC;SACD,MAAM,CAAC,SAAS,EAAE;QACjB,KAAK,EAAE,CAAC,GAAG,CAAC;QACZ,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,2FAA2F;KACzG,CAAC;SACD,MAAM,CAAC,QAAQ,EAAE;QAChB,KAAK,EAAE,CAAC,GAAG,CAAC;QACZ,IAAI,EAAE,QAAQ;QACd,WAAW,EACT,4HAA4H,GAAG,KAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC;KAC1K,CAAC;SACD,YAAY,CAAC,OAAO,CAAC,CAAA;AAC1B,CAAC;AAEM,KAAK,UAAU,OAAO,CAAC,IAAiG;IAC7H,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACrC,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YACrB,IAAI,EAAE,IAAI;SACX,CAAA;IACH,CAAC,CAAC,CAAA;IACF,OAAO,2BAA2B,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAA;AAC3F,CAAC;AAEM,KAAK,UAAU,2BAA2B,CAC/C,aAAsD,EACtD,YAAqB,EACrB,qBAA8B,EAC9B,oBAA8B;IAE9B,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IAChC,MAAM,MAAM,GAAG,MAAM,IAAA,kBAAS,EAAC,UAAU,EAAE,qBAAqB,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,CAAA;IAExI,MAAM,YAAY,GAAiB,IAAA,0BAAgB,EAAC,EAAE,MAAM,EAAE,CAAC,CAAA;IAC/D,IAAA,0CAAwB,EAAC,YAAY,CAAC,CAAA;IAEtC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAA;IACxD,MAAM,KAAK,GAAiB,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACpC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAA,6BAAc,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAA,kDAA+B,EAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAA;IACxI,CAAC,CAAC,CAAA;IAEF,OAAO,uBAAuB,CAAC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,CAAA;AACnE,CAAC;AAED,KAAK,UAAU,uBAAuB,CACpC,OAAyC,EACzC,WAAyB,EACzB,YAAqB,EACrB,oBAAuC,IAAI,mCAAiB,EAAE,EAC9D,WAAqB,IAAI,0BAAQ,CAAC,OAAO,EAAE,iBAAiB,CAAC;IAE7D,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAA;IAC/B,MAAM,OAAO,GAAG,IAAI,yBAAO,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;IACnD,MAAM,cAAc,GAAG,IAAI,gCAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAA;IAE/E,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,kBAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;QAC/B,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAA;QACnC,cAAc,CAAC,WAAW,EAAE,CAAA;IAC9B,CAAC,CAAA;IACD,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;IAErC,IAAI,CAAC;QACH,MAAM,qBAAqB,GAAG,MAAM,cAAc,CAAC,8BAA8B,EAAE,CAAA;QACnF,IAAI,qBAAqB,IAAI,IAAI,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxE,MAAM,IAAI,wCAAyB,CAAC,0CAA0C,CAAC,CAAA;QACjF,CAAC;QAED,KAAK,MAAM,WAAW,IAAI,WAAW,EAAE,CAAC;YACtC,KAAK,MAAM,oBAAoB,IAAI,qBAAqB,EAAE,CAAC;gBACzD,MAAM,cAAc,CAAC,cAAc,CAAC,oBAAoB,EAAE,WAAW,EAAE,OAAO,CAAC,CAAA;YACjF,CAAC;QACH,CAAC;QAED,MAAM,cAAc,CAAC,UAAU,EAAE,CAAA;QACjC,OAAO,WAAW,CAAA;IACpB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAA;QACnC,cAAc,CAAC,WAAW,EAAE,CAAA;QAC5B,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;QAC/C,kBAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAA;IAChG,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,IAAI;IACX,OAAO,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,IAAW,CAAC,CAAA;AAC5D,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,kBAAG,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAA;IAC9D,IAAI,EAAE,CAAC,KAAK,CAAC,gCAAiB,CAAC,CAAA;AACjC,CAAC", "sourcesContent": ["#! /usr/bin/env node\n\nimport { AppInfo, CancellationToken, Packager, PackagerOptions, PublishManager, PublishOptions, UploadTask, checkBuildRequestOptions } from \"app-builder-lib\"\nimport { Publish } from \"app-builder-lib/out/core\"\nimport { computeSafeArtifactNameIfNeeded } from \"app-builder-lib/out/platformPackager\"\nimport { getConfig } from \"app-builder-lib/out/util/config/config\"\nimport { InvalidConfigurationError, archFromString, log, printErrorAndExit } from \"builder-util\"\nimport * as chalk from \"chalk\"\nimport * as path from \"path\"\nimport * as yargs from \"yargs\"\nimport { BuildOptions, normalizeOptions } from \"./builder\"\n\n/** @internal */\nexport function configurePublishCommand(yargs: yargs.Argv): yargs.Argv {\n  // https://github.com/yargs/yargs/issues/760\n  // demandOption is required to be set\n  return yargs\n    .parserConfiguration({\n      \"camel-case-expansion\": false,\n    })\n    .option(\"files\", {\n      alias: \"f\",\n      string: true,\n      type: \"array\",\n      requiresArg: true,\n      description: \"The file(s) to upload to your publisher\",\n    })\n    .option(\"version\", {\n      alias: [\"v\"],\n      type: \"string\",\n      description: \"The app/build version used when searching for an upload release (used by some Publishers)\",\n    })\n    .option(\"config\", {\n      alias: [\"c\"],\n      type: \"string\",\n      description:\n        \"The path to an electron-builder config. Defaults to `electron-builder.yml` (or `json`, or `json5`, or `js`, or `ts`), see \" + chalk.underline(\"https://goo.gl/YFRJOM\"),\n    })\n    .demandOption(\"files\")\n}\n\nexport async function publish(args: { files: string[]; version: string | undefined; configurationFilePath: string | undefined }) {\n  const uploadTasks = args.files.map(f => {\n    return {\n      file: path.resolve(f),\n      arch: null,\n    }\n  })\n  return publishArtifactsWithOptions(uploadTasks, args.version, args.configurationFilePath)\n}\n\nexport async function publishArtifactsWithOptions(\n  uploadOptions: { file: string; arch: string | null }[],\n  buildVersion?: string,\n  configurationFilePath?: string,\n  publishConfiguration?: Publish\n) {\n  const projectDir = process.cwd()\n  const config = await getConfig(projectDir, configurationFilePath || null, { publish: publishConfiguration, detectUpdateChannel: false })\n\n  const buildOptions: BuildOptions = normalizeOptions({ config })\n  checkBuildRequestOptions(buildOptions)\n\n  const uniqueUploads = Array.from(new Set(uploadOptions))\n  const tasks: UploadTask[] = uniqueUploads.map(({ file, arch }) => {\n    const filename = path.basename(file)\n    return { file, arch: arch ? archFromString(arch) : null, safeArtifactName: computeSafeArtifactNameIfNeeded(filename, () => filename) }\n  })\n\n  return publishPackageWithTasks(buildOptions, tasks, buildVersion)\n}\n\nasync function publishPackageWithTasks(\n  options: PackagerOptions & PublishOptions,\n  uploadTasks: UploadTask[],\n  buildVersion?: string,\n  cancellationToken: CancellationToken = new CancellationToken(),\n  packager: Packager = new Packager(options, cancellationToken)\n) {\n  await packager.validateConfig()\n  const appInfo = new AppInfo(packager, buildVersion)\n  const publishManager = new PublishManager(packager, options, cancellationToken)\n\n  const sigIntHandler = () => {\n    log.warn(\"cancelled by SIGINT\")\n    packager.cancellationToken.cancel()\n    publishManager.cancelTasks()\n  }\n  process.once(\"SIGINT\", sigIntHandler)\n\n  try {\n    const publishConfigurations = await publishManager.getGlobalPublishConfigurations()\n    if (publishConfigurations == null || publishConfigurations.length === 0) {\n      throw new InvalidConfigurationError(\"unable to find any publish configuration\")\n    }\n\n    for (const newArtifact of uploadTasks) {\n      for (const publishConfiguration of publishConfigurations) {\n        await publishManager.scheduleUpload(publishConfiguration, newArtifact, appInfo)\n      }\n    }\n\n    await publishManager.awaitTasks()\n    return uploadTasks\n  } catch (error: any) {\n    packager.cancellationToken.cancel()\n    publishManager.cancelTasks()\n    process.removeListener(\"SIGINT\", sigIntHandler)\n    log.error({ message: (error.stack || error.message || error).toString() }, \"error publishing\")\n  }\n  return null\n}\n\nfunction main() {\n  return publish(configurePublishCommand(yargs).argv as any)\n}\n\nif (require.main === module) {\n  log.warn(\"please use as subcommand: electron-builder publish\")\n  main().catch(printErrorAndExit)\n}\n"]}