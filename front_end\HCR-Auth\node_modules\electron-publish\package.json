{"name": "electron-publish", "version": "26.0.11", "main": "out/index.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/electron-publish"}, "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "files": ["out"], "dependencies": {"@types/fs-extra": "^9.0.11", "chalk": "^4.1.2", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "lazy-val": "^1.0.5", "mime": "^2.5.2", "builder-util-runtime": "9.3.1", "builder-util": "26.0.11"}, "typings": "./out/index.d.ts", "devDependencies": {"@types/mime": "2.0.3"}}