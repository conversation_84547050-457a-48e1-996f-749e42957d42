var Ma=Object.create;var On=Object.defineProperty;var ja=Object.getOwnPropertyDescriptor;var Ra=Object.getOwnPropertyNames;var Ja=Object.getPrototypeOf,qa=Object.prototype.hasOwnProperty;var Wa=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Tr=(e,t)=>{for(var r in t)On(e,r,{get:t[r],enumerable:!0})},Na=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of Ra(t))!qa.call(e,s)&&s!==r&&On(e,s,{get:()=>t[s],enumerable:!(n=ja(t,s))||n.enumerable});return e};var Ga=(e,t,r)=>(r=e!=null?Ma(Ja(e)):{},Na(t||!e||!e.__esModule?On(r,"default",{value:e,enumerable:!0}):r,e));var Ys=(e,t,r)=>{if(!t.has(e))throw TypeError("Cannot "+r)};var it=(e,t,r)=>(Ys(e,t,"read from private field"),r?r.call(e):t.get(e)),Xs=(e,t,r)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,r)},Vs=(e,t,r,n)=>(Ys(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);var yi=Wa(Pt=>{"use strict";Object.defineProperty(Pt,"__esModule",{value:!0});Pt.extract=Zp;Pt.parse=tc;Pt.parseWithComments=Di;Pt.print=rc;Pt.strip=ec;var $p=/\*\/$/,Hp=/^\/\*\*?/,li=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,Kp=/(^|\s+)\/\/([^\r\n]*)/g,oi=/^(\r?\n)+/,zp=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,pi=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,Qp=/(\r?\n|^) *\* ?/g,mi=[];function Zp(e){let t=e.match(li);return t?t[0].trimLeft():""}function ec(e){let t=e.match(li);return t&&t[0]?e.substring(t[0].length):e}function tc(e){return Di(e).pragmas}function Di(e){let t=`
`;e=e.replace(Hp,"").replace($p,"").replace(Qp,"$1");let r="";for(;r!==e;)r=e,e=e.replace(zp,`${t}$1 $2${t}`);e=e.replace(oi,"").trimRight();let n=Object.create(null),s=e.replace(pi,"").replace(oi,"").trimRight(),u;for(;u=pi.exec(e);){let i=u[2].replace(Kp,"");typeof n[u[1]]=="string"||Array.isArray(n[u[1]])?n[u[1]]=mi.concat(n[u[1]],i):n[u[1]]=i}return{comments:s,pragmas:n}}function rc({comments:e="",pragmas:t={}}){let r=`
`,n="/**",s=" *",u=" */",i=Object.keys(t),a=i.flatMap(p=>ci(p,t[p])).map(p=>`${s} ${p}${r}`).join("");if(!e){if(i.length===0)return"";if(i.length===1&&!Array.isArray(t[i[0]])){let p=t[i[0]];return`${n} ${ci(i[0],p)[0]}${u}`}}let o=e.split(r).map(p=>`${s} ${p}`).join(r)+r;return n+r+(e?o:"")+(e&&i.length?s+r:"")+a+u}function ci(e,t){return mi.concat(t).map(r=>`@${e} ${r}`.trim())}});var qs={};Tr(qs,{languages:()=>rm,options:()=>La,printers:()=>tm});var $s=[{linguistLanguageId:183,name:"JavaScript",type:"programming",tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",color:"#f1e05a",aliases:["js","node"],extensions:[".js","._js",".bones",".cjs",".es",".es6",".frag",".gs",".jake",".javascript",".jsb",".jscad",".jsfl",".jslib",".jsm",".jspre",".jss",".mjs",".njs",".pac",".sjs",".ssjs",".xsjs",".xsjslib",".wxs"],filenames:["Jakefile"],interpreters:["chakra","d8","gjs","js","node","nodejs","qjs","rhino","v8","v8-shell","zx"],parsers:["babel","acorn","espree","meriyah","babel-flow","babel-ts","flow","typescript"],vscodeLanguageIds:["javascript","mongo"]},{linguistLanguageId:183,name:"Flow",type:"programming",tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",color:"#f1e05a",aliases:[],extensions:[".js.flow"],filenames:[],interpreters:["chakra","d8","gjs","js","node","nodejs","qjs","rhino","v8","v8-shell"],parsers:["flow","babel-flow"],vscodeLanguageIds:["javascript"]},{linguistLanguageId:183,name:"JSX",type:"programming",tmScope:"source.js.jsx",aceMode:"javascript",codemirrorMode:"jsx",codemirrorMimeType:"text/jsx",color:void 0,aliases:void 0,extensions:[".jsx"],filenames:void 0,interpreters:void 0,parsers:["babel","babel-flow","babel-ts","flow","typescript","espree","meriyah"],vscodeLanguageIds:["javascriptreact"],group:"JavaScript"},{linguistLanguageId:378,name:"TypeScript",type:"programming",color:"#3178c6",aliases:["ts"],interpreters:["deno","ts-node"],extensions:[".ts",".cts",".mts"],tmScope:"source.ts",aceMode:"typescript",codemirrorMode:"javascript",codemirrorMimeType:"application/typescript",parsers:["typescript","babel-ts"],vscodeLanguageIds:["typescript"]},{linguistLanguageId:94901924,name:"TSX",type:"programming",color:"#3178c6",group:"TypeScript",extensions:[".tsx"],tmScope:"source.tsx",aceMode:"javascript",codemirrorMode:"jsx",codemirrorMimeType:"text/jsx",parsers:["typescript","babel-ts"],vscodeLanguageIds:["typescriptreact"]}];var Rs={};Tr(Rs,{canAttachComment:()=>Fp,embed:()=>ai,experimentalFeatures:()=>Hl,getCommentChildNodes:()=>Cp,getVisitorKeys:()=>Br,handleComments:()=>ns,insertPragma:()=>Ei,isBlockComment:()=>ee,isGap:()=>Ap,massageAstNode:()=>Bu,print:()=>Sa,printComment:()=>Mu,willPrintOwnComments:()=>ss});var Ua=(e,t,r,n)=>{if(!(e&&t==null))return t.replaceAll?t.replaceAll(r,n):r.global?t.replace(r,n):t.split(r).join(n)},H=Ua;var Ya=(e,t,r)=>{if(!(e&&t==null))return Array.isArray(t)||typeof t=="string"?t[r<0?t.length+r:r]:t.at(r)},v=Ya;var Xa=/^[\$A-Z_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC][\$0-9A-Z_a-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]*$/,Va=e=>Xa.test(e),Hs=Va;function $a(e){return e!==null&&typeof e=="object"}var Ks=$a;function*Ha(e,t){let{getVisitorKeys:r,filter:n=()=>!0}=t,s=u=>Ks(u)&&n(u);for(let u of r(e)){let i=e[u];if(Array.isArray(i))for(let a of i)s(a)&&(yield a);else s(i)&&(yield i)}}function*Ka(e,t){let r=[e];for(let n=0;n<r.length;n++){let s=r[n];for(let u of Ha(s,t))yield u,r.push(u)}}function zs(e,{getVisitorKeys:t,predicate:r}){for(let n of Ka(e,{getVisitorKeys:t}))if(r(n))return!0;return!1}var Qs=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;function Zs(e){return e===12288||e>=65281&&e<=65376||e>=65504&&e<=65510}function eu(e){return e>=4352&&e<=4447||e===8986||e===8987||e===9001||e===9002||e>=9193&&e<=9196||e===9200||e===9203||e===9725||e===9726||e===9748||e===9749||e>=9800&&e<=9811||e===9855||e===9875||e===9889||e===9898||e===9899||e===9917||e===9918||e===9924||e===9925||e===9934||e===9940||e===9962||e===9970||e===9971||e===9973||e===9978||e===9981||e===9989||e===9994||e===9995||e===10024||e===10060||e===10062||e>=10067&&e<=10069||e===10071||e>=10133&&e<=10135||e===10160||e===10175||e===11035||e===11036||e===11088||e===11093||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12771||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=19903||e>=19968&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||e===94192||e===94193||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101632&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||e===110589||e===110590||e>=110592&&e<=110882||e===110898||e>=110928&&e<=110930||e===110933||e>=110948&&e<=110951||e>=110960&&e<=111355||e===126980||e===127183||e===127374||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||e===127568||e===127569||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||e===127988||e>=127992&&e<=128062||e===128064||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||e===128378||e===128405||e===128406||e===128420||e>=128507&&e<=128591||e>=128640&&e<=128709||e===128716||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||e===128747||e===128748||e>=128756&&e<=128764||e>=128992&&e<=129003||e===129008||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129672||e>=129680&&e<=129725||e>=129727&&e<=129733||e>=129742&&e<=129755||e>=129760&&e<=129768||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}var tu=e=>!(Zs(e)||eu(e));var za=/[^\x20-\x7F]/;function Qa(e){if(!e)return 0;if(!za.test(e))return e.length;e=e.replace(Qs(),"  ");let t=0;for(let r of e){let n=r.codePointAt(0);n<=31||n>=127&&n<=159||n>=768&&n<=879||(t+=tu(n)?1:2)}return t}var Ze=Qa;function xr(e){return(t,r,n)=>{let s=!!(n!=null&&n.backwards);if(r===!1)return!1;let{length:u}=t,i=r;for(;i>=0&&i<u;){let a=t.charAt(i);if(e instanceof RegExp){if(!e.test(a))return i}else if(!e.includes(a))return i;s?i--:i++}return i===-1||i===u?i:!1}}var Tm=xr(/\s/),Je=xr(" 	"),ru=xr(",; 	"),nu=xr(/[^\n\r]/);function Za(e,t,r){let n=!!(r!=null&&r.backwards);if(t===!1)return!1;let s=e.charAt(t);if(n){if(e.charAt(t-1)==="\r"&&s===`
`)return t-2;if(s===`
`||s==="\r"||s==="\u2028"||s==="\u2029")return t-1}else{if(s==="\r"&&e.charAt(t+1)===`
`)return t+2;if(s===`
`||s==="\r"||s==="\u2028"||s==="\u2029")return t+1}return t}var qe=Za;function eo(e,t,r={}){let n=Je(e,r.backwards?t-1:t,r),s=qe(e,n,r);return n!==s}var Z=eo;function to(e,t){if(t===!1)return!1;if(e.charAt(t)==="/"&&e.charAt(t+1)==="*"){for(let r=t+2;r<e.length;++r)if(e.charAt(r)==="*"&&e.charAt(r+1)==="/")return r+2}return t}var Ot=to;function ro(e,t){return t===!1?!1:e.charAt(t)==="/"&&e.charAt(t+1)==="/"?nu(e,t):t}var vt=ro;function no(e,t){let r=null,n=t;for(;n!==r;)r=n,n=ru(e,n),n=Ot(e,n),n=Je(e,n);return n=vt(e,n),n=qe(e,n),n!==!1&&Z(e,n)}var _t=no;function so(e){return Array.isArray(e)&&e.length>0}var L=so;var gr="'",su='"';function uo(e,t){let r=t===!0||t===gr?gr:su,n=r===gr?su:gr,s=0,u=0;for(let i of e)i===r?s++:i===n&&u++;return s>u?n:r}var hr=uo;function io(e,t,r){let n=t==='"'?"'":'"',u=H(!1,e,/\\(.)|(["'])/gs,(i,a,o)=>a===n?a:o===t?"\\"+o:o||(r&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(a)?a:"\\"+a));return t+u+t}var uu=io;function ao(e,t){let r=e.slice(1,-1),n=t.parser==="json"||t.parser==="jsonc"||t.parser==="json5"&&t.quoteProps==="preserve"&&!t.singleQuote?'"':t.__isInHtmlAttribute?"'":hr(r,t.singleQuote);return uu(r,n,!(t.parser==="css"||t.parser==="less"||t.parser==="scss"||t.__embeddedInHtml))}var at=ao;function j(e){var n,s,u;let t=((n=e.range)==null?void 0:n[0])??e.start,r=(u=((s=e.declaration)==null?void 0:s.decorators)??e.decorators)==null?void 0:u[0];return r?Math.min(j(r),t):t}function I(e){var t;return((t=e.range)==null?void 0:t[1])??e.end}function gt(e,t){let r=j(e);return Number.isInteger(r)&&r===j(t)}function oo(e,t){let r=I(e);return Number.isInteger(r)&&r===I(t)}function iu(e,t){return gt(e,t)&&oo(e,t)}var Qt=null;function Zt(e){if(Qt!==null&&typeof Qt.property){let t=Qt;return Qt=Zt.prototype=null,t}return Qt=Zt.prototype=e??Object.create(null),new Zt}var po=10;for(let e=0;e<=po;e++)Zt();function vn(e){return Zt(e)}function co(e,t="type"){vn(e);function r(n){let s=n[t],u=e[s];if(!Array.isArray(u))throw Object.assign(new Error(`Missing visitor keys for '${s}'.`),{node:n});return u}return r}var Sr=co;var au={ArrayExpression:["elements"],AssignmentExpression:["left","right"],BinaryExpression:["left","right"],InterpreterDirective:[],Directive:["value"],DirectiveLiteral:[],BlockStatement:["directives","body"],BreakStatement:["label"],CallExpression:["callee","arguments","typeParameters","typeArguments"],CatchClause:["param","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DoWhileStatement:["test","body"],EmptyStatement:[],ExpressionStatement:["expression"],File:["program"],ForInStatement:["left","right","body"],ForStatement:["init","test","update","body"],FunctionDeclaration:["id","params","body","returnType","typeParameters","predicate"],FunctionExpression:["id","params","body","returnType","typeParameters"],Identifier:["typeAnnotation","decorators"],IfStatement:["test","consequent","alternate"],LabeledStatement:["label","body"],StringLiteral:[],NumericLiteral:[],NullLiteral:[],BooleanLiteral:[],RegExpLiteral:[],LogicalExpression:["left","right"],MemberExpression:["object","property"],NewExpression:["callee","arguments","typeParameters","typeArguments"],Program:["directives","body"],ObjectExpression:["properties"],ObjectMethod:["key","params","body","decorators","returnType","typeParameters"],ObjectProperty:["key","value","decorators"],RestElement:["argument","typeAnnotation","decorators"],ReturnStatement:["argument"],SequenceExpression:["expressions"],ParenthesizedExpression:["expression"],SwitchCase:["test","consequent"],SwitchStatement:["discriminant","cases"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],AssignmentPattern:["left","right","decorators","typeAnnotation"],ArrayPattern:["elements","typeAnnotation","decorators"],ArrowFunctionExpression:["params","body","returnType","typeParameters","predicate"],ClassBody:["body"],ClassExpression:["id","body","superClass","mixins","typeParameters","superTypeParameters","implements","decorators","superTypeArguments"],ClassDeclaration:["id","body","superClass","mixins","typeParameters","superTypeParameters","implements","decorators","superTypeArguments"],ExportAllDeclaration:["source","attributes","exported"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source","attributes"],ExportSpecifier:["local","exported"],ForOfStatement:["left","right","body"],ImportDeclaration:["specifiers","source","attributes"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["local","imported"],ImportExpression:["source","options","attributes"],MetaProperty:["meta","property"],ClassMethod:["key","params","body","decorators","returnType","typeParameters"],ObjectPattern:["properties","typeAnnotation","decorators"],SpreadElement:["argument"],Super:[],TaggedTemplateExpression:["tag","quasi","typeParameters","typeArguments"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],YieldExpression:["argument"],AwaitExpression:["argument"],Import:[],BigIntLiteral:[],ExportNamespaceSpecifier:["exported"],OptionalMemberExpression:["object","property"],OptionalCallExpression:["callee","arguments","typeParameters","typeArguments"],ClassProperty:["key","value","typeAnnotation","decorators","variance"],ClassAccessorProperty:["key","value","typeAnnotation","decorators"],ClassPrivateProperty:["key","value","decorators","typeAnnotation","variance"],ClassPrivateMethod:["key","params","body","decorators","returnType","typeParameters"],PrivateName:["id"],StaticBlock:["body"],AnyTypeAnnotation:[],ArrayTypeAnnotation:["elementType"],BooleanTypeAnnotation:[],BooleanLiteralTypeAnnotation:[],NullLiteralTypeAnnotation:[],ClassImplements:["id","typeParameters"],DeclareClass:["id","typeParameters","extends","mixins","implements","body"],DeclareFunction:["id","predicate"],DeclareInterface:["id","typeParameters","extends","body"],DeclareModule:["id","body"],DeclareModuleExports:["typeAnnotation"],DeclareTypeAlias:["id","typeParameters","right"],DeclareOpaqueType:["id","typeParameters","supertype"],DeclareVariable:["id"],DeclareExportDeclaration:["declaration","specifiers","source"],DeclareExportAllDeclaration:["source"],DeclaredPredicate:["value"],ExistsTypeAnnotation:[],FunctionTypeAnnotation:["typeParameters","params","rest","returnType","this"],FunctionTypeParam:["name","typeAnnotation"],GenericTypeAnnotation:["id","typeParameters"],InferredPredicate:[],InterfaceExtends:["id","typeParameters"],InterfaceDeclaration:["id","typeParameters","extends","body"],InterfaceTypeAnnotation:["extends","body"],IntersectionTypeAnnotation:["types"],MixedTypeAnnotation:[],EmptyTypeAnnotation:[],NullableTypeAnnotation:["typeAnnotation"],NumberLiteralTypeAnnotation:[],NumberTypeAnnotation:[],ObjectTypeAnnotation:["properties","indexers","callProperties","internalSlots"],ObjectTypeInternalSlot:["id","value","optional","static","method"],ObjectTypeCallProperty:["value"],ObjectTypeIndexer:["id","key","value","variance"],ObjectTypeProperty:["key","value","variance"],ObjectTypeSpreadProperty:["argument"],OpaqueType:["id","typeParameters","supertype","impltype"],QualifiedTypeIdentifier:["id","qualification"],StringLiteralTypeAnnotation:[],StringTypeAnnotation:[],SymbolTypeAnnotation:[],ThisTypeAnnotation:[],TupleTypeAnnotation:["types","elementTypes"],TypeofTypeAnnotation:["argument","typeArguments"],TypeAlias:["id","typeParameters","right"],TypeAnnotation:["typeAnnotation"],TypeCastExpression:["expression","typeAnnotation"],TypeParameter:["bound","default","variance"],TypeParameterDeclaration:["params"],TypeParameterInstantiation:["params"],UnionTypeAnnotation:["types"],Variance:[],VoidTypeAnnotation:[],EnumDeclaration:["id","body"],EnumBooleanBody:["members"],EnumNumberBody:["members"],EnumStringBody:["members"],EnumSymbolBody:["members"],EnumBooleanMember:["id","init"],EnumNumberMember:["id","init"],EnumStringMember:["id","init"],EnumDefaultedMember:["id"],IndexedAccessType:["objectType","indexType"],OptionalIndexedAccessType:["objectType","indexType"],JSXAttribute:["name","value"],JSXClosingElement:["name"],JSXElement:["openingElement","children","closingElement"],JSXEmptyExpression:[],JSXExpressionContainer:["expression"],JSXSpreadChild:["expression"],JSXIdentifier:[],JSXMemberExpression:["object","property"],JSXNamespacedName:["namespace","name"],JSXOpeningElement:["name","attributes","typeArguments","typeParameters"],JSXSpreadAttribute:["argument"],JSXText:[],JSXFragment:["openingFragment","children","closingFragment"],JSXOpeningFragment:[],JSXClosingFragment:[],Noop:[],Placeholder:[],V8IntrinsicIdentifier:[],ArgumentPlaceholder:[],BindExpression:["object","callee"],ImportAttribute:["key","value"],Decorator:["expression"],DoExpression:["body"],ExportDefaultSpecifier:["exported"],RecordExpression:["properties"],TupleExpression:["elements"],DecimalLiteral:[],ModuleExpression:["body"],TopicReference:[],PipelineTopicExpression:["expression"],PipelineBareFunction:["callee"],PipelinePrimaryTopicReference:[],TSParameterProperty:["parameter","decorators"],TSDeclareFunction:["id","typeParameters","params","returnType","body"],TSDeclareMethod:["decorators","key","typeParameters","params","returnType"],TSQualifiedName:["left","right"],TSCallSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSPropertySignature:["key","typeAnnotation"],TSMethodSignature:["key","typeParameters","parameters","typeAnnotation","params","returnType"],TSIndexSignature:["parameters","typeAnnotation"],TSAnyKeyword:[],TSBooleanKeyword:[],TSBigIntKeyword:[],TSIntrinsicKeyword:[],TSNeverKeyword:[],TSNullKeyword:[],TSNumberKeyword:[],TSObjectKeyword:[],TSStringKeyword:[],TSSymbolKeyword:[],TSUndefinedKeyword:[],TSUnknownKeyword:[],TSVoidKeyword:[],TSThisType:[],TSFunctionType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructorType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSTypeReference:["typeName","typeParameters","typeArguments"],TSTypePredicate:["parameterName","typeAnnotation"],TSTypeQuery:["exprName","typeParameters","typeArguments"],TSTypeLiteral:["members"],TSArrayType:["elementType"],TSTupleType:["elementTypes"],TSOptionalType:["typeAnnotation"],TSRestType:["typeAnnotation"],TSNamedTupleMember:["label","elementType"],TSUnionType:["types"],TSIntersectionType:["types"],TSConditionalType:["checkType","extendsType","trueType","falseType"],TSInferType:["typeParameter"],TSParenthesizedType:["typeAnnotation"],TSTypeOperator:["typeAnnotation"],TSIndexedAccessType:["objectType","indexType"],TSMappedType:["typeParameter","typeAnnotation","nameType"],TSLiteralType:["literal"],TSExpressionWithTypeArguments:["expression","typeParameters"],TSInterfaceDeclaration:["id","typeParameters","extends","body"],TSInterfaceBody:["body"],TSTypeAliasDeclaration:["id","typeParameters","typeAnnotation"],TSInstantiationExpression:["expression","typeParameters","typeArguments"],TSAsExpression:["expression","typeAnnotation"],TSSatisfiesExpression:["expression","typeAnnotation"],TSTypeAssertion:["typeAnnotation","expression"],TSEnumDeclaration:["id","members"],TSEnumMember:["id","initializer"],TSModuleDeclaration:["id","body"],TSModuleBlock:["body"],TSImportType:["argument","qualifier","typeParameters","typeArguments"],TSImportEqualsDeclaration:["id","moduleReference"],TSExternalModuleReference:["expression"],TSNonNullExpression:["expression"],TSExportAssignment:["expression"],TSNamespaceExportDeclaration:["id"],TSTypeAnnotation:["typeAnnotation"],TSTypeParameterInstantiation:["params"],TSTypeParameterDeclaration:["params"],TSTypeParameter:["constraint","default","name"],ChainExpression:["expression"],ExperimentalRestProperty:["argument"],ExperimentalSpreadProperty:["argument"],Literal:[],MethodDefinition:["decorators","key","value"],PrivateIdentifier:[],Property:["key","value"],PropertyDefinition:["decorators","key","typeAnnotation","value","variance"],AccessorProperty:["decorators","key","typeAnnotation","value"],TSAbstractAccessorProperty:["decorators","key","typeAnnotation"],TSAbstractKeyword:[],TSAbstractMethodDefinition:["key","value"],TSAbstractPropertyDefinition:["decorators","key","typeAnnotation"],TSAsyncKeyword:[],TSClassImplements:["expression","typeArguments","typeParameters"],TSDeclareKeyword:[],TSEmptyBodyFunctionExpression:["id","typeParameters","params","returnType"],TSExportKeyword:[],TSInterfaceHeritage:["expression","typeArguments","typeParameters"],TSPrivateKeyword:[],TSProtectedKeyword:[],TSPublicKeyword:[],TSReadonlyKeyword:[],TSStaticKeyword:[],TSTemplateLiteralType:["quasis","types"],AsExpression:["expression","typeAnnotation"],BigIntLiteralTypeAnnotation:[],BigIntTypeAnnotation:[],ConditionalTypeAnnotation:["checkType","extendsType","trueType","falseType"],DeclareEnum:["id","body"],InferTypeAnnotation:["typeParameter"],KeyofTypeAnnotation:["argument"],ObjectTypeMappedTypeProperty:["keyTparam","propType","sourceType","variance"],QualifiedTypeofIdentifier:["qualification","id"],TupleTypeLabeledElement:["label","elementType","variance"],TupleTypeSpreadElement:["label","typeAnnotation"],TypeOperator:["typeAnnotation"],TypePredicate:["parameterName","typeAnnotation","asserts"],NGRoot:["node"],NGPipeExpression:["left","right","arguments"],NGChainedExpression:["expressions"],NGEmptyExpression:[],NGMicrosyntax:["body"],NGMicrosyntaxKey:[],NGMicrosyntaxExpression:["expression","alias"],NGMicrosyntaxKeyedExpression:["key","expression"],NGMicrosyntaxLet:["key","value"],NGMicrosyntaxAs:["key","alias"],JsExpressionRoot:["node"],JsonRoot:["node"],TSJSDocAllType:[],TSJSDocUnknownType:[],TSJSDocNullableType:["typeAnnotation"],TSJSDocNonNullableType:["typeAnnotation"],NeverTypeAnnotation:[],UndefinedTypeAnnotation:[],UnknownTypeAnnotation:[],AsConstExpression:["expression"],SatisfiesExpression:["expression","typeAnnotation"]};var lo=Sr(au),Br=lo;function mo(e){let t=new Set(e);return r=>t.has(r==null?void 0:r.type)}var M=mo;var Do=M(["Block","CommentBlock","MultiLine"]),ee=Do;var yo=M(["AnyTypeAnnotation","ThisTypeAnnotation","NumberTypeAnnotation","VoidTypeAnnotation","BooleanTypeAnnotation","BigIntTypeAnnotation","SymbolTypeAnnotation","StringTypeAnnotation","NeverTypeAnnotation","UndefinedTypeAnnotation","UnknownTypeAnnotation","EmptyTypeAnnotation","MixedTypeAnnotation"]),br=yo;function fo(e,t){let r=t.split(".");for(let n=r.length-1;n>=0;n--){let s=r[n];if(n===0)return e.type==="Identifier"&&e.name===s;if(e.type!=="MemberExpression"||e.optional||e.computed||e.property.type!=="Identifier"||e.property.name!==s)return!1;e=e.object}}function Eo(e,t){return t.some(r=>fo(e,r))}var ou=Eo;function Fo({type:e}){return e.startsWith("TS")&&e.endsWith("Keyword")}var Pr=Fo;function tr(e,t){return t(e)||zs(e,{getVisitorKeys:Br,predicate:t})}function jt(e){return e.type==="AssignmentExpression"||e.type==="BinaryExpression"||e.type==="LogicalExpression"||e.type==="NGPipeExpression"||e.type==="ConditionalExpression"||w(e)||q(e)||e.type==="SequenceExpression"||e.type==="TaggedTemplateExpression"||e.type==="BindExpression"||e.type==="UpdateExpression"&&!e.prefix||Ae(e)||e.type==="TSNonNullExpression"||e.type==="ChainExpression"}function lu(e){return e.expressions?e.expressions[0]:e.left??e.test??e.callee??e.object??e.tag??e.argument??e.expression}function Ir(e){if(e.expressions)return["expressions",0];if(e.left)return["left"];if(e.test)return["test"];if(e.object)return["object"];if(e.callee)return["callee"];if(e.tag)return["tag"];if(e.argument)return["argument"];if(e.expression)return["expression"];throw new Error("Unexpected node has no left side.")}var Rt=M(["Line","CommentLine","SingleLine","HashbangComment","HTMLOpen","HTMLClose","Hashbang","InterpreterDirective"]),mu=M(["ExportDefaultDeclaration","DeclareExportDeclaration","ExportNamedDeclaration","ExportAllDeclaration","DeclareExportAllDeclaration"]),G=M(["ArrayExpression","TupleExpression"]),se=M(["ObjectExpression","RecordExpression"]);function Pe(e){return e.type==="NumericLiteral"||e.type==="Literal"&&typeof e.value=="number"}function qn(e){return e.type==="UnaryExpression"&&(e.operator==="+"||e.operator==="-")&&Pe(e.argument)}function te(e){return e.type==="StringLiteral"||e.type==="Literal"&&typeof e.value=="string"}function Wn(e){return e.type==="RegExpLiteral"||e.type==="Literal"&&!!e.regex}var rr=M(["Literal","BooleanLiteral","BigIntLiteral","DecimalLiteral","DirectiveLiteral","NullLiteral","NumericLiteral","RegExpLiteral","StringLiteral"]),Du=M(["Identifier","ThisExpression","Super","PrivateName","PrivateIdentifier","Import"]),Le=M(["ObjectTypeAnnotation","TSTypeLiteral","TSMappedType"]),Mt=M(["FunctionExpression","ArrowFunctionExpression"]);function Co(e){return e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression"&&e.body.type==="BlockStatement"}function _n(e){return w(e)&&e.callee.type==="Identifier"&&["async","inject","fakeAsync","waitForAsync"].includes(e.callee.name)}var U=M(["JSXElement","JSXFragment"]);function ht(e){return e.method&&e.kind==="init"||e.kind==="get"||e.kind==="set"}function Lr(e){return(e.type==="ObjectTypeProperty"||e.type==="ObjectTypeInternalSlot")&&!e.static&&!e.method&&e.kind!=="get"&&e.kind!=="set"&&e.value.type==="FunctionTypeAnnotation"}function yu(e){return(e.type==="TypeAnnotation"||e.type==="TSTypeAnnotation")&&e.typeAnnotation.type==="FunctionTypeAnnotation"&&!e.static&&!gt(e,e.typeAnnotation)}var me=M(["BinaryExpression","LogicalExpression","NGPipeExpression"]);function Et(e){return q(e)||e.type==="BindExpression"&&!!e.object}var Ao=M(["TSThisType","NullLiteralTypeAnnotation","BooleanLiteralTypeAnnotation","StringLiteralTypeAnnotation","BigIntLiteralTypeAnnotation","NumberLiteralTypeAnnotation","TSLiteralType","TSTemplateLiteralType"]);function Jt(e){return Pr(e)||br(e)||Ao(e)||(e.type==="GenericTypeAnnotation"||e.type==="TSTypeReference")&&!e.typeParameters}function To(e){let t=/^(?:before|after)(?:Each|All)$/;return e.callee.type==="Identifier"&&e.arguments.length===1&&t.test(e.callee.name)}var xo=["it","it.only","it.skip","describe","describe.only","describe.skip","test","test.only","test.skip","test.step","test.describe","test.describe.only","test.describe.parallel","test.describe.parallel.only","test.describe.serial","test.describe.serial.only","skip","xit","xdescribe","xtest","fit","fdescribe","ftest"];function go(e){return ou(e,xo)}function St(e,t){if(e.type!=="CallExpression")return!1;if(e.arguments.length===1){if(_n(e)&&t&&St(t))return Mt(e.arguments[0]);if(To(e))return _n(e.arguments[0])}else if((e.arguments.length===2||e.arguments.length===3)&&(e.arguments[0].type==="TemplateLiteral"||te(e.arguments[0]))&&go(e.callee))return e.arguments[2]&&!Pe(e.arguments[2])?!1:(e.arguments.length===2?Mt(e.arguments[1]):Co(e.arguments[1])&&$(e.arguments[1]).length<=1)||_n(e.arguments[1]);return!1}var fu=e=>t=>((t==null?void 0:t.type)==="ChainExpression"&&(t=t.expression),e(t)),w=fu(M(["CallExpression","OptionalCallExpression"])),q=fu(M(["MemberExpression","OptionalMemberExpression"]));function Eu(e){let t="expressions";e.type==="TSTemplateLiteralType"&&(t="types");let r=e[t];return r.length===0?!1:r.every(n=>{if(Rn(n)||Fu(n))return!0})}function Fu(e,{maxDepth:t=Number.POSITIVE_INFINITY}={}){if(A(e))return!1;if(e.type==="ChainExpression")return Fu(e.expression,{maxDepth:t});if(!q(e))return!1;let r=e,n=0;for(;q(r)&&n++<=t;)if(!Rn(r.property)||(r=r.object,A(r)))return!1;return Rn(r)}function Rn(e){return A(e)?!1:rr(e)||Du(e)}function Nn(e,t=5){return Cu(e,t)<=t}function Cu(e,t){let r=0;for(let n in e){let s=e[n];if(s&&typeof s=="object"&&typeof s.type=="string"&&(r++,r+=Cu(s,t-r)),r>t)return r}return r}var ho=.25;function nr(e,t){let{printWidth:r}=t;if(A(e))return!1;let n=r*ho;if(e.type==="ThisExpression"||e.type==="Identifier"&&e.name.length<=n||qn(e)&&!A(e.argument))return!0;let s=e.type==="Literal"&&"regex"in e&&e.regex.pattern||e.type==="RegExpLiteral"&&e.pattern;return s?s.length<=n:te(e)?at(De(e),t).length<=n:e.type==="TemplateLiteral"?e.expressions.length===0&&e.quasis[0].value.raw.length<=n&&!e.quasis[0].value.raw.includes(`
`):e.type==="UnaryExpression"?nr(e.argument,{printWidth:r}):e.type==="CallExpression"&&e.arguments.length===0&&e.callee.type==="Identifier"?e.callee.name.length<=n-2:rr(e)}function we(e,t){return U(t)?Bt(t):A(t,x.Leading,r=>Z(e,I(r)))}function Gn(e,t){return t.parser!=="json"&&t.parser!=="jsonc"&&te(e.key)&&De(e.key).slice(1,-1)===e.key.value&&(Hs(e.key.value)&&!(t.parser==="babel-ts"&&e.type==="ClassProperty"||t.parser==="typescript"&&e.type==="PropertyDefinition")||Un(e.key.value)&&String(Number(e.key.value))===e.key.value&&(t.parser==="babel"||t.parser==="acorn"||t.parser==="espree"||t.parser==="meriyah"||t.parser==="__babel_estree"))}function Un(e){return/^(?:\d+|\d+\.\d+)$/.test(e)}function pu(e){return e.quasis.some(t=>t.value.raw.includes(`
`))}function wr(e,t){return(e.type==="TemplateLiteral"&&pu(e)||e.type==="TaggedTemplateExpression"&&pu(e.quasi))&&!Z(t,j(e),{backwards:!0})}function Or(e){if(!A(e))return!1;let t=v(!1,ot(e,x.Dangling),-1);return t&&!ee(t)}function Au(e){if(e.length<=1)return!1;let t=0;for(let r of e)if(Mt(r)){if(t+=1,t>1)return!0}else if(w(r)){for(let n of ae(r))if(Mt(n))return!0}return!1}function vr(e){let{node:t,parent:r,key:n}=e;return n==="callee"&&w(t)&&w(r)&&r.arguments.length>0&&t.arguments.length>r.arguments.length}var So=new Set(["!","-","+","~"]);function be(e,t=2){if(t<=0)return!1;if(e.type==="ChainExpression"||e.type==="TSNonNullExpression")return be(e.expression,t);let r=n=>be(n,t-1);if(Wn(e))return Ze(e.pattern??e.regex.pattern)<=5;if(rr(e)||Du(e)||e.type==="ArgumentPlaceholder")return!0;if(e.type==="TemplateLiteral")return e.quasis.every(n=>!n.value.raw.includes(`
`))&&e.expressions.every(r);if(se(e))return e.properties.every(n=>!n.computed&&(n.shorthand||n.value&&r(n.value)));if(G(e))return e.elements.every(n=>n===null||r(n));if(pt(e)){if(e.type==="ImportExpression"||be(e.callee,t)){let n=ae(e);return n.length<=t&&n.every(r)}return!1}return q(e)?be(e.object,t)&&be(e.property,t):e.type==="UnaryExpression"&&So.has(e.operator)||e.type==="UpdateExpression"?be(e.argument,t):!1}function De(e){var t;return((t=e.extra)==null?void 0:t.raw)??e.raw}function du(e){return e}function ye(e,t="es5"){return e.trailingComma==="es5"&&t==="es5"||e.trailingComma==="all"&&(t==="all"||t==="es5")}function ie(e,t){switch(e.type){case"BinaryExpression":case"LogicalExpression":case"AssignmentExpression":case"NGPipeExpression":return ie(e.left,t);case"MemberExpression":case"OptionalMemberExpression":return ie(e.object,t);case"TaggedTemplateExpression":return e.tag.type==="FunctionExpression"?!1:ie(e.tag,t);case"CallExpression":case"OptionalCallExpression":return e.callee.type==="FunctionExpression"?!1:ie(e.callee,t);case"ConditionalExpression":return ie(e.test,t);case"UpdateExpression":return!e.prefix&&ie(e.argument,t);case"BindExpression":return e.object&&ie(e.object,t);case"SequenceExpression":return ie(e.expressions[0],t);case"ChainExpression":case"TSSatisfiesExpression":case"TSAsExpression":case"TSNonNullExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":return ie(e.expression,t);default:return t(e)}}var cu={"==":!0,"!=":!0,"===":!0,"!==":!0},kr={"*":!0,"/":!0,"%":!0},Jn={">>":!0,">>>":!0,"<<":!0};function sr(e,t){return!(er(t)!==er(e)||e==="**"||cu[e]&&cu[t]||t==="%"&&kr[e]||e==="%"&&kr[t]||t!==e&&kr[t]&&kr[e]||Jn[e]&&Jn[t])}var Bo=new Map([["|>"],["??"],["||"],["&&"],["|"],["^"],["&"],["==","===","!=","!=="],["<",">","<=",">=","in","instanceof"],[">>","<<",">>>"],["+","-"],["*","/","%"],["**"]].flatMap((e,t)=>e.map(r=>[r,t])));function er(e){return Bo.get(e)}function Tu(e){return!!Jn[e]||e==="|"||e==="^"||e==="&"}function xu(e){var r;if(e.rest)return!0;let t=$(e);return((r=v(!1,t,-1))==null?void 0:r.type)==="RestElement"}var Mn=new WeakMap;function $(e){if(Mn.has(e))return Mn.get(e);let t=[];return e.this&&t.push(e.this),Array.isArray(e.parameters)?t.push(...e.parameters):Array.isArray(e.params)&&t.push(...e.params),e.rest&&t.push(e.rest),Mn.set(e,t),t}function gu(e,t){let{node:r}=e,n=0,s=u=>t(u,n++);r.this&&e.call(s,"this"),Array.isArray(r.parameters)?e.each(s,"parameters"):Array.isArray(r.params)&&e.each(s,"params"),r.rest&&e.call(s,"rest")}var jn=new WeakMap;function ae(e){if(jn.has(e))return jn.get(e);if(e.type==="ChainExpression")return ae(e.expression);let t=e.arguments;return e.type==="ImportExpression"&&(t=[e.source],e.attributes&&t.push(e.attributes),e.options&&t.push(e.options)),jn.set(e,t),t}function ur(e,t){let{node:r}=e;if(r.type==="ChainExpression")return e.call(()=>ur(e,t),"expression");r.type==="ImportExpression"?(e.call(n=>t(n,0),"source"),r.attributes&&e.call(n=>t(n,1),"attributes"),r.options&&e.call(n=>t(n,1),"options")):e.each(t,"arguments")}function Yn(e,t){let r=[];if(e.type==="ChainExpression"&&r.push("expression"),e.type==="ImportExpression"){if(t===0||t===(e.attributes||e.options?-2:-1))return[...r,"source"];if(e.attributes&&(t===1||t===-1))return[...r,"attributes"];if(e.options&&(t===1||t===-1))return[...r,"options"];throw new RangeError("Invalid argument index")}if(t<0&&(t=e.arguments.length+t),t<0||t>=e.arguments.length)throw new RangeError("Invalid argument index");return[...r,"arguments",t]}function ir(e){return e.value.trim()==="prettier-ignore"&&!e.unignore}function Bt(e){return(e==null?void 0:e.prettierIgnore)||A(e,x.PrettierIgnore)}var x={Leading:2,Trailing:4,Dangling:8,Block:16,Line:32,PrettierIgnore:64,First:128,Last:256},hu=(e,t)=>{if(typeof e=="function"&&(t=e,e=0),e||t)return(r,n,s)=>!(e&x.Leading&&!r.leading||e&x.Trailing&&!r.trailing||e&x.Dangling&&(r.leading||r.trailing)||e&x.Block&&!ee(r)||e&x.Line&&!Rt(r)||e&x.First&&n!==0||e&x.Last&&n!==s.length-1||e&x.PrettierIgnore&&!ir(r)||t&&!t(r))};function A(e,t,r){if(!L(e==null?void 0:e.comments))return!1;let n=hu(t,r);return n?e.comments.some(n):!0}function ot(e,t,r){if(!Array.isArray(e==null?void 0:e.comments))return[];let n=hu(t,r);return n?e.comments.filter(n):e.comments}var fe=(e,{originalText:t})=>_t(t,I(e));function pt(e){return w(e)||e.type==="NewExpression"||e.type==="ImportExpression"}function Ce(e){return e&&(e.type==="ObjectProperty"||e.type==="Property"&&!ht(e))}var Ae=M(["TSAsExpression","TSSatisfiesExpression","AsExpression","AsConstExpression","SatisfiesExpression"]),We=M(["UnionTypeAnnotation","TSUnionType"]),_r=M(["IntersectionTypeAnnotation","TSIntersectionType"]);var bo=new Set(["range","raw","comments","leadingComments","trailingComments","innerComments","extra","start","end","loc","flags","errors","tokens"]),qt=e=>{for(let t of e.quasis)delete t.value};function Su(e,t,r){var s,u;if(e.type==="Program"&&delete t.sourceType,(e.type==="BigIntLiteral"||e.type==="BigIntLiteralTypeAnnotation")&&t.value&&(t.value=t.value.toLowerCase()),(e.type==="BigIntLiteral"||e.type==="Literal")&&t.bigint&&(t.bigint=t.bigint.toLowerCase()),e.type==="DecimalLiteral"&&(t.value=Number(t.value)),e.type==="Literal"&&t.decimal&&(t.decimal=Number(t.decimal)),e.type==="EmptyStatement"||e.type==="JSXText"||e.type==="JSXExpressionContainer"&&(e.expression.type==="Literal"||e.expression.type==="StringLiteral")&&e.expression.value===" ")return null;if((e.type==="Property"||e.type==="ObjectProperty"||e.type==="MethodDefinition"||e.type==="ClassProperty"||e.type==="ClassMethod"||e.type==="PropertyDefinition"||e.type==="TSDeclareMethod"||e.type==="TSPropertySignature"||e.type==="ObjectTypeProperty")&&typeof e.key=="object"&&e.key&&(e.key.type==="Literal"||e.key.type==="NumericLiteral"||e.key.type==="StringLiteral"||e.key.type==="Identifier")&&delete t.key,e.type==="JSXElement"&&e.openingElement.name.name==="style"&&e.openingElement.attributes.some(i=>i.type==="JSXAttribute"&&i.name.name==="jsx"))for(let{type:i,expression:a}of t.children)i==="JSXExpressionContainer"&&a.type==="TemplateLiteral"&&qt(a);e.type==="JSXAttribute"&&e.name.name==="css"&&e.value.type==="JSXExpressionContainer"&&e.value.expression.type==="TemplateLiteral"&&qt(t.value.expression),e.type==="JSXAttribute"&&((s=e.value)==null?void 0:s.type)==="Literal"&&/["']|&quot;|&apos;/.test(e.value.value)&&(t.value.value=H(!1,t.value.value,/["']|&quot;|&apos;/g,'"'));let n=e.expression||e.callee;if(e.type==="Decorator"&&n.type==="CallExpression"&&n.callee.name==="Component"&&n.arguments.length===1){let i=e.expression.arguments[0].properties;for(let[a,o]of t.expression.arguments[0].properties.entries())switch(i[a].key.name){case"styles":G(o.value)&&qt(o.value.elements[0]);break;case"template":o.value.type==="TemplateLiteral"&&qt(o.value);break}}if(e.type==="TaggedTemplateExpression"&&(e.tag.type==="MemberExpression"||e.tag.type==="Identifier"&&(e.tag.name==="gql"||e.tag.name==="graphql"||e.tag.name==="css"||e.tag.name==="md"||e.tag.name==="markdown"||e.tag.name==="html")||e.tag.type==="CallExpression")&&qt(t.quasi),e.type==="TemplateLiteral"&&((u=e.leadingComments)!=null&&u.some(a=>ee(a)&&["GraphQL","HTML"].some(o=>a.value===` ${o} `))||r.type==="CallExpression"&&r.callee.name==="graphql"||!e.leadingComments)&&qt(t),(e.type==="TSIntersectionType"||e.type==="TSUnionType")&&e.types.length===1)return t.types[0];e.type==="ChainExpression"&&e.expression.type==="TSNonNullExpression"&&([t.type,t.expression.type]=[t.expression.type,t.type])}Su.ignoredProperties=bo;var Bu=Su;var et="string",Ne="array",tt="cursor",Ge="indent",Ue="align",rt="trim",pe="group",de="fill",xe="if-break",Ye="indent-if-break",Xe="line-suffix",Ve="line-suffix-boundary",ce="line",Oe="label",ve="break-parent",Mr=new Set([tt,Ge,Ue,rt,pe,de,xe,Ye,Xe,Ve,ce,Oe,ve]);function Po(e){if(typeof e=="string")return et;if(Array.isArray(e))return Ne;if(!e)return;let{type:t}=e;if(Mr.has(t))return t}var nt=Po;var ko=e=>new Intl.ListFormat("en-US",{type:"disjunction"}).format(e);function Io(e){let t=e===null?"null":typeof e;if(t!=="string"&&t!=="object")return`Unexpected doc '${t}', 
Expected it to be 'string' or 'object'.`;if(nt(e))throw new Error("doc is valid.");let r=Object.prototype.toString.call(e);if(r!=="[object Object]")return`Unexpected doc '${r}'.`;let n=ko([...Mr].map(s=>`'${s}'`));return`Unexpected doc.type '${e.type}'.
Expected it to be ${n}.`}var Xn=class extends Error{name="InvalidDocError";constructor(t){super(Io(t)),this.doc=t}},Ft=Xn;var bu={};function Lo(e,t,r,n){let s=[e];for(;s.length>0;){let u=s.pop();if(u===bu){r(s.pop());continue}r&&s.push(u,bu);let i=nt(u);if(!i)throw new Ft(u);if((t==null?void 0:t(u))!==!1)switch(i){case Ne:case de:{let a=i===Ne?u:u.parts;for(let o=a.length,p=o-1;p>=0;--p)s.push(a[p]);break}case xe:s.push(u.flatContents,u.breakContents);break;case pe:if(n&&u.expandedStates)for(let a=u.expandedStates.length,o=a-1;o>=0;--o)s.push(u.expandedStates[o]);else s.push(u.contents);break;case Ue:case Ge:case Ye:case Oe:case Xe:s.push(u.contents);break;case et:case tt:case rt:case Ve:case ce:case ve:break;default:throw new Ft(u)}}}var Vn=Lo;var Pu=()=>{},$e=Pu,jr=Pu;function E(e){return $e(e),{type:Ge,contents:e}}function ge(e,t){return $e(t),{type:Ue,contents:t,n:e}}function D(e,t={}){return $e(e),jr(t.expandedStates,!0),{type:pe,id:t.id,contents:e,break:!!t.shouldBreak,expandedStates:t.expandedStates}}function ku(e){return ge(Number.NEGATIVE_INFINITY,e)}function Rr(e){return ge(-1,e)}function He(e,t){return D(e[0],{...t,expandedStates:e})}function Wt(e){return jr(e),{type:de,parts:e}}function b(e,t="",r={}){return $e(e),t!==""&&$e(t),{type:xe,breakContents:e,flatContents:t,groupId:r.groupId}}function Ct(e,t){return $e(e),{type:Ye,contents:e,groupId:t.groupId,negate:t.negate}}function $n(e){return $e(e),{type:Xe,contents:e}}var ke={type:Ve},Ee={type:ve};var Hn={type:ce,hard:!0},wo={type:ce,hard:!0,literal:!0},d={type:ce},F={type:ce,soft:!0},C=[Hn,Ee],Jr=[wo,Ee],Kn={type:tt};function P(e,t){$e(e),jr(t);let r=[];for(let n=0;n<t.length;n++)n!==0&&r.push(e),r.push(t[n]);return r}function Iu(e,t,r){$e(e);let n=e;if(t>0){for(let s=0;s<Math.floor(t/r);++s)n=E(n);n=ge(t%r,n),n=ge(Number.NEGATIVE_INFINITY,n)}return n}function st(e,t){return $e(t),e?{type:Oe,label:e,contents:t}:t}var qr=e=>{if(Array.isArray(e))return e;if(e.type!==de)throw new Error(`Expect doc to be 'array' or '${de}'.`);return e.parts};function ct(e,t){if(typeof e=="string")return t(e);let r=new Map;return n(e);function n(u){if(r.has(u))return r.get(u);let i=s(u);return r.set(u,i),i}function s(u){switch(nt(u)){case Ne:return t(u.map(n));case de:return t({...u,parts:u.parts.map(n)});case xe:return t({...u,breakContents:n(u.breakContents),flatContents:n(u.flatContents)});case pe:{let{expandedStates:i,contents:a}=u;return i?(i=i.map(n),a=i[0]):a=n(a),t({...u,contents:a,expandedStates:i})}case Ue:case Ge:case Ye:case Oe:case Xe:return t({...u,contents:n(u.contents)});case et:case tt:case rt:case Ve:case ce:case ve:return t(u);default:throw new Ft(u)}}}function wu(e,t,r){let n=r,s=!1;function u(i){if(s)return!1;let a=t(i);a!==void 0&&(s=!0,n=a)}return Vn(e,u),n}function Oo(e){if(e.type===pe&&e.break||e.type===ce&&e.hard||e.type===ve)return!0}function re(e){return wu(e,Oo,!1)}function Lu(e){if(e.length>0){let t=v(!1,e,-1);!t.expandedStates&&!t.break&&(t.break="propagated")}return null}function Ou(e){let t=new Set,r=[];function n(u){if(u.type===ve&&Lu(r),u.type===pe){if(r.push(u),t.has(u))return!1;t.add(u)}}function s(u){u.type===pe&&r.pop().break&&Lu(r)}Vn(e,n,s,!0)}function vo(e){return e.type===ce&&!e.hard?e.soft?"":" ":e.type===xe?e.flatContents:e}function ar(e){return ct(e,vo)}function _o(e){switch(nt(e)){case de:if(e.parts.every(t=>t===""))return"";break;case pe:if(!e.contents&&!e.id&&!e.break&&!e.expandedStates)return"";if(e.contents.type===pe&&e.contents.id===e.id&&e.contents.break===e.break&&e.contents.expandedStates===e.expandedStates)return e.contents;break;case Ue:case Ge:case Ye:case Xe:if(!e.contents)return"";break;case xe:if(!e.flatContents&&!e.breakContents)return"";break;case Ne:{let t=[];for(let r of e){if(!r)continue;let[n,...s]=Array.isArray(r)?r:[r];typeof n=="string"&&typeof v(!1,t,-1)=="string"?t[t.length-1]+=n:t.push(n),t.push(...s)}return t.length===0?"":t.length===1?t[0]:t}case et:case tt:case rt:case Ve:case ce:case Oe:case ve:break;default:throw new Ft(e)}return e}function Nt(e){return ct(e,t=>_o(t))}function Ie(e,t=Jr){return ct(e,r=>typeof r=="string"?P(t,r.split(`
`)):r)}function Mo(e){if(e.type===ce)return!0}function vu(e){return wu(e,Mo,!1)}function or(e,t){return e.type===Oe?{...e,contents:t(e.contents)}:t(e)}function jo(e){let t=`*${e.value}*`.split(`
`);return t.length>1&&t.every(r=>r.trimStart()[0]==="*")}var _u=jo;function Mu(e,t){let r=e.node;if(Rt(r))return t.originalText.slice(j(r),I(r)).trimEnd();if(ee(r))return _u(r)?Ro(r):["/*",Ie(r.value),"*/"];throw new Error("Not a comment: "+JSON.stringify(r))}function Ro(e){let t=e.value.split(`
`);return["/*",P(C,t.map((r,n)=>n===0?r.trimEnd():" "+(n<t.length-1?r.trim():r.trimStart()))),"*/"]}var ns={};Tr(ns,{endOfLine:()=>Yo,ownLine:()=>Uo,remaining:()=>Xo});function Jo(e){let t=e.type||e.kind||"(unknown type)",r=String(e.name||e.id&&(typeof e.id=="object"?e.id.name:e.id)||e.key&&(typeof e.key=="object"?e.key.name:e.key)||e.value&&(typeof e.value=="object"?"":String(e.value))||e.operator||"");return r.length>20&&(r=r.slice(0,19)+"\u2026"),t+(r?" "+r:"")}function zn(e,t){(e.comments??(e.comments=[])).push(t),t.printed=!1,t.nodeDescription=Jo(e)}function oe(e,t){t.leading=!0,t.trailing=!1,zn(e,t)}function he(e,t,r){t.leading=!1,t.trailing=!1,r&&(t.marker=r),zn(e,t)}function K(e,t){t.leading=!1,t.trailing=!0,zn(e,t)}function qo(e,t){let r=null,n=t;for(;n!==r;)r=n,n=Je(e,n),n=Ot(e,n),n=vt(e,n),n=qe(e,n);return n}var lt=qo;function Wo(e,t){let r=lt(e,t);return r===!1?"":e.charAt(r)}var Ke=Wo;function No(e,t,r){for(let n=t;n<r;++n)if(e.charAt(n)===`
`)return!0;return!1}var Te=No;function Go(e){return ee(e)&&e.value[0]==="*"&&/@(?:type|satisfies)\b/.test(e.value)}var ju=Go;function Uo(e){return[Yu,Ju,Nu,$o,Zn,es,Ru,qu,ip,sp,rs,Uu,ap,Wu,Gu,ts,Ho,fp].some(t=>t(e))}function Yo(e){return[Vo,Nu,Ju,Uu,Zn,es,Ru,qu,Gu,np,up,rs,cp,ts,Dp,yp].some(t=>t(e))}function Xo(e){return[Yu,Zn,es,Ko,rp,Wu,rs,tp,ep,mp,ts,lp].some(t=>t(e))}function bt(e,t){let r=(e.body||e.properties).find(({type:n})=>n!=="EmptyStatement");r?oe(r,t):he(e,t)}function Qn(e,t){e.type==="BlockStatement"?bt(e,t):oe(e,t)}function Vo({comment:e,followingNode:t}){return t&&ju(e)?(oe(t,e),!0):!1}function Zn({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){if((r==null?void 0:r.type)!=="IfStatement"||!n)return!1;if(Ke(s,I(e))===")")return K(t,e),!0;if(t===r.consequent&&n===r.alternate){if(t.type==="BlockStatement")K(t,e);else{let i=Rt(e)||e.loc.start.line===e.loc.end.line,a=e.loc.start.line===t.loc.start.line;i&&a?K(t,e):he(r,e)}return!0}return n.type==="BlockStatement"?(bt(n,e),!0):n.type==="IfStatement"?(Qn(n.consequent,e),!0):r.consequent===n?(oe(n,e),!0):!1}function es({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){return(r==null?void 0:r.type)!=="WhileStatement"||!n?!1:Ke(s,I(e))===")"?(K(t,e),!0):n.type==="BlockStatement"?(bt(n,e),!0):r.body===n?(oe(n,e),!0):!1}function Ru({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){return(r==null?void 0:r.type)!=="TryStatement"&&(r==null?void 0:r.type)!=="CatchClause"||!n?!1:r.type==="CatchClause"&&t?(K(t,e),!0):n.type==="BlockStatement"?(bt(n,e),!0):n.type==="TryStatement"?(Qn(n.finalizer,e),!0):n.type==="CatchClause"?(Qn(n.body,e),!0):!1}function $o({comment:e,enclosingNode:t,followingNode:r}){return q(t)&&(r==null?void 0:r.type)==="Identifier"?(oe(t,e),!0):!1}function Ho({comment:e,enclosingNode:t,followingNode:r,options:n}){return!n.experimentalTernaries||!((t==null?void 0:t.type)==="ConditionalExpression"||(t==null?void 0:t.type)==="ConditionalTypeAnnotation"||(t==null?void 0:t.type)==="TSConditionalType")?!1:(r==null?void 0:r.type)==="ConditionalExpression"||(r==null?void 0:r.type)==="ConditionalTypeAnnotation"||(r==null?void 0:r.type)==="TSConditionalType"?(he(t,e),!0):!1}function Ju({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s,options:u}){let i=t&&!Te(s,I(t),j(e));return(!t||!i)&&((r==null?void 0:r.type)==="ConditionalExpression"||(r==null?void 0:r.type)==="ConditionalTypeAnnotation"||(r==null?void 0:r.type)==="TSConditionalType")&&n?u.experimentalTernaries&&r.alternate===n&&!(ee(e)&&!Te(u.originalText,j(e),I(e)))?(he(r,e),!0):(oe(n,e),!0):!1}function Ko({comment:e,precedingNode:t,enclosingNode:r}){return Ce(r)&&r.shorthand&&r.key===t&&r.value.type==="AssignmentPattern"?(K(r.value.left,e),!0):!1}var zo=new Set(["ClassDeclaration","ClassExpression","DeclareClass","DeclareInterface","InterfaceDeclaration","TSInterfaceDeclaration"]);function qu({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){if(zo.has(r==null?void 0:r.type)){if(L(r.decorators)&&(n==null?void 0:n.type)!=="Decorator")return K(v(!1,r.decorators,-1),e),!0;if(r.body&&n===r.body)return bt(r.body,e),!0;if(n){if(r.superClass&&n===r.superClass&&t&&(t===r.id||t===r.typeParameters))return K(t,e),!0;for(let s of["implements","extends","mixins"])if(r[s]&&n===r[s][0])return t&&(t===r.id||t===r.typeParameters||t===r.superClass)?K(t,e):he(r,e,s),!0}}return!1}var Qo=new Set(["ClassMethod","ClassProperty","PropertyDefinition","TSAbstractPropertyDefinition","TSAbstractMethodDefinition","TSDeclareMethod","MethodDefinition","ClassAccessorProperty","AccessorProperty","TSAbstractAccessorProperty"]);function Wu({comment:e,precedingNode:t,enclosingNode:r,text:n}){return r&&t&&Ke(n,I(e))==="("&&(r.type==="Property"||r.type==="TSDeclareMethod"||r.type==="TSAbstractMethodDefinition")&&t.type==="Identifier"&&r.key===t&&Ke(n,I(t))!==":"?(K(t,e),!0):(t==null?void 0:t.type)==="Decorator"&&Qo.has(r==null?void 0:r.type)?(K(t,e),!0):!1}var Zo=new Set(["FunctionDeclaration","FunctionExpression","ClassMethod","MethodDefinition","ObjectMethod"]);function ep({comment:e,precedingNode:t,enclosingNode:r,text:n}){return Ke(n,I(e))!=="("?!1:t&&Zo.has(r==null?void 0:r.type)?(K(t,e),!0):!1}function tp({comment:e,enclosingNode:t,text:r}){if((t==null?void 0:t.type)!=="ArrowFunctionExpression")return!1;let n=lt(r,I(e));return n!==!1&&r.slice(n,n+2)==="=>"?(he(t,e),!0):!1}function rp({comment:e,enclosingNode:t,text:r}){return Ke(r,I(e))!==")"?!1:t&&(Xu(t)&&$(t).length===0||pt(t)&&ae(t).length===0)?(he(t,e),!0):((t==null?void 0:t.type)==="MethodDefinition"||(t==null?void 0:t.type)==="TSAbstractMethodDefinition")&&$(t.value).length===0?(he(t.value,e),!0):!1}function Nu({comment:e,precedingNode:t,enclosingNode:r,followingNode:n,text:s}){return(t==null?void 0:t.type)==="FunctionTypeParam"&&(r==null?void 0:r.type)==="FunctionTypeAnnotation"&&(n==null?void 0:n.type)!=="FunctionTypeParam"?(K(t,e),!0):((t==null?void 0:t.type)==="Identifier"||(t==null?void 0:t.type)==="AssignmentPattern"||(t==null?void 0:t.type)==="ObjectPattern"||(t==null?void 0:t.type)==="ArrayPattern"||(t==null?void 0:t.type)==="RestElement"||(t==null?void 0:t.type)==="TSParameterProperty")&&Xu(r)&&Ke(s,I(e))===")"?(K(t,e),!0):!ee(e)&&((r==null?void 0:r.type)==="FunctionDeclaration"||(r==null?void 0:r.type)==="FunctionExpression"||(r==null?void 0:r.type)==="ObjectMethod")&&(n==null?void 0:n.type)==="BlockStatement"&&r.body===n&&lt(s,I(e))===j(n)?(bt(n,e),!0):!1}function Gu({comment:e,enclosingNode:t}){return(t==null?void 0:t.type)==="LabeledStatement"?(oe(t,e),!0):!1}function ts({comment:e,enclosingNode:t}){return((t==null?void 0:t.type)==="ContinueStatement"||(t==null?void 0:t.type)==="BreakStatement")&&!t.label?(K(t,e),!0):!1}function np({comment:e,precedingNode:t,enclosingNode:r}){return w(r)&&t&&r.callee===t&&r.arguments.length>0?(oe(r.arguments[0],e),!0):!1}function sp({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){return We(r)?(ir(e)&&(n.prettierIgnore=!0,e.unignore=!0),t?(K(t,e),!0):!1):(We(n)&&ir(e)&&(n.types[0].prettierIgnore=!0,e.unignore=!0),!1)}function up({comment:e,enclosingNode:t}){return Ce(t)?(oe(t,e),!0):!1}function rs({comment:e,enclosingNode:t,followingNode:r,ast:n,isLastComment:s}){var u;return((u=n==null?void 0:n.body)==null?void 0:u.length)===0?(s?he(n,e):oe(n,e),!0):(t==null?void 0:t.type)==="Program"&&t.body.length===0&&!L(t.directives)?(s?he(t,e):oe(t,e),!0):(r==null?void 0:r.type)==="Program"&&r.body.length===0&&(t==null?void 0:t.type)==="ModuleExpression"?(he(r,e),!0):!1}function ip({comment:e,enclosingNode:t}){return(t==null?void 0:t.type)==="ForInStatement"||(t==null?void 0:t.type)==="ForOfStatement"?(oe(t,e),!0):!1}function Uu({comment:e,precedingNode:t,enclosingNode:r,text:n}){if((r==null?void 0:r.type)==="ImportSpecifier"||(r==null?void 0:r.type)==="ExportSpecifier")return oe(r,e),!0;let s=(t==null?void 0:t.type)==="ImportSpecifier"&&(r==null?void 0:r.type)==="ImportDeclaration",u=(t==null?void 0:t.type)==="ExportSpecifier"&&(r==null?void 0:r.type)==="ExportNamedDeclaration";return(s||u)&&Z(n,I(e))?(K(t,e),!0):!1}function ap({comment:e,enclosingNode:t}){return(t==null?void 0:t.type)==="AssignmentPattern"?(oe(t,e),!0):!1}var op=new Set(["VariableDeclarator","AssignmentExpression","TypeAlias","TSTypeAliasDeclaration"]),pp=new Set(["ObjectExpression","RecordExpression","ArrayExpression","TupleExpression","TemplateLiteral","TaggedTemplateExpression","ObjectTypeAnnotation","TSTypeLiteral"]);function cp({comment:e,enclosingNode:t,followingNode:r}){return op.has(t==null?void 0:t.type)&&r&&(pp.has(r.type)||ee(e))?(oe(r,e),!0):!1}function lp({comment:e,enclosingNode:t,followingNode:r,text:n}){return!r&&((t==null?void 0:t.type)==="TSMethodSignature"||(t==null?void 0:t.type)==="TSDeclareFunction"||(t==null?void 0:t.type)==="TSAbstractMethodDefinition")&&Ke(n,I(e))===";"?(K(t,e),!0):!1}function Yu({comment:e,enclosingNode:t,followingNode:r}){if(ir(e)&&(t==null?void 0:t.type)==="TSMappedType"&&(r==null?void 0:r.type)==="TSTypeParameter"&&r.constraint)return t.prettierIgnore=!0,e.unignore=!0,!0}function mp({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){return(r==null?void 0:r.type)!=="TSMappedType"?!1:(n==null?void 0:n.type)==="TSTypeParameter"&&n.name?(oe(n.name,e),!0):(t==null?void 0:t.type)==="TSTypeParameter"&&t.constraint?(K(t.constraint,e),!0):!1}function Dp({comment:e,enclosingNode:t,followingNode:r}){return!t||t.type!=="SwitchCase"||t.test||!r||r!==t.consequent[0]?!1:(r.type==="BlockStatement"&&Rt(e)?bt(r,e):he(t,e),!0)}function yp({comment:e,precedingNode:t,enclosingNode:r,followingNode:n}){return We(t)&&((r.type==="TSArrayType"||r.type==="ArrayTypeAnnotation")&&!n||_r(r))?(K(v(!1,t.types,-1),e),!0):!1}function fp({comment:e,enclosingNode:t,precedingNode:r,followingNode:n}){if(((t==null?void 0:t.type)==="ObjectPattern"||(t==null?void 0:t.type)==="ArrayPattern")&&(n==null?void 0:n.type)==="TSTypeAnnotation")return r?K(r,e):he(t,e),!0}var Xu=M(["ArrowFunctionExpression","FunctionExpression","FunctionDeclaration","ObjectMethod","ClassMethod","TSDeclareFunction","TSCallSignatureDeclaration","TSConstructSignatureDeclaration","TSMethodSignature","TSConstructorType","TSFunctionType","TSDeclareMethod"]);var Ep=new Set(["EmptyStatement","TemplateElement","Import","TSEmptyBodyFunctionExpression","ChainExpression"]);function Fp(e){return!Ep.has(e.type)}function Cp(e,t){var r;if((t.parser==="typescript"||t.parser==="flow"||t.parser==="acorn"||t.parser==="espree"||t.parser==="meriyah"||t.parser==="__babel_estree")&&e.type==="MethodDefinition"&&((r=e.value)==null?void 0:r.type)==="FunctionExpression"&&$(e.value).length===0&&!e.value.returnType&&!L(e.value.typeParameters)&&e.value.body)return[...e.decorators||[],e.key,e.value.body]}function ss(e){let{node:t,parent:r}=e;return(U(t)||r&&(r.type==="JSXSpreadAttribute"||r.type==="JSXSpreadChild"||We(r)||(r.type==="ClassDeclaration"||r.type==="ClassExpression")&&r.superClass===t))&&(!Bt(t)||We(r))}function Ap(e,{parser:t}){if(t==="flow"||t==="babel-flow")return e=H(!1,e,/[\s(]/g,""),e===""||e==="/*"||e==="/*::"}function Vu(e){switch(e){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}var Se=Symbol("MODE_BREAK"),ut=Symbol("MODE_FLAT"),pr=Symbol("cursor");function $u(){return{value:"",length:0,queue:[]}}function dp(e,t){return us(e,{type:"indent"},t)}function Tp(e,t,r){return t===Number.NEGATIVE_INFINITY?e.root||$u():t<0?us(e,{type:"dedent"},r):t?t.type==="root"?{...e,root:e}:us(e,{type:typeof t=="string"?"stringAlign":"numberAlign",n:t},r):e}function us(e,t,r){let n=t.type==="dedent"?e.queue.slice(0,-1):[...e.queue,t],s="",u=0,i=0,a=0;for(let l of n)switch(l.type){case"indent":m(),r.useTabs?o(1):p(r.tabWidth);break;case"stringAlign":m(),s+=l.n,u+=l.n.length;break;case"numberAlign":i+=1,a+=l.n;break;default:throw new Error(`Unexpected type '${l.type}'`)}return c(),{...e,value:s,length:u,queue:n};function o(l){s+="	".repeat(l),u+=r.tabWidth*l}function p(l){s+=" ".repeat(l),u+=l}function m(){r.useTabs?y():c()}function y(){i>0&&o(i),f()}function c(){a>0&&p(a),f()}function f(){i=0,a=0}}function is(e){let t=0,r=0,n=e.length;e:for(;n--;){let s=e[n];if(s===pr){r++;continue}for(let u=s.length-1;u>=0;u--){let i=s[u];if(i===" "||i==="	")t++;else{e[n]=s.slice(0,u+1);break e}}}if(t>0||r>0)for(e.length=n+1;r-- >0;)e.push(pr);return t}function Wr(e,t,r,n,s,u){if(r===Number.POSITIVE_INFINITY)return!0;let i=t.length,a=[e],o=[];for(;r>=0;){if(a.length===0){if(i===0)return!0;a.push(t[--i]);continue}let{mode:p,doc:m}=a.pop();switch(nt(m)){case et:o.push(m),r-=Ze(m);break;case Ne:case de:{let y=qr(m);for(let c=y.length-1;c>=0;c--)a.push({mode:p,doc:y[c]});break}case Ge:case Ue:case Ye:case Oe:a.push({mode:p,doc:m.contents});break;case rt:r+=is(o);break;case pe:{if(u&&m.break)return!1;let y=m.break?Se:p,c=m.expandedStates&&y===Se?v(!1,m.expandedStates,-1):m.contents;a.push({mode:y,doc:c});break}case xe:{let c=(m.groupId?s[m.groupId]||ut:p)===Se?m.breakContents:m.flatContents;c&&a.push({mode:p,doc:c});break}case ce:if(p===Se||m.hard)return!0;m.soft||(o.push(" "),r--);break;case Xe:n=!0;break;case Ve:if(n)return!1;break}}return!1}function Nr(e,t){let r={},n=t.printWidth,s=Vu(t.endOfLine),u=0,i=[{ind:$u(),mode:Se,doc:e}],a=[],o=!1,p=[],m=0;for(Ou(e);i.length>0;){let{ind:c,mode:f,doc:l}=i.pop();switch(nt(l)){case et:{let h=s!==`
`?H(!1,l,`
`,s):l;a.push(h),i.length>0&&(u+=Ze(h));break}case Ne:for(let h=l.length-1;h>=0;h--)i.push({ind:c,mode:f,doc:l[h]});break;case tt:if(m>=2)throw new Error("There are too many 'cursor' in doc.");a.push(pr),m++;break;case Ge:i.push({ind:dp(c,t),mode:f,doc:l.contents});break;case Ue:i.push({ind:Tp(c,l.n,t),mode:f,doc:l.contents});break;case rt:u-=is(a);break;case pe:switch(f){case ut:if(!o){i.push({ind:c,mode:l.break?Se:ut,doc:l.contents});break}case Se:{o=!1;let h={ind:c,mode:ut,doc:l.contents},g=n-u,S=p.length>0;if(!l.break&&Wr(h,i,g,S,r))i.push(h);else if(l.expandedStates){let B=v(!1,l.expandedStates,-1);if(l.break){i.push({ind:c,mode:Se,doc:B});break}else for(let O=1;O<l.expandedStates.length+1;O++)if(O>=l.expandedStates.length){i.push({ind:c,mode:Se,doc:B});break}else{let R=l.expandedStates[O],_={ind:c,mode:ut,doc:R};if(Wr(_,i,g,S,r)){i.push(_);break}}}else i.push({ind:c,mode:Se,doc:l.contents});break}}l.id&&(r[l.id]=v(!1,i,-1).mode);break;case de:{let h=n-u,{parts:g}=l;if(g.length===0)break;let[S,B]=g,O={ind:c,mode:ut,doc:S},R={ind:c,mode:Se,doc:S},_=Wr(O,[],h,p.length>0,r,!0);if(g.length===1){_?i.push(O):i.push(R);break}let T={ind:c,mode:ut,doc:B},W={ind:c,mode:Se,doc:B};if(g.length===2){_?i.push(T,O):i.push(W,R);break}g.splice(0,2);let Fe={ind:c,mode:f,doc:Wt(g)},X=g[0];Wr({ind:c,mode:ut,doc:[S,B,X]},[],h,p.length>0,r,!0)?i.push(Fe,T,O):_?i.push(Fe,W,O):i.push(Fe,W,R);break}case xe:case Ye:{let h=l.groupId?r[l.groupId]:f;if(h===Se){let g=l.type===xe?l.breakContents:l.negate?l.contents:E(l.contents);g&&i.push({ind:c,mode:f,doc:g})}if(h===ut){let g=l.type===xe?l.flatContents:l.negate?E(l.contents):l.contents;g&&i.push({ind:c,mode:f,doc:g})}break}case Xe:p.push({ind:c,mode:f,doc:l.contents});break;case Ve:p.length>0&&i.push({ind:c,mode:f,doc:Hn});break;case ce:switch(f){case ut:if(l.hard)o=!0;else{l.soft||(a.push(" "),u+=1);break}case Se:if(p.length>0){i.push({ind:c,mode:f,doc:l},...p.reverse()),p.length=0;break}l.literal?c.root?(a.push(s,c.root.value),u=c.root.length):(a.push(s),u=0):(u-=is(a),a.push(s+c.value),u=c.length);break}break;case Oe:i.push({ind:c,mode:f,doc:l.contents});break;case ve:break;default:throw new Ft(l)}i.length===0&&p.length>0&&(i.push(...p.reverse()),p.length=0)}let y=a.indexOf(pr);if(y!==-1){let c=a.indexOf(pr,y+1),f=a.slice(0,y).join(""),l=a.slice(y+1,c).join(""),h=a.slice(c+1).join("");return{formatted:f+l+h,cursorNodeStart:f.length,cursorNodeText:l}}return{formatted:a.join("")}}function xp(e,t,r=0){let n=0;for(let s=r;s<e.length;++s)e[s]==="	"?n=n+t-n%t:n++;return n}var Hu=xp;function gp(e,t){let r=e.lastIndexOf(`
`);return r===-1?0:Hu(e.slice(r+1).match(/^[\t ]*/)[0],t)}var Ku=gp;function Gr(e,t,r){let{node:n}=e;if(n.type==="TemplateLiteral"&&Bp(e)){let m=hp(e,r,t);if(m)return m}let u="expressions";n.type==="TSTemplateLiteralType"&&(u="types");let i=[],a=e.map(t,u),o=Eu(n);o&&(a=a.map(m=>Nr(m,{...r,printWidth:Number.POSITIVE_INFINITY}).formatted)),i.push(ke,"`");let p=0;return e.each(({index:m,node:y})=>{if(i.push(t()),y.tail)return;let{tabWidth:c}=r,f=y.value.raw,l=f.includes(`
`)?Ku(f,c):p;p=l;let h=a[m];if(!o){let S=n[u][m],B=Te(r.originalText,I(y),j(n.quasis[m+1]));if(!B){let O=Nr(h,{...r,printWidth:Number.POSITIVE_INFINITY}).formatted;O.includes(`
`)?B=!0:h=O}B&&(A(S)||q(S)||S.type==="ConditionalExpression"||S.type==="SequenceExpression"||Ae(S)||me(S))&&(h=[E([F,h]),F])}let g=l===0&&f.endsWith(`
`)?ge(Number.NEGATIVE_INFINITY,h):Iu(h,l,c);i.push(D(["${",g,ke,"}"]))},"quasis"),i.push("`"),i}function zu(e){let t=e("quasi");return st(t.label&&{tagged:!0,...t.label},[e("tag"),e("typeParameters"),ke,t])}function hp(e,t,r){let{node:n}=e,s=n.quasis[0].value.raw.trim().split(/\s*\|\s*/);if(s.length>1||s.some(u=>u.length>0)){t.__inJestEach=!0;let u=e.map(r,"expressions");t.__inJestEach=!1;let i=[],a=u.map(c=>"${"+Nr(c,{...t,printWidth:Number.POSITIVE_INFINITY,endOfLine:"lf"}).formatted+"}"),o=[{hasLineBreak:!1,cells:[]}];for(let c=1;c<n.quasis.length;c++){let f=v(!1,o,-1),l=a[c-1];f.cells.push(l),l.includes(`
`)&&(f.hasLineBreak=!0),n.quasis[c].value.raw.includes(`
`)&&o.push({hasLineBreak:!1,cells:[]})}let p=Math.max(s.length,...o.map(c=>c.cells.length)),m=Array.from({length:p}).fill(0),y=[{cells:s},...o.filter(c=>c.cells.length>0)];for(let{cells:c}of y.filter(f=>!f.hasLineBreak))for(let[f,l]of c.entries())m[f]=Math.max(m[f],Ze(l));return i.push(ke,"`",E([C,P(C,y.map(c=>P(" | ",c.cells.map((f,l)=>c.hasLineBreak?f:f+" ".repeat(m[l]-Ze(f))))))]),C,"`"),i}}function Sp(e,t){let{node:r}=e,n=t();return A(r)&&(n=D([E([F,n]),F])),["${",n,ke,"}"]}function Gt(e,t){return e.map(r=>Sp(r,t),"expressions")}function Ur(e,t){return ct(e,r=>typeof r=="string"?t?H(!1,r,/(\\*)`/g,"$1$1\\`"):as(r):r)}function as(e){return H(!1,e,/([\\`]|\${)/g,"\\$1")}function Bp({node:e,parent:t}){let r=/^[fx]?(?:describe|it|test)$/;return t.type==="TaggedTemplateExpression"&&t.quasi===e&&t.tag.type==="MemberExpression"&&t.tag.property.type==="Identifier"&&t.tag.property.name==="each"&&(t.tag.object.type==="Identifier"&&r.test(t.tag.object.name)||t.tag.object.type==="MemberExpression"&&t.tag.object.property.type==="Identifier"&&(t.tag.object.property.name==="only"||t.tag.object.property.name==="skip")&&t.tag.object.object.type==="Identifier"&&r.test(t.tag.object.object.name))}var os=[(e,t)=>e.type==="ObjectExpression"&&t==="properties",(e,t)=>e.type==="CallExpression"&&e.callee.type==="Identifier"&&e.callee.name==="Component"&&t==="arguments",(e,t)=>e.type==="Decorator"&&t==="expression"];function Zu(e){let t=n=>n.type==="TemplateLiteral",r=(n,s)=>Ce(n)&&!n.computed&&n.key.type==="Identifier"&&n.key.name==="styles"&&s==="value";return e.match(t,(n,s)=>G(n)&&s==="elements",r,...os)||e.match(t,r,...os)}function ei(e){return e.match(t=>t.type==="TemplateLiteral",(t,r)=>Ce(t)&&!t.computed&&t.key.type==="Identifier"&&t.key.name==="template"&&r==="value",...os)}function Qu(e,t){return A(e,x.Block|x.Leading,({value:r})=>r===` ${t} `)}function Yr({node:e,parent:t},r){return Qu(e,r)||bp(t)&&Qu(t,r)}function bp(e){return e.type==="AsConstExpression"||e.type==="TSAsExpression"&&e.typeAnnotation.type==="TSTypeReference"&&e.typeAnnotation.typeName.type==="Identifier"&&e.typeAnnotation.typeName.name==="const"}async function Pp(e,t,r){let{node:n}=r,s=n.quasis.map(m=>m.value.raw),u=0,i=s.reduce((m,y,c)=>c===0?y:m+"@prettier-placeholder-"+u+++"-id"+y,""),a=await e(i,{parser:"scss"}),o=Gt(r,t),p=kp(a,o);if(!p)throw new Error("Couldn't insert all the expressions");return["`",E([C,p]),F,"`"]}function kp(e,t){if(!L(t))return e;let r=0,n=ct(Nt(e),s=>typeof s!="string"||!s.includes("@prettier-placeholder")?s:s.split(/@prettier-placeholder-(\d+)-id/).map((u,i)=>i%2===0?Ie(u):(r++,t[u])));return t.length===r?n:null}function Ip({node:e,parent:t,grandparent:r}){return r&&e.quasis&&t.type==="JSXExpressionContainer"&&r.type==="JSXElement"&&r.openingElement.name.name==="style"&&r.openingElement.attributes.some(n=>n.type==="JSXAttribute"&&n.name.name==="jsx")||(t==null?void 0:t.type)==="TaggedTemplateExpression"&&t.tag.type==="Identifier"&&t.tag.name==="css"||(t==null?void 0:t.type)==="TaggedTemplateExpression"&&t.tag.type==="MemberExpression"&&t.tag.object.name==="css"&&(t.tag.property.name==="global"||t.tag.property.name==="resolve")}function Xr(e){return e.type==="Identifier"&&e.name==="styled"}function ti(e){return/^[A-Z]/.test(e.object.name)&&e.property.name==="extend"}function Lp({parent:e}){if(!e||e.type!=="TaggedTemplateExpression")return!1;let t=e.tag.type==="ParenthesizedExpression"?e.tag.expression:e.tag;switch(t.type){case"MemberExpression":return Xr(t.object)||ti(t);case"CallExpression":return Xr(t.callee)||t.callee.type==="MemberExpression"&&(t.callee.object.type==="MemberExpression"&&(Xr(t.callee.object.object)||ti(t.callee.object))||t.callee.object.type==="CallExpression"&&Xr(t.callee.object.callee));case"Identifier":return t.name==="css";default:return!1}}function wp({parent:e,grandparent:t}){return(t==null?void 0:t.type)==="JSXAttribute"&&e.type==="JSXExpressionContainer"&&t.name.type==="JSXIdentifier"&&t.name.name==="css"}function Op(e){if(Ip(e)||Lp(e)||wp(e)||Zu(e))return Pp}var ri=Op;async function vp(e,t,r){let{node:n}=r,s=n.quasis.length,u=Gt(r,t),i=[];for(let a=0;a<s;a++){let o=n.quasis[a],p=a===0,m=a===s-1,y=o.value.cooked,c=y.split(`
`),f=c.length,l=u[a],h=f>2&&c[0].trim()===""&&c[1].trim()==="",g=f>2&&c[f-1].trim()===""&&c[f-2].trim()==="",S=c.every(O=>/^\s*(?:#[^\n\r]*)?$/.test(O));if(!m&&/#[^\n\r]*$/.test(c[f-1]))return null;let B=null;S?B=_p(c):B=await e(y,{parser:"graphql"}),B?(B=Ur(B,!1),!p&&h&&i.push(""),i.push(B),!m&&g&&i.push("")):!p&&!m&&h&&i.push(""),l&&i.push(l)}return["`",E([C,P(C,i)]),C,"`"]}function _p(e){let t=[],r=!1,n=e.map(s=>s.trim());for(let[s,u]of n.entries())u!==""&&(n[s-1]===""&&r?t.push([C,u]):t.push(u),r=!0);return t.length===0?null:P(C,t)}function Mp({node:e,parent:t}){return Yr({node:e,parent:t},"GraphQL")||t&&(t.type==="TaggedTemplateExpression"&&(t.tag.type==="MemberExpression"&&t.tag.object.name==="graphql"&&t.tag.property.name==="experimental"||t.tag.type==="Identifier"&&(t.tag.name==="gql"||t.tag.name==="graphql"))||t.type==="CallExpression"&&t.callee.type==="Identifier"&&t.callee.name==="graphql")}function jp(e){if(Mp(e))return vp}var ni=jp;var ps=0;async function si(e,t,r,n,s){let{node:u}=n,i=ps;ps=ps+1>>>0;let a=S=>`PRETTIER_HTML_PLACEHOLDER_${S}_${i}_IN_JS`,o=u.quasis.map((S,B,O)=>B===O.length-1?S.value.cooked:S.value.cooked+a(B)).join(""),p=Gt(n,r),m=new RegExp(a("(\\d+)"),"g"),y=0,c=await t(o,{parser:e,__onHtmlRoot(S){y=S.children.length}}),f=ct(c,S=>{if(typeof S!="string")return S;let B=[],O=S.split(m);for(let R=0;R<O.length;R++){let _=O[R];if(R%2===0){_&&(_=as(_),s.__embeddedInHtml&&(_=H(!1,_,/<\/(?=script\b)/gi,"<\\/")),B.push(_));continue}let T=Number(_);B.push(p[T])}return B}),l=/^\s/.test(o)?" ":"",h=/\s$/.test(o)?" ":"",g=s.htmlWhitespaceSensitivity==="ignore"?C:l&&h?d:null;return g?D(["`",E([g,D(f)]),g,"`"]):st({hug:!1},D(["`",l,y>1?E(D(f)):D(f),h,"`"]))}function Rp(e){return Yr(e,"HTML")||e.match(t=>t.type==="TemplateLiteral",(t,r)=>t.type==="TaggedTemplateExpression"&&t.tag.type==="Identifier"&&t.tag.name==="html"&&r==="quasi")}var Jp=si.bind(void 0,"html"),qp=si.bind(void 0,"angular");function Wp(e){if(Rp(e))return Jp;if(ei(e))return qp}var ui=Wp;async function Np(e,t,r){let{node:n}=r,s=H(!1,n.quasis[0].value.raw,/((?:\\\\)*)\\`/g,(o,p)=>"\\".repeat(p.length/2)+"`"),u=Gp(s),i=u!=="";i&&(s=H(!1,s,new RegExp(`^${u}`,"gm"),""));let a=Ur(await e(s,{parser:"markdown",__inJsTemplate:!0}),!0);return["`",i?E([F,a]):[Jr,ku(a)],F,"`"]}function Gp(e){let t=e.match(/^([^\S\n]*)\S/m);return t===null?"":t[1]}function Up(e){if(Yp(e))return Np}function Yp({node:e,parent:t}){return(t==null?void 0:t.type)==="TaggedTemplateExpression"&&e.quasis.length===1&&t.tag.type==="Identifier"&&(t.tag.name==="md"||t.tag.name==="markdown")}var ii=Up;function Xp(e){let{node:t}=e;if(t.type!=="TemplateLiteral"||Vp(t))return;let r;for(let n of[ri,ni,ui,ii])if(r=n(e),!!r)return t.quasis.length===1&&t.quasis[0].value.raw.trim()===""?"``":async(...s)=>{let u=await r(...s);return u&&st({embed:!0,...u.label},u)}}function Vp({quasis:e}){return e.some(({value:{cooked:t}})=>t===null)}var ai=Xp;var At=Ga(yi(),1);function nc(e){if(!e.startsWith("#!"))return"";let t=e.indexOf(`
`);return t===-1?e:e.slice(0,t)}var fi=nc;function sc(e){let t=fi(e);t&&(e=e.slice(t.length+1));let r=(0,At.extract)(e),{pragmas:n,comments:s}=(0,At.parseWithComments)(r);return{shebang:t,text:e,pragmas:n,comments:s}}function Ei(e){let{shebang:t,text:r,pragmas:n,comments:s}=sc(e),u=(0,At.strip)(r),i=(0,At.print)({pragmas:{format:"",...n},comments:s.trimStart()});return(t?`${t}
`:"")+i+(u.startsWith(`
`)?`
`:`

`)+u}function uc(e,t){let{originalText:r,[Symbol.for("comments")]:n,locStart:s,locEnd:u,[Symbol.for("printedComments")]:i}=t,{node:a}=e,o=s(a),p=u(a);for(let m of n)s(m)>=o&&u(m)<=p&&i.add(m);return r.slice(o,p)}var Fi=uc;function cs(e,t){var u,i,a,o,p,m,y;if(e.isRoot)return!1;let{node:r,key:n,parent:s}=e;if(t.__isInHtmlInterpolation&&!t.bracketSpacing&&pc(r)&&cr(e))return!0;if(ic(r))return!1;if(r.type==="Identifier"){if((u=r.extra)!=null&&u.parenthesized&&/^PRETTIER_HTML_PLACEHOLDER_\d+_\d+_IN_JS$/.test(r.name)||n==="left"&&(r.name==="async"&&!s.await||r.name==="let")&&s.type==="ForOfStatement")return!0;if(r.name==="let"){let c=(i=e.findAncestor(f=>f.type==="ForOfStatement"))==null?void 0:i.left;if(c&&ie(c,f=>f===r))return!0}if(n==="object"&&r.name==="let"&&s.type==="MemberExpression"&&s.computed&&!s.optional){let c=e.findAncestor(l=>l.type==="ExpressionStatement"||l.type==="ForStatement"||l.type==="ForInStatement"),f=c?c.type==="ExpressionStatement"?c.expression:c.type==="ForStatement"?c.init:c.left:void 0;if(f&&ie(f,l=>l===r))return!0}if(n==="expression")switch(r.name){case"await":case"interface":case"module":case"using":case"yield":case"let":case"type":{let c=e.findAncestor(f=>!Ae(f));if(c!==s&&c.type==="ExpressionStatement")return!0}}return!1}if(r.type==="ObjectExpression"||r.type==="FunctionExpression"||r.type==="ClassExpression"||r.type==="DoExpression"){let c=(a=e.findAncestor(f=>f.type==="ExpressionStatement"))==null?void 0:a.expression;if(c&&ie(c,f=>f===r))return!0}if(r.type==="ObjectExpression"){let c=(o=e.findAncestor(f=>f.type==="ArrowFunctionExpression"))==null?void 0:o.body;if(c&&c.type!=="SequenceExpression"&&c.type!=="AssignmentExpression"&&ie(c,f=>f===r))return!0}switch(s.type){case"ParenthesizedExpression":return!1;case"ClassDeclaration":case"ClassExpression":if(n==="superClass"&&(r.type==="ArrowFunctionExpression"||r.type==="AssignmentExpression"||r.type==="AwaitExpression"||r.type==="BinaryExpression"||r.type==="ConditionalExpression"||r.type==="LogicalExpression"||r.type==="NewExpression"||r.type==="ObjectExpression"||r.type==="SequenceExpression"||r.type==="TaggedTemplateExpression"||r.type==="UnaryExpression"||r.type==="UpdateExpression"||r.type==="YieldExpression"||r.type==="TSNonNullExpression"||r.type==="ClassExpression"&&L(r.decorators)))return!0;break;case"ExportDefaultDeclaration":return Ci(e,t)||r.type==="SequenceExpression";case"Decorator":if(n==="expression"){if(q(r)&&r.computed)return!0;let c=!1,f=!1,l=r;for(;l;)switch(l.type){case"MemberExpression":f=!0,l=l.object;break;case"CallExpression":if(f||c)return t.parser!=="typescript";c=!0,l=l.callee;break;case"Identifier":return!1;case"TaggedTemplateExpression":return t.parser!=="typescript";default:return!0}return!0}break;case"TypeAnnotation":if(e.match(void 0,void 0,(c,f)=>f==="returnType"&&c.type==="ArrowFunctionExpression")&&oc(r))return!0;break;case"BinaryExpression":if(n==="left"&&(s.operator==="in"||s.operator==="instanceof")&&r.type==="UnaryExpression")return!0;break}switch(r.type){case"UpdateExpression":if(s.type==="UnaryExpression")return r.prefix&&(r.operator==="++"&&s.operator==="+"||r.operator==="--"&&s.operator==="-");case"UnaryExpression":switch(s.type){case"UnaryExpression":return r.operator===s.operator&&(r.operator==="+"||r.operator==="-");case"BindExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"TaggedTemplateExpression":return!0;case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"BinaryExpression":return n==="left"&&s.operator==="**";case"TSNonNullExpression":return!0;default:return!1}case"BinaryExpression":if(s.type==="UpdateExpression"||r.operator==="in"&&ac(e))return!0;if(r.operator==="|>"&&((p=r.extra)!=null&&p.parenthesized)){let c=e.grandparent;if(c.type==="BinaryExpression"&&c.operator==="|>")return!0}case"TSTypeAssertion":case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"LogicalExpression":switch(s.type){case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":return!Ae(r);case"ConditionalExpression":return Ae(r);case"CallExpression":case"NewExpression":case"OptionalCallExpression":return n==="callee";case"ClassExpression":case"ClassDeclaration":return n==="superClass";case"TSTypeAssertion":case"TaggedTemplateExpression":case"UnaryExpression":case"JSXSpreadAttribute":case"SpreadElement":case"BindExpression":case"AwaitExpression":case"TSNonNullExpression":case"UpdateExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"AssignmentExpression":case"AssignmentPattern":return n==="left"&&(r.type==="TSTypeAssertion"||Ae(r));case"LogicalExpression":if(r.type==="LogicalExpression")return s.operator!==r.operator;case"BinaryExpression":{let{operator:c,type:f}=r;if(!c&&f!=="TSTypeAssertion")return!0;let l=er(c),h=s.operator,g=er(h);return g>l||n==="right"&&g===l||g===l&&!sr(h,c)?!0:g<l&&c==="%"?h==="+"||h==="-":!!Tu(h)}default:return!1}case"SequenceExpression":switch(s.type){case"ReturnStatement":return!1;case"ForStatement":return!1;case"ExpressionStatement":return n!=="expression";case"ArrowFunctionExpression":return n!=="body";default:return!0}case"YieldExpression":if(s.type==="AwaitExpression")return!0;case"AwaitExpression":switch(s.type){case"TaggedTemplateExpression":case"UnaryExpression":case"LogicalExpression":case"SpreadElement":case"TSAsExpression":case"TSSatisfiesExpression":case"TSNonNullExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"BindExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"ConditionalExpression":return n==="test";case"BinaryExpression":return!(!r.argument&&s.operator==="|>");default:return!1}case"TSFunctionType":if(e.match(c=>c.type==="TSFunctionType",(c,f)=>f==="typeAnnotation"&&c.type==="TSTypeAnnotation",(c,f)=>f==="returnType"&&c.type==="ArrowFunctionExpression"))return!0;case"TSConditionalType":case"TSConstructorType":if(n==="extendsType"&&s.type==="TSConditionalType"){if(r.type==="TSConditionalType")return!0;let{typeAnnotation:c}=r.returnType||r.typeAnnotation;if(c.type==="TSTypePredicate"&&c.typeAnnotation&&(c=c.typeAnnotation.typeAnnotation),c.type==="TSInferType"&&c.typeParameter.constraint)return!0}if(n==="checkType"&&s.type==="TSConditionalType")return!0;case"TSUnionType":case"TSIntersectionType":if((s.type==="TSUnionType"||s.type==="TSIntersectionType")&&s.types.length>1&&(!r.types||r.types.length>1))return!0;case"TSInferType":if(r.type==="TSInferType"&&s.type==="TSRestType")return!1;case"TSTypeOperator":return s.type==="TSArrayType"||s.type==="TSOptionalType"||s.type==="TSRestType"||n==="objectType"&&s.type==="TSIndexedAccessType"||s.type==="TSTypeOperator"||s.type==="TSTypeAnnotation"&&e.grandparent.type.startsWith("TSJSDoc");case"TSTypeQuery":return n==="objectType"&&s.type==="TSIndexedAccessType"||n==="elementType"&&s.type==="TSArrayType";case"TypeofTypeAnnotation":return n==="objectType"&&(s.type==="IndexedAccessType"||s.type==="OptionalIndexedAccessType")||n==="elementType"&&s.type==="ArrayTypeAnnotation";case"ArrayTypeAnnotation":return s.type==="NullableTypeAnnotation";case"IntersectionTypeAnnotation":case"UnionTypeAnnotation":return s.type==="ArrayTypeAnnotation"||s.type==="NullableTypeAnnotation"||s.type==="IntersectionTypeAnnotation"||s.type==="UnionTypeAnnotation"||n==="objectType"&&(s.type==="IndexedAccessType"||s.type==="OptionalIndexedAccessType");case"InferTypeAnnotation":case"NullableTypeAnnotation":return s.type==="ArrayTypeAnnotation"||n==="objectType"&&(s.type==="IndexedAccessType"||s.type==="OptionalIndexedAccessType");case"FunctionTypeAnnotation":{if(e.match(void 0,(f,l)=>l==="typeAnnotation"&&f.type==="TypeAnnotation",(f,l)=>l==="returnType"&&f.type==="ArrowFunctionExpression")||e.match(void 0,(f,l)=>l==="typeAnnotation"&&f.type==="TypePredicate",(f,l)=>l==="typeAnnotation"&&f.type==="TypeAnnotation",(f,l)=>l==="returnType"&&f.type==="ArrowFunctionExpression"))return!0;let c=s.type==="NullableTypeAnnotation"?e.grandparent:s;return c.type==="UnionTypeAnnotation"||c.type==="IntersectionTypeAnnotation"||c.type==="ArrayTypeAnnotation"||n==="objectType"&&(c.type==="IndexedAccessType"||c.type==="OptionalIndexedAccessType")||n==="checkType"&&s.type==="ConditionalTypeAnnotation"||n==="extendsType"&&s.type==="ConditionalTypeAnnotation"&&r.returnType.type==="InferTypeAnnotation"&&r.returnType.typeParameter.bound||c.type==="NullableTypeAnnotation"||s.type==="FunctionTypeParam"&&s.name===null&&$(r).some(f=>{var l;return((l=f.typeAnnotation)==null?void 0:l.type)==="NullableTypeAnnotation"})}case"ConditionalTypeAnnotation":if(n==="extendsType"&&s.type==="ConditionalTypeAnnotation"&&r.type==="ConditionalTypeAnnotation"||n==="checkType"&&s.type==="ConditionalTypeAnnotation")return!0;case"OptionalIndexedAccessType":return n==="objectType"&&s.type==="IndexedAccessType";case"StringLiteral":case"NumericLiteral":case"Literal":if(typeof r.value=="string"&&s.type==="ExpressionStatement"&&!s.directive){let c=e.grandparent;return c.type==="Program"||c.type==="BlockStatement"}return n==="object"&&s.type==="MemberExpression"&&typeof r.value=="number";case"AssignmentExpression":{let c=e.grandparent;return n==="body"&&s.type==="ArrowFunctionExpression"?!0:n==="key"&&(s.type==="ClassProperty"||s.type==="PropertyDefinition")&&s.computed||(n==="init"||n==="update")&&s.type==="ForStatement"?!1:s.type==="ExpressionStatement"?r.left.type==="ObjectPattern":!(n==="key"&&s.type==="TSPropertySignature"||s.type==="AssignmentExpression"||s.type==="SequenceExpression"&&c.type==="ForStatement"&&(c.init===s||c.update===s)||n==="value"&&s.type==="Property"&&c.type==="ObjectPattern"&&c.properties.includes(s)||s.type==="NGChainedExpression")}case"ConditionalExpression":switch(s.type){case"TaggedTemplateExpression":case"UnaryExpression":case"SpreadElement":case"BinaryExpression":case"LogicalExpression":case"NGPipeExpression":case"ExportDefaultDeclaration":case"AwaitExpression":case"JSXSpreadAttribute":case"TSTypeAssertion":case"TypeCastExpression":case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"TSNonNullExpression":return!0;case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"ConditionalExpression":return t.experimentalTernaries?!1:n==="test";case"MemberExpression":case"OptionalMemberExpression":return n==="object";default:return!1}case"FunctionExpression":switch(s.type){case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"TaggedTemplateExpression":return!0;default:return!1}case"ArrowFunctionExpression":switch(s.type){case"BinaryExpression":return s.operator!=="|>"||((m=r.extra)==null?void 0:m.parenthesized);case"NewExpression":case"CallExpression":case"OptionalCallExpression":return n==="callee";case"MemberExpression":case"OptionalMemberExpression":return n==="object";case"TSAsExpression":case"TSSatisfiesExpression":case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":case"TSNonNullExpression":case"BindExpression":case"TaggedTemplateExpression":case"UnaryExpression":case"LogicalExpression":case"AwaitExpression":case"TSTypeAssertion":return!0;case"ConditionalExpression":return n==="test";default:return!1}case"ClassExpression":switch(s.type){case"NewExpression":return n==="callee";default:return!1}case"OptionalMemberExpression":case"OptionalCallExpression":case"CallExpression":case"MemberExpression":if(cc(e))return!0;case"TaggedTemplateExpression":case"TSNonNullExpression":if(n==="callee"&&(s.type==="BindExpression"||s.type==="NewExpression")){let c=r;for(;c;)switch(c.type){case"CallExpression":case"OptionalCallExpression":return!0;case"MemberExpression":case"OptionalMemberExpression":case"BindExpression":c=c.object;break;case"TaggedTemplateExpression":c=c.tag;break;case"TSNonNullExpression":c=c.expression;break;default:return!1}}return!1;case"BindExpression":return n==="callee"&&(s.type==="BindExpression"||s.type==="NewExpression")||n==="object"&&q(s);case"NGPipeExpression":return!(s.type==="NGRoot"||s.type==="NGMicrosyntaxExpression"||s.type==="ObjectProperty"&&!((y=r.extra)!=null&&y.parenthesized)||G(s)||n==="arguments"&&w(s)||n==="right"&&s.type==="NGPipeExpression"||n==="property"&&s.type==="MemberExpression"||s.type==="AssignmentExpression");case"JSXFragment":case"JSXElement":return n==="callee"||n==="left"&&s.type==="BinaryExpression"&&s.operator==="<"||!G(s)&&s.type!=="ArrowFunctionExpression"&&s.type!=="AssignmentExpression"&&s.type!=="AssignmentPattern"&&s.type!=="BinaryExpression"&&s.type!=="NewExpression"&&s.type!=="ConditionalExpression"&&s.type!=="ExpressionStatement"&&s.type!=="JsExpressionRoot"&&s.type!=="JSXAttribute"&&s.type!=="JSXElement"&&s.type!=="JSXExpressionContainer"&&s.type!=="JSXFragment"&&s.type!=="LogicalExpression"&&!w(s)&&!Ce(s)&&s.type!=="ReturnStatement"&&s.type!=="ThrowStatement"&&s.type!=="TypeCastExpression"&&s.type!=="VariableDeclarator"&&s.type!=="YieldExpression";case"TSInstantiationExpression":return n==="object"&&q(s)}return!1}var ic=M(["BlockStatement","BreakStatement","ClassBody","ClassDeclaration","ClassMethod","ClassProperty","PropertyDefinition","ClassPrivateProperty","ContinueStatement","DebuggerStatement","DeclareClass","DeclareExportAllDeclaration","DeclareExportDeclaration","DeclareFunction","DeclareInterface","DeclareModule","DeclareModuleExports","DeclareVariable","DeclareEnum","DoWhileStatement","EnumDeclaration","ExportAllDeclaration","ExportDefaultDeclaration","ExportNamedDeclaration","ExpressionStatement","ForInStatement","ForOfStatement","ForStatement","FunctionDeclaration","IfStatement","ImportDeclaration","InterfaceDeclaration","LabeledStatement","MethodDefinition","ReturnStatement","SwitchStatement","ThrowStatement","TryStatement","TSDeclareFunction","TSEnumDeclaration","TSImportEqualsDeclaration","TSInterfaceDeclaration","TSModuleDeclaration","TSNamespaceExportDeclaration","TypeAlias","VariableDeclaration","WhileStatement","WithStatement"]);function ac(e){let t=0,{node:r}=e;for(;r;){let n=e.getParentNode(t++);if((n==null?void 0:n.type)==="ForStatement"&&n.init===r)return!0;r=n}return!1}function oc(e){return tr(e,t=>t.type==="ObjectTypeAnnotation"&&tr(t,r=>r.type==="FunctionTypeAnnotation"))}function pc(e){return se(e)}function cr(e){let{parent:t,key:r}=e;switch(t.type){case"NGPipeExpression":if(r==="arguments"&&e.isLast)return e.callParent(cr);break;case"ObjectProperty":if(r==="value")return e.callParent(()=>e.key==="properties"&&e.isLast);break;case"BinaryExpression":case"LogicalExpression":if(r==="right")return e.callParent(cr);break;case"ConditionalExpression":if(r==="alternate")return e.callParent(cr);break;case"UnaryExpression":if(t.prefix)return e.callParent(cr);break}return!1}function Ci(e,t){let{node:r,parent:n}=e;return r.type==="FunctionExpression"||r.type==="ClassExpression"?n.type==="ExportDefaultDeclaration"||!cs(e,t):!jt(r)||n.type!=="ExportDefaultDeclaration"&&cs(e,t)?!1:e.call(()=>Ci(e,t),...Ir(r))}function cc(e){let{node:t,parent:r,grandparent:n,key:s}=e;return!!((t.type==="OptionalMemberExpression"||t.type==="OptionalCallExpression")&&(s==="object"&&r.type==="MemberExpression"||s==="callee"&&(r.type==="CallExpression"||r.type==="NewExpression")||r.type==="TSNonNullExpression"&&n.type==="MemberExpression"&&n.object===r)||e.match(()=>t.type==="CallExpression"||t.type==="MemberExpression",(u,i)=>i==="expression"&&u.type==="ChainExpression")&&(e.match(void 0,void 0,(u,i)=>i==="callee"&&(u.type==="CallExpression"&&!u.optional||u.type==="NewExpression")||i==="object"&&u.type==="MemberExpression"&&!u.optional)||e.match(void 0,void 0,(u,i)=>i==="expression"&&u.type==="TSNonNullExpression",(u,i)=>i==="object"&&u.type==="MemberExpression"))||e.match(()=>t.type==="CallExpression"||t.type==="MemberExpression",(u,i)=>i==="expression"&&u.type==="TSNonNullExpression",(u,i)=>i==="expression"&&u.type==="ChainExpression",(u,i)=>i==="object"&&u.type==="MemberExpression"))}var Be=cs;function lc(e,t){let r=t-1;r=Je(e,r,{backwards:!0}),r=qe(e,r,{backwards:!0}),r=Je(e,r,{backwards:!0});let n=qe(e,r,{backwards:!0});return r!==n}var Ai=lc;var mc=()=>!0;function ls(e,t){let r=e.node;return r.printed=!0,t.printer.printComment(e,t)}function Dc(e,t){var m;let r=e.node,n=[ls(e,t)],{printer:s,originalText:u,locStart:i,locEnd:a}=t;if((m=s.isBlockComment)==null?void 0:m.call(s,r)){let y=Z(u,a(r))?Z(u,i(r),{backwards:!0})?C:d:" ";n.push(y)}else n.push(C);let p=qe(u,Je(u,a(r)));return p!==!1&&Z(u,p)&&n.push(C),n}function yc(e,t,r){var p;let n=e.node,s=ls(e,t),{printer:u,originalText:i,locStart:a}=t,o=(p=u.isBlockComment)==null?void 0:p.call(u,n);if(r!=null&&r.hasLineSuffix&&!(r!=null&&r.isBlock)||Z(i,a(n),{backwards:!0})){let m=Ai(i,a(n));return{doc:$n([C,m?C:"",s]),isBlock:o,hasLineSuffix:!0}}return!o||r!=null&&r.hasLineSuffix?{doc:[$n([" ",s]),Ee],isBlock:o,hasLineSuffix:!0}:{doc:[" ",s],isBlock:o,hasLineSuffix:!1}}function J(e,t,r={}){let{node:n}=e;if(!L(n==null?void 0:n.comments))return"";let{indent:s=!1,marker:u,filter:i=mc}=r,a=[];if(e.each(({node:p})=>{p.leading||p.trailing||p.marker!==u||!i(p)||a.push(ls(e,t))},"comments"),a.length===0)return"";let o=P(C,a);return s?E([C,o]):o}function ms(e,t){let r=e.node;if(!r)return{};let n=t[Symbol.for("printedComments")];if((r.comments||[]).filter(o=>!n.has(o)).length===0)return{leading:"",trailing:""};let u=[],i=[],a;return e.each(()=>{let o=e.node;if(n!=null&&n.has(o))return;let{leading:p,trailing:m}=o;p?u.push(Dc(e,t)):m&&(a=yc(e,t,a),i.push(a.doc))},"comments"),{leading:u,trailing:i}}function le(e,t,r){let{leading:n,trailing:s}=ms(e,r);return!n&&!s?t:or(t,u=>[n,u,s])}var Ds=class extends Error{name="UnexpectedNodeError";constructor(t,r,n="type"){super(`Unexpected ${r} node ${n}: ${JSON.stringify(t[n])}.`),this.node=t}},_e=Ds;function ys(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var Me,fs=class{constructor(t){Xs(this,Me,void 0);Vs(this,Me,new Set(t))}getLeadingWhitespaceCount(t){let r=it(this,Me),n=0;for(let s=0;s<t.length&&r.has(t.charAt(s));s++)n++;return n}getTrailingWhitespaceCount(t){let r=it(this,Me),n=0;for(let s=t.length-1;s>=0&&r.has(t.charAt(s));s--)n++;return n}getLeadingWhitespace(t){let r=this.getLeadingWhitespaceCount(t);return t.slice(0,r)}getTrailingWhitespace(t){let r=this.getTrailingWhitespaceCount(t);return t.slice(t.length-r)}hasLeadingWhitespace(t){return it(this,Me).has(t.charAt(0))}hasTrailingWhitespace(t){return it(this,Me).has(v(!1,t,-1))}trimStart(t){let r=this.getLeadingWhitespaceCount(t);return t.slice(r)}trimEnd(t){let r=this.getTrailingWhitespaceCount(t);return t.slice(0,t.length-r)}trim(t){return this.trimEnd(this.trimStart(t))}split(t,r=!1){let n=`[${ys([...it(this,Me)].join(""))}]+`,s=new RegExp(r?`(${n})`:n);return t.split(s)}hasWhitespaceCharacter(t){let r=it(this,Me);return Array.prototype.some.call(t,n=>r.has(n))}hasNonWhitespaceCharacter(t){let r=it(this,Me);return Array.prototype.some.call(t,n=>!r.has(n))}isWhitespaceOnly(t){let r=it(this,Me);return Array.prototype.every.call(t,n=>r.has(n))}};Me=new WeakMap;var di=fs;var Vr=new di(` 
\r	`),Es=e=>e===""||e===d||e===C||e===F;function fc(e,t,r){var O,R,_;let{node:n}=e;if(n.type==="JSXElement"&&kc(n))return[r("openingElement"),r("closingElement")];let s=n.type==="JSXElement"?r("openingElement"):r("openingFragment"),u=n.type==="JSXElement"?r("closingElement"):r("closingFragment");if(n.children.length===1&&n.children[0].type==="JSXExpressionContainer"&&(n.children[0].expression.type==="TemplateLiteral"||n.children[0].expression.type==="TaggedTemplateExpression"))return[s,...e.map(r,"children"),u];n.children=n.children.map(T=>Ic(T)?{type:"JSXText",value:" ",raw:" "}:T);let i=n.children.some(U),a=n.children.filter(T=>T.type==="JSXExpressionContainer").length>1,o=n.type==="JSXElement"&&n.openingElement.attributes.length>1,p=re(s)||i||o||a,m=e.parent.rootMarker==="mdx",y=t.singleQuote?"{' '}":'{" "}',c=m?" ":b([y,F]," "),f=((R=(O=n.openingElement)==null?void 0:O.name)==null?void 0:R.name)==="fbt",l=Ec(e,t,r,c,f),h=n.children.some(T=>lr(T));for(let T=l.length-2;T>=0;T--){let W=l[T]===""&&l[T+1]==="",Fe=l[T]===C&&l[T+1]===""&&l[T+2]===C,X=(l[T]===F||l[T]===C)&&l[T+1]===""&&l[T+2]===c,ue=l[T]===c&&l[T+1]===""&&(l[T+2]===F||l[T+2]===C),z=l[T]===c&&l[T+1]===""&&l[T+2]===c,wt=l[T]===F&&l[T+1]===""&&l[T+2]===C||l[T]===C&&l[T+1]===""&&l[T+2]===F;Fe&&h||W||X||z||wt?l.splice(T,2):ue&&l.splice(T+1,2)}for(;l.length>0&&Es(v(!1,l,-1));)l.pop();for(;l.length>1&&Es(l[0])&&Es(l[1]);)l.shift(),l.shift();let g=[];for(let[T,W]of l.entries()){if(W===c){if(T===1&&l[T-1]===""){if(l.length===2){g.push(y);continue}g.push([y,C]);continue}else if(T===l.length-1){g.push(y);continue}else if(l[T-1]===""&&l[T-2]===C){g.push(y);continue}}g.push(W),re(W)&&(p=!0)}let S=h?Wt(g):D(g,{shouldBreak:!0});if(((_=t.cursorNode)==null?void 0:_.type)==="JSXText"&&n.children.includes(t.cursorNode)&&(S=[Kn,S,Kn]),m)return S;let B=D([s,E([C,S]),C,u]);return p?B:He([D([s,...l,u]),B])}function Ec(e,t,r,n,s){let u=[];return e.each(({node:i,next:a})=>{if(i.type==="JSXText"){let o=De(i);if(lr(i)){let p=Vr.split(o,!0);p[0]===""&&(u.push(""),p.shift(),/\n/.test(p[0])?u.push(xi(s,p[1],i,a)):u.push(n),p.shift());let m;if(v(!1,p,-1)===""&&(p.pop(),m=p.pop()),p.length===0)return;for(let[y,c]of p.entries())y%2===1?u.push(d):u.push(c);m!==void 0?/\n/.test(m)?u.push(xi(s,v(!1,u,-1),i,a)):u.push(n):u.push(Ti(s,v(!1,u,-1),i,a))}else/\n/.test(o)?o.match(/\n/g).length>1&&u.push("",C):u.push("",n)}else{let o=r();if(u.push(o),a&&lr(a)){let m=Vr.trim(De(a)),[y]=Vr.split(m);u.push(Ti(s,y,i,a))}else u.push(C)}},"children"),u}function Ti(e,t,r,n){return e?"":r.type==="JSXElement"&&!r.closingElement||(n==null?void 0:n.type)==="JSXElement"&&!n.closingElement?t.length===1?F:C:F}function xi(e,t,r,n){return e?C:t.length===1?r.type==="JSXElement"&&!r.closingElement||(n==null?void 0:n.type)==="JSXElement"&&!n.closingElement?C:F:C}var Fc=new Set(["ArrayExpression","TupleExpression","JSXAttribute","JSXElement","JSXExpressionContainer","JSXFragment","ExpressionStatement","CallExpression","OptionalCallExpression","ConditionalExpression","JsExpressionRoot"]);function Cc(e,t,r){let{parent:n}=e;if(Fc.has(n.type))return t;let s=e.match(void 0,i=>i.type==="ArrowFunctionExpression",w,i=>i.type==="JSXExpressionContainer"),u=Be(e,r);return D([u?"":b("("),E([F,t]),F,u?"":b(")")],{shouldBreak:s})}function Ac(e,t,r){let{node:n}=e,s=[];if(s.push(r("name")),n.value){let u;if(te(n.value)){let i=De(n.value),a=H(!1,H(!1,i.slice(1,-1),"&apos;","'"),"&quot;",'"'),o=hr(a,t.jsxSingleQuote);a=o==='"'?H(!1,a,'"',"&quot;"):H(!1,a,"'","&apos;"),u=e.call(()=>le(e,Ie(o+a+o),t),"value")}else u=r("value");s.push("=",u)}return s}function dc(e,t,r){let{node:n}=e,s=(u,i)=>u.type==="JSXEmptyExpression"||!A(u)&&(G(u)||se(u)||u.type==="ArrowFunctionExpression"||u.type==="AwaitExpression"&&(s(u.argument,u)||u.argument.type==="JSXElement")||w(u)||u.type==="ChainExpression"&&w(u.expression)||u.type==="FunctionExpression"||u.type==="TemplateLiteral"||u.type==="TaggedTemplateExpression"||u.type==="DoExpression"||U(i)&&(u.type==="ConditionalExpression"||me(u)));return s(n.expression,e.parent)?D(["{",r("expression"),ke,"}"]):D(["{",E([F,r("expression")]),F,ke,"}"])}function Tc(e,t,r){var a,o;let{node:n}=e,s=A(n.name)||A(n.typeParameters)||A(n.typeArguments);if(n.selfClosing&&n.attributes.length===0&&!s)return["<",r("name"),n.typeArguments?r("typeArguments"):r("typeParameters")," />"];if(((a=n.attributes)==null?void 0:a.length)===1&&n.attributes[0].value&&te(n.attributes[0].value)&&!n.attributes[0].value.value.includes(`
`)&&!s&&!A(n.attributes[0]))return D(["<",r("name"),n.typeArguments?r("typeArguments"):r("typeParameters")," ",...e.map(r,"attributes"),n.selfClosing?" />":">"]);let u=(o=n.attributes)==null?void 0:o.some(p=>p.value&&te(p.value)&&p.value.value.includes(`
`)),i=t.singleAttributePerLine&&n.attributes.length>1?C:d;return D(["<",r("name"),n.typeArguments?r("typeArguments"):r("typeParameters"),E(e.map(()=>[i,r()],"attributes")),...xc(n,t,s)],{shouldBreak:u})}function xc(e,t,r){return e.selfClosing?[d,"/>"]:gc(e,t,r)?[">"]:[F,">"]}function gc(e,t,r){let n=e.attributes.length>0&&A(v(!1,e.attributes,-1),x.Trailing);return e.attributes.length===0&&!r||(t.bracketSameLine||t.jsxBracketSameLine)&&(!r||e.attributes.length>0)&&!n}function hc(e,t,r){let{node:n}=e,s=[];s.push("</");let u=r("name");return A(n.name,x.Leading|x.Line)?s.push(E([C,u]),C):A(n.name,x.Leading|x.Block)?s.push(" ",u):s.push(u),s.push(">"),s}function Sc(e,t){let{node:r}=e,n=A(r),s=A(r,x.Line),u=r.type==="JSXOpeningFragment";return[u?"<":"</",E([s?C:n&&!u?" ":"",J(e,t)]),s?C:"",">"]}function Bc(e,t,r){let n=le(e,fc(e,t,r),t);return Cc(e,n,t)}function bc(e,t){let{node:r}=e,n=A(r,x.Line);return[J(e,t,{indent:n}),n?C:""]}function Pc(e,t,r){let{node:n}=e;return["{",e.call(({node:s})=>{let u=["...",r()];return!A(s)||!ss(e)?u:[E([F,le(e,u,t)]),F]},n.type==="JSXSpreadAttribute"?"argument":"expression"),"}"]}function gi(e,t,r){let{node:n}=e;if(n.type.startsWith("JSX"))switch(n.type){case"JSXAttribute":return Ac(e,t,r);case"JSXIdentifier":return n.name;case"JSXNamespacedName":return P(":",[r("namespace"),r("name")]);case"JSXMemberExpression":return P(".",[r("object"),r("property")]);case"JSXSpreadAttribute":case"JSXSpreadChild":return Pc(e,t,r);case"JSXExpressionContainer":return dc(e,t,r);case"JSXFragment":case"JSXElement":return Bc(e,t,r);case"JSXOpeningElement":return Tc(e,t,r);case"JSXClosingElement":return hc(e,t,r);case"JSXOpeningFragment":case"JSXClosingFragment":return Sc(e,t);case"JSXEmptyExpression":return bc(e,t);case"JSXText":throw new Error("JSXText should be handled by JSXElement");default:throw new _e(n,"JSX")}}function kc(e){if(e.children.length===0)return!0;if(e.children.length>1)return!1;let t=e.children[0];return t.type==="JSXText"&&!lr(t)}function lr(e){return e.type==="JSXText"&&(Vr.hasNonWhitespaceCharacter(De(e))||!/\n/.test(De(e)))}function Ic(e){return e.type==="JSXExpressionContainer"&&te(e.expression)&&e.expression.value===" "&&!A(e.expression)}function hi(e){let{node:t,parent:r}=e;if(!U(t)||!U(r))return!1;let{index:n,siblings:s}=e,u;for(let i=n;i>0;i--){let a=s[i-1];if(!(a.type==="JSXText"&&!lr(a))){u=a;break}}return(u==null?void 0:u.type)==="JSXExpressionContainer"&&u.expression.type==="JSXEmptyExpression"&&Bt(u.expression)}function Lc(e){return Bt(e.node)||hi(e)}var $r=Lc;var wc=0;function Hr(e,t,r){var R;let{node:n,parent:s,grandparent:u,key:i}=e,a=i!=="body"&&(s.type==="IfStatement"||s.type==="WhileStatement"||s.type==="SwitchStatement"||s.type==="DoWhileStatement"),o=n.operator==="|>"&&((R=e.root.extra)==null?void 0:R.__isUsingHackPipeline),p=Fs(e,r,t,!1,a);if(a)return p;if(o)return D(p);if(w(s)&&s.callee===n||s.type==="UnaryExpression"||q(s)&&!s.computed)return D([E([F,...p]),F]);let m=s.type==="ReturnStatement"||s.type==="ThrowStatement"||s.type==="JSXExpressionContainer"&&u.type==="JSXAttribute"||n.operator!=="|"&&s.type==="JsExpressionRoot"||n.type!=="NGPipeExpression"&&(s.type==="NGRoot"&&t.parser==="__ng_binding"||s.type==="NGMicrosyntaxExpression"&&u.type==="NGMicrosyntax"&&u.body.length===1)||n===s.body&&s.type==="ArrowFunctionExpression"||n!==s.body&&s.type==="ForStatement"||s.type==="ConditionalExpression"&&u.type!=="ReturnStatement"&&u.type!=="ThrowStatement"&&!w(u)||s.type==="TemplateLiteral",y=s.type==="AssignmentExpression"||s.type==="VariableDeclarator"||s.type==="ClassProperty"||s.type==="PropertyDefinition"||s.type==="TSAbstractPropertyDefinition"||s.type==="ClassPrivateProperty"||Ce(s),c=me(n.left)&&sr(n.operator,n.left.operator);if(m||Ut(n)&&!c||!Ut(n)&&y)return D(p);if(p.length===0)return"";let f=U(n.right),l=p.findIndex(_=>typeof _!="string"&&!Array.isArray(_)&&_.type===pe),h=p.slice(0,l===-1?1:l+1),g=p.slice(h.length,f?-1:void 0),S=Symbol("logicalChain-"+ ++wc),B=D([...h,E(g)],{id:S});if(!f)return B;let O=v(!1,p,-1);return D([B,Ct(O,{groupId:S})])}function Fs(e,t,r,n,s){var h;let{node:u}=e;if(!me(u))return[D(t())];let i=[];sr(u.operator,u.left.operator)?i=e.call(g=>Fs(g,t,r,!0,s),"left"):i.push(D(t("left")));let a=Ut(u),o=(u.operator==="|>"||u.type==="NGPipeExpression"||Oc(e,r))&&!we(r.originalText,u.right),p=u.type==="NGPipeExpression"?"|":u.operator,m=u.type==="NGPipeExpression"&&u.arguments.length>0?D(E([F,": ",P([d,": "],e.map(()=>ge(2,D(t())),"arguments"))])):"",y;if(a)y=[p," ",t("right"),m];else{let S=p==="|>"&&((h=e.root.extra)==null?void 0:h.__isUsingHackPipeline)?e.call(B=>Fs(B,t,r,!0,s),"right"):t("right");y=[o?d:"",p,o?" ":d,S,m]}let{parent:c}=e,f=A(u.left,x.Trailing|x.Line),l=f||!(s&&u.type==="LogicalExpression")&&c.type!==u.type&&u.left.type!==u.type&&u.right.type!==u.type;if(i.push(o?"":" ",l?D(y,{shouldBreak:f}):y),n&&A(u)){let g=Nt(le(e,i,r));return Array.isArray(g)||g.type===de?qr(g):[g]}return i}function Ut(e){return e.type!=="LogicalExpression"?!1:!!(se(e.right)&&e.right.properties.length>0||G(e.right)&&e.right.elements.length>0||U(e.right))}var Si=e=>e.type==="BinaryExpression"&&e.operator==="|";function Oc(e,t){return(t.parser==="__vue_expression"||t.parser==="__vue_ts_expression")&&Si(e.node)&&!e.hasAncestor(r=>!Si(r)&&r.type!=="JsExpressionRoot")}function bi(e,t,r){let{node:n}=e;if(n.type.startsWith("NG"))switch(n.type){case"NGRoot":return[r("node"),A(n.node)?" //"+ot(n.node)[0].value.trimEnd():""];case"NGPipeExpression":return Hr(e,t,r);case"NGChainedExpression":return D(P([";",d],e.map(()=>_c(e)?r():["(",r(),")"],"expressions")));case"NGEmptyExpression":return"";case"NGMicrosyntax":return e.map(()=>[e.isFirst?"":Bi(e)?" ":[";",d],r()],"body");case"NGMicrosyntaxKey":return/^[$_a-z][\w$]*(?:-[$_a-z][\w$])*$/i.test(n.name)?n.name:JSON.stringify(n.name);case"NGMicrosyntaxExpression":return[r("expression"),n.alias===null?"":[" as ",r("alias")]];case"NGMicrosyntaxKeyedExpression":{let{index:s,parent:u}=e,i=Bi(e)||(s===1&&(n.key.name==="then"||n.key.name==="else"||n.key.name==="as")||(s===2||s===3)&&(n.key.name==="else"&&u.body[s-1].type==="NGMicrosyntaxKeyedExpression"&&u.body[s-1].key.name==="then"||n.key.name==="track"))&&u.body[0].type==="NGMicrosyntaxExpression";return[r("key"),i?" ":": ",r("expression")]}case"NGMicrosyntaxLet":return["let ",r("key"),n.value===null?"":[" = ",r("value")]];case"NGMicrosyntaxAs":return[r("key")," as ",r("alias")];default:throw new _e(n,"Angular")}}function Bi({node:e,index:t}){return e.type==="NGMicrosyntaxKeyedExpression"&&e.key.name==="of"&&t===1}var vc=M(["CallExpression","OptionalCallExpression","AssignmentExpression"]);function _c({node:e}){return tr(e,vc)}function Cs(e,t,r){let{node:n}=e;return D([P(d,e.map(r,"decorators")),Ii(n,t)?C:d])}function Pi(e,t,r){return Li(e.node)?[P(C,e.map(r,"declaration","decorators")),C]:""}function ki(e,t,r){let{node:n,parent:s}=e,{decorators:u}=n;if(!L(u)||Li(s)||$r(e))return"";let i=n.type==="ClassExpression"||n.type==="ClassDeclaration"||Ii(n,t);return[e.key==="declaration"&&mu(s)?C:i?Ee:"",P(d,e.map(r,"decorators")),d]}function Ii(e,t){return e.decorators.some(r=>Z(t.originalText,I(r)))}function Li(e){var r;if(e.type!=="ExportDefaultDeclaration"&&e.type!=="ExportNamedDeclaration"&&e.type!=="DeclareExportDeclaration")return!1;let t=(r=e.declaration)==null?void 0:r.decorators;return L(t)&&gt(e,t[0])}var mt=class extends Error{name="ArgExpansionBailout"};function Mc(e,t,r){let{node:n}=e,s=ae(n);if(s.length===0)return["(",J(e,t),")"];if(Jc(s))return["(",r(["arguments",0]),", ",r(["arguments",1]),")"];let u=!1,i=s.length-1,a=[];ur(e,({node:c},f)=>{let l=r();f===i||(fe(c,t)?(u=!0,l=[l,",",C,C]):l=[l,",",d]),a.push(l)});let p=!(n.type==="ImportExpression"||n.callee.type==="Import")&&ye(t,"all")?",":"";function m(){return D(["(",E([d,...a]),p,d,")"],{shouldBreak:!0})}if(u||e.parent.type!=="Decorator"&&Au(s))return m();if(Rc(s)){let c=a.slice(1);if(c.some(re))return m();let f;try{f=r(Yn(n,0),{expandFirstArg:!0})}catch(l){if(l instanceof mt)return m();throw l}return re(f)?[Ee,He([["(",D(f,{shouldBreak:!0}),", ",...c,")"],m()])]:He([["(",f,", ",...c,")"],["(",D(f,{shouldBreak:!0}),", ",...c,")"],m()])}if(jc(s,a,t)){let c=a.slice(0,-1);if(c.some(re))return m();let f;try{f=r(Yn(n,-1),{expandLastArg:!0})}catch(l){if(l instanceof mt)return m();throw l}return re(f)?[Ee,He([["(",...c,D(f,{shouldBreak:!0}),")"],m()])]:He([["(",...c,f,")"],["(",...c,D(f,{shouldBreak:!0}),")"],m()])}let y=["(",E([F,...a]),b(p),F,")"];return vr(e)?y:D(y,{shouldBreak:a.some(re)||u})}function mr(e,t=!1){return se(e)&&(e.properties.length>0||A(e))||G(e)&&(e.elements.length>0||A(e))||e.type==="TSTypeAssertion"&&mr(e.expression)||Ae(e)&&mr(e.expression)||e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression"&&(!e.returnType||!e.returnType.typeAnnotation||e.returnType.typeAnnotation.type!=="TSTypeReference"||qc(e.body))&&(e.body.type==="BlockStatement"||e.body.type==="ArrowFunctionExpression"&&mr(e.body,!0)||se(e.body)||G(e.body)||!t&&(w(e.body)||e.body.type==="ConditionalExpression")||U(e.body))||e.type==="DoExpression"||e.type==="ModuleExpression"}function jc(e,t,r){var u,i;let n=v(!1,e,-1);if(e.length===1){let a=v(!1,t,-1);if((u=a.label)!=null&&u.embed&&((i=a.label)==null?void 0:i.hug)!==!1)return!0}let s=v(!1,e,-2);return!A(n,x.Leading)&&!A(n,x.Trailing)&&mr(n)&&(!s||s.type!==n.type)&&(e.length!==2||s.type!=="ArrowFunctionExpression"||!G(n))&&!(e.length>1&&As(n,r))}function Rc(e){if(e.length!==2)return!1;let[t,r]=e;return t.type==="ModuleExpression"&&Wc(r)?!0:!A(t)&&(t.type==="FunctionExpression"||t.type==="ArrowFunctionExpression"&&t.body.type==="BlockStatement")&&r.type!=="FunctionExpression"&&r.type!=="ArrowFunctionExpression"&&r.type!=="ConditionalExpression"&&wi(r)&&!mr(r)}function wi(e){var t;if(e.type==="ParenthesizedExpression")return wi(e.expression);if(Ae(e)||e.type==="TypeCastExpression"){let{typeAnnotation:r}=e;return r.type==="TypeAnnotation"&&(r=r.typeAnnotation),r.type==="TSArrayType"&&(r=r.elementType,r.type==="TSArrayType"&&(r=r.elementType)),(r.type==="GenericTypeAnnotation"||r.type==="TSTypeReference")&&((t=r.typeParameters)==null?void 0:t.params.length)===1&&(r=r.typeParameters.params[0]),Jt(r)&&be(e.expression,1)}return pt(e)&&ae(e).length>1?!1:me(e)?be(e.left,1)&&be(e.right,1):Wn(e)||be(e)}function Jc(e){return e.length===2&&e[0].type==="ArrowFunctionExpression"&&$(e[0]).length===0&&e[0].body.type==="BlockStatement"&&e[1].type==="ArrayExpression"&&!e.some(t=>A(t))}function qc(e){return e.type==="BlockStatement"&&(e.body.some(t=>t.type!=="EmptyStatement")||A(e,x.Dangling))}function Wc(e){return e.type==="ObjectExpression"&&e.properties.length===1&&Ce(e.properties[0])&&e.properties[0].key.type==="Identifier"&&e.properties[0].key.name==="type"&&te(e.properties[0].value)&&e.properties[0].value.value==="module"}var Dr=Mc;var Nc=e=>((e.type==="ChainExpression"||e.type==="TSNonNullExpression")&&(e=e.expression),w(e)&&ae(e).length>0);function Oi(e,t,r){var p;let n=r("object"),s=ds(e,t,r),{node:u}=e,i=e.findAncestor(m=>!(q(m)||m.type==="TSNonNullExpression")),a=e.findAncestor(m=>!(m.type==="ChainExpression"||m.type==="TSNonNullExpression")),o=i&&(i.type==="NewExpression"||i.type==="BindExpression"||i.type==="AssignmentExpression"&&i.left.type!=="Identifier")||u.computed||u.object.type==="Identifier"&&u.property.type==="Identifier"&&!q(a)||(a.type==="AssignmentExpression"||a.type==="VariableDeclarator")&&(Nc(u.object)||((p=n.label)==null?void 0:p.memberChain));return st(n.label,[n,o?s:D(E([F,s]))])}function ds(e,t,r){let n=r("property"),{node:s}=e,u=V(e);return s.computed?!s.property||Pe(s.property)?[u,"[",n,"]"]:D([u,"[",E([F,n]),F,"]"]):[u,".",n]}function vi(e,t,r){if(e.node.type==="ChainExpression")return e.call(()=>vi(e,t,r),"expression");let{parent:n}=e,s=!n||n.type==="ExpressionStatement",u=[];function i(k){let{originalText:N}=t,Q=lt(N,I(k));return N.charAt(Q)===")"?Q!==!1&&_t(N,Q+1):fe(k,t)}function a(k){let{node:N}=k;if(N.type==="ChainExpression")return k.call(()=>a(k),"expression");if(w(N)&&(Et(N.callee)||w(N.callee))){let Q=i(N);u.unshift({node:N,hasTrailingEmptyLine:Q,printed:[le(k,[V(k),ze(k,t,r),Dr(k,t,r)],t),Q?C:""]}),k.call(je=>a(je),"callee")}else Et(N)?(u.unshift({node:N,needsParens:Be(k,t),printed:le(k,q(N)?ds(k,t,r):Kr(k,t,r),t)}),k.call(Q=>a(Q),"object")):N.type==="TSNonNullExpression"?(u.unshift({node:N,printed:le(k,"!",t)}),k.call(Q=>a(Q),"expression")):u.unshift({node:N,printed:r()})}let{node:o}=e;u.unshift({node:o,printed:[V(e),ze(e,t,r),Dr(e,t,r)]}),o.callee&&e.call(k=>a(k),"callee");let p=[],m=[u[0]],y=1;for(;y<u.length&&(u[y].node.type==="TSNonNullExpression"||w(u[y].node)||q(u[y].node)&&u[y].node.computed&&Pe(u[y].node.property));++y)m.push(u[y]);if(!w(u[0].node))for(;y+1<u.length&&(Et(u[y].node)&&Et(u[y+1].node));++y)m.push(u[y]);p.push(m),m=[];let c=!1;for(;y<u.length;++y){if(c&&Et(u[y].node)){if(u[y].node.computed&&Pe(u[y].node.property)){m.push(u[y]);continue}p.push(m),m=[],c=!1}(w(u[y].node)||u[y].node.type==="ImportExpression")&&(c=!0),m.push(u[y]),A(u[y].node,x.Trailing)&&(p.push(m),m=[],c=!1)}m.length>0&&p.push(m);function f(k){return/^[A-Z]|^[$_]+$/.test(k)}function l(k){return k.length<=t.tabWidth}function h(k){var je;let N=(je=k[1][0])==null?void 0:je.node.computed;if(k[0].length===1){let xt=k[0][0].node;return xt.type==="ThisExpression"||xt.type==="Identifier"&&(f(xt.name)||s&&l(xt.name)||N)}let Q=v(!1,k[0],-1).node;return q(Q)&&Q.property.type==="Identifier"&&(f(Q.property.name)||N)}let g=p.length>=2&&!A(p[1][0].node)&&h(p);function S(k){let N=k.map(Q=>Q.printed);return k.length>0&&v(!1,k,-1).needsParens?["(",...N,")"]:N}function B(k){return k.length===0?"":E([C,P(C,k.map(S))])}let O=p.map(S),R=O,_=g?3:2,T=p.flat(),W=T.slice(1,-1).some(k=>A(k.node,x.Leading))||T.slice(0,-1).some(k=>A(k.node,x.Trailing))||p[_]&&A(p[_][0].node,x.Leading);if(p.length<=_&&!W&&!p.some(k=>v(!1,k,-1).hasTrailingEmptyLine))return vr(e)?R:D(R);let Fe=v(!1,p[g?1:0],-1).node,X=!w(Fe)&&i(Fe),ue=[S(p[0]),g?p.slice(1,2).map(S):"",X?C:"",B(p.slice(g?2:1))],z=u.map(({node:k})=>k).filter(w);function wt(){let k=v(!1,v(!1,p,-1),-1).node,N=v(!1,O,-1);return w(k)&&re(N)&&z.slice(0,-1).some(Q=>Q.arguments.some(Mt))}let Ht;return W||z.length>2&&z.some(k=>!k.arguments.every(N=>be(N)))||O.slice(0,-1).some(re)||wt()?Ht=D(ue):Ht=[re(R)||X?Ee:"",He([R,ue])],st({memberChain:!0},Ht)}var _i=vi;function zr(e,t,r){var y;let{node:n,parent:s}=e,u=n.type==="NewExpression",i=n.type==="ImportExpression",a=V(e),o=ae(n),p=o.length===1&&wr(o[0],t.originalText);if(p||o.length>0&&!u&&!i&&(Uc(n,s)||St(n,s))){let c=[];if(ur(e,()=>{c.push(r())}),!(p&&((y=c[0].label)!=null&&y.embed)))return[u?"new ":"",r("callee"),a,ze(e,t,r),"(",P(", ",c),")"]}if(!i&&!u&&Et(n.callee)&&!e.call(c=>Be(c,t),"callee",...n.callee.type==="ChainExpression"?["expression"]:[]))return _i(e,t,r);let m=[u?"new ":"",i?Gc(n):r("callee"),a,ze(e,t,r),Dr(e,t,r)];return i||w(n.callee)?D(m):m}function Gc(e){return e.phase?`import.${e.phase}`:"import"}function Uc(e,t){if(e.callee.type!=="Identifier")return!1;if(e.callee.name==="require"){let r=ae(e);return r.length===1&&te(r[0])||r.length>1}if(e.callee.name==="define"){let r=ae(e);return t.type==="ExpressionStatement"&&(r.length===1||r.length===2&&r[0].type==="ArrayExpression"||r.length===3&&te(r[0])&&r[1].type==="ArrayExpression")}return!1}function dt(e,t,r,n,s,u){let i=Yc(e,t,r,n,u),a=u?r(u,{assignmentLayout:i}):"";switch(i){case"break-after-operator":return D([D(n),s,D(E([d,a]))]);case"never-break-after-operator":return D([D(n),s," ",a]);case"fluid":{let o=Symbol("assignment");return D([D(n),s,D(E(d),{id:o}),ke,Ct(a,{groupId:o})])}case"break-lhs":return D([n,s," ",D(a)]);case"chain":return[D(n),s,d,a];case"chain-tail":return[D(n),s,E([d,a])];case"chain-tail-arrow-chain":return[D(n),s,a];case"only-left":return n}}function ji(e,t,r){let{node:n}=e;return dt(e,t,r,r("left"),[" ",n.operator],"right")}function Ri(e,t,r){return dt(e,t,r,r("id")," =","init")}function Yc(e,t,r,n,s){let{node:u}=e,i=u[s];if(!i)return"only-left";let a=!Qr(i);if(e.match(Qr,Ji,c=>!a||c.type!=="ExpressionStatement"&&c.type!=="VariableDeclaration"))return a?i.type==="ArrowFunctionExpression"&&i.body.type==="ArrowFunctionExpression"?"chain-tail-arrow-chain":"chain-tail":"chain";if(!a&&Qr(i.right)||we(t.originalText,i))return"break-after-operator";if(i.type==="CallExpression"&&i.callee.name==="require"||t.parser==="json5"||t.parser==="jsonc"||t.parser==="json")return"never-break-after-operator";let m=vu(n);if(Vc(u)||zc(u)||Ts(u)&&m)return"break-lhs";let y=Zc(u,n,t);return e.call(()=>Xc(e,t,r,y),s)?"break-after-operator":$c(u)?"break-lhs":!m&&(y||i.type==="TemplateLiteral"||i.type==="TaggedTemplateExpression"||i.type==="BooleanLiteral"||Pe(i)||i.type==="ClassExpression")?"never-break-after-operator":"fluid"}function Xc(e,t,r,n){let s=e.node;if(me(s)&&!Ut(s))return!0;switch(s.type){case"StringLiteralTypeAnnotation":case"SequenceExpression":return!0;case"TSConditionalType":case"ConditionalTypeAnnotation":if(!t.experimentalTernaries&&!rl(s))break;return!0;case"ConditionalExpression":{if(!t.experimentalTernaries){let{test:p}=s;return me(p)&&!Ut(p)}let{consequent:a,alternate:o}=s;return a.type==="ConditionalExpression"||o.type==="ConditionalExpression"}case"ClassExpression":return L(s.decorators)}if(n)return!1;let u=s,i=[];for(;;)if(u.type==="UnaryExpression"||u.type==="AwaitExpression"||u.type==="YieldExpression"&&u.argument!==null)u=u.argument,i.push("argument");else if(u.type==="TSNonNullExpression")u=u.expression,i.push("expression");else break;return!!(te(u)||e.call(()=>qi(e,t,r),...i))}function Vc(e){if(Ji(e)){let t=e.left||e.id;return t.type==="ObjectPattern"&&t.properties.length>2&&t.properties.some(r=>{var n;return Ce(r)&&(!r.shorthand||((n=r.value)==null?void 0:n.type)==="AssignmentPattern")})}return!1}function Qr(e){return e.type==="AssignmentExpression"}function Ji(e){return Qr(e)||e.type==="VariableDeclarator"}function $c(e){let t=Kc(e);if(L(t)){let r=e.type==="TSTypeAliasDeclaration"?"constraint":"bound";if(t.length>1&&t.some(n=>n[r]||n.default))return!0}return!1}var Hc=M(["TSTypeAliasDeclaration","TypeAlias"]);function Kc(e){var t;if(Hc(e))return(t=e.typeParameters)==null?void 0:t.params}function zc(e){if(e.type!=="VariableDeclarator")return!1;let{typeAnnotation:t}=e.id;if(!t||!t.typeAnnotation)return!1;let r=Mi(t.typeAnnotation);return L(r)&&r.length>1&&r.some(n=>L(Mi(n))||n.type==="TSConditionalType")}function Ts(e){var t;return e.type==="VariableDeclarator"&&((t=e.init)==null?void 0:t.type)==="ArrowFunctionExpression"}var Qc=M(["TSTypeReference","GenericTypeAnnotation"]);function Mi(e){var t;if(Qc(e))return(t=e.typeParameters)==null?void 0:t.params}function qi(e,t,r,n=!1){var i;let{node:s}=e,u=()=>qi(e,t,r,!0);if(s.type==="ChainExpression"||s.type==="TSNonNullExpression")return e.call(u,"expression");if(w(s)){if((i=zr(e,t,r).label)!=null&&i.memberChain)return!1;let o=ae(s);return!(o.length===0||o.length===1&&nr(o[0],t))||el(s,r)?!1:e.call(u,"callee")}return q(s)?e.call(u,"object"):n&&(s.type==="Identifier"||s.type==="ThisExpression")}function Zc(e,t,r){return Ce(e)?(t=Nt(t),typeof t=="string"&&Ze(t)<r.tabWidth+3):!1}function el(e,t){let r=tl(e);if(L(r)){if(r.length>1)return!0;if(r.length===1){let s=r[0];if(We(s)||_r(s)||s.type==="TSTypeLiteral"||s.type==="ObjectTypeAnnotation")return!0}let n=e.typeParameters?"typeParameters":"typeArguments";if(re(t(n)))return!0}return!1}function tl(e){var t;return(t=e.typeParameters??e.typeArguments)==null?void 0:t.params}function rl(e){function t(r){switch(r.type){case"FunctionTypeAnnotation":case"GenericTypeAnnotation":case"TSFunctionType":case"TSTypeReference":return!!r.typeParameters;default:return!1}}return t(e.checkType)||t(e.extendsType)}function Dt(e,t,r,n,s){let u=e.node,i=$(u),a=s?ze(e,r,t):"";if(i.length===0)return[a,"(",J(e,r,{filter:l=>Ke(r.originalText,I(l))===")"}),")"];let{parent:o}=e,p=St(o),m=xs(u),y=[];if(gu(e,(l,h)=>{let g=h===i.length-1;g&&u.rest&&y.push("..."),y.push(t()),!g&&(y.push(","),p||m?y.push(" "):fe(i[h],r)?y.push(C,C):y.push(d))}),n&&!sl(e)){if(re(a)||re(y))throw new mt;return D([ar(a),"(",ar(y),")"])}let c=i.every(l=>!L(l.decorators));return m&&c?[a,"(",...y,")"]:p?[a,"(",...y,")"]:(Lr(o)||yu(o)||o.type==="TypeAlias"||o.type==="UnionTypeAnnotation"||o.type==="IntersectionTypeAnnotation"||o.type==="FunctionTypeAnnotation"&&o.returnType===u)&&i.length===1&&i[0].name===null&&u.this!==i[0]&&i[0].typeAnnotation&&u.typeParameters===null&&Jt(i[0].typeAnnotation)&&!u.rest?r.arrowParens==="always"?["(",...y,")"]:y:[a,"(",E([F,...y]),b(!xu(u)&&ye(r,"all")?",":""),F,")"]}function xs(e){if(!e)return!1;let t=$(e);if(t.length!==1)return!1;let[r]=t;return!A(r)&&(r.type==="ObjectPattern"||r.type==="ArrayPattern"||r.type==="Identifier"&&r.typeAnnotation&&(r.typeAnnotation.type==="TypeAnnotation"||r.typeAnnotation.type==="TSTypeAnnotation")&&Le(r.typeAnnotation.typeAnnotation)||r.type==="FunctionTypeParam"&&Le(r.typeAnnotation)&&r!==e.rest||r.type==="AssignmentPattern"&&(r.left.type==="ObjectPattern"||r.left.type==="ArrayPattern")&&(r.right.type==="Identifier"||se(r.right)&&r.right.properties.length===0||G(r.right)&&r.right.elements.length===0))}function nl(e){let t;return e.returnType?(t=e.returnType,t.typeAnnotation&&(t=t.typeAnnotation)):e.typeAnnotation&&(t=e.typeAnnotation),t}function kt(e,t){var s;let r=nl(e);if(!r)return!1;let n=(s=e.typeParameters)==null?void 0:s.params;if(n){if(n.length>1)return!1;if(n.length===1){let u=n[0];if(u.constraint||u.default)return!1}}return $(e).length===1&&(Le(r)||re(t))}function sl(e){return e.match(t=>t.type==="ArrowFunctionExpression"&&t.body.type==="BlockStatement",(t,r)=>{if(t.type==="CallExpression"&&r==="arguments"&&t.arguments.length===1&&t.callee.type==="CallExpression"){let n=t.callee.callee;return n.type==="Identifier"||n.type==="MemberExpression"&&!n.computed&&n.object.type==="Identifier"&&n.property.type==="Identifier"}return!1},(t,r)=>t.type==="VariableDeclarator"&&r==="init"||t.type==="ExportDefaultDeclaration"&&r==="declaration"||t.type==="TSExportAssignment"&&r==="expression"||t.type==="AssignmentExpression"&&r==="right"&&t.left.type==="MemberExpression"&&t.left.object.type==="Identifier"&&t.left.object.name==="module"&&t.left.property.type==="Identifier"&&t.left.property.name==="exports",t=>t.type!=="VariableDeclaration"||t.kind==="const"&&t.declarations.length===1)}function Wi(e){let t=$(e);return t.length>1&&t.some(r=>r.type==="TSParameterProperty")}var ul=M(["VoidTypeAnnotation","TSVoidKeyword","NullLiteralTypeAnnotation","TSNullKeyword"]),il=M(["ObjectTypeAnnotation","TSTypeLiteral","GenericTypeAnnotation","TSTypeReference"]);function al(e){let{types:t}=e;if(t.some(n=>A(n)))return!1;let r=t.find(n=>il(n));return r?t.every(n=>n===r||ul(n)):!1}function gs(e){return Jt(e)||Le(e)?!0:We(e)?al(e):!1}function Ni(e,t,r){let n=t.semi?";":"",{node:s}=e,u=[ne(e),"opaque type ",r("id"),r("typeParameters")];return s.supertype&&u.push(": ",r("supertype")),s.impltype&&u.push(" = ",r("impltype")),u.push(n),u}function Zr(e,t,r){let n=t.semi?";":"",{node:s}=e,u=[ne(e)];u.push("type ",r("id"),r("typeParameters"));let i=s.type==="TSTypeAliasDeclaration"?"typeAnnotation":"right";return[dt(e,t,r,u," =",i),n]}function en(e,t,r){let n=!1;return D(e.map(({isFirst:s,previous:u,node:i,index:a})=>{let o=r();if(s)return o;let p=Le(i),m=Le(u);return m&&p?[" & ",n?E(o):o]:!m&&!p?E([" &",d,o]):(a>1&&(n=!0),[" & ",a>1?E(o):o])},"types"))}function tn(e,t,r){let{node:n}=e,{parent:s}=e,u=s.type!=="TypeParameterInstantiation"&&(s.type!=="TSConditionalType"||!t.experimentalTernaries)&&(s.type!=="ConditionalTypeAnnotation"||!t.experimentalTernaries)&&s.type!=="TSTypeParameterInstantiation"&&s.type!=="GenericTypeAnnotation"&&s.type!=="TSTypeReference"&&s.type!=="TSTypeAssertion"&&s.type!=="TupleTypeAnnotation"&&s.type!=="TSTupleType"&&!(s.type==="FunctionTypeParam"&&!s.name&&e.grandparent.this!==s)&&!((s.type==="TypeAlias"||s.type==="VariableDeclarator"||s.type==="TSTypeAliasDeclaration")&&we(t.originalText,n)),i=gs(n),a=e.map(m=>{let y=r();return i||(y=ge(2,y)),le(m,y,t)},"types");if(i)return P(" | ",a);let o=u&&!we(t.originalText,n),p=[b([o?d:"","| "]),P([d,"| "],a)];return Be(e,t)?D([E(p),F]):(s.type==="TupleTypeAnnotation"||s.type==="TSTupleType")&&s[s.type==="TupleTypeAnnotation"&&s.types?"types":"elementTypes"].length>1?D([E([b(["(",F]),p]),F,b(")")]):D(u?E(p):p)}function ol(e){var n;let{node:t,parent:r}=e;return t.type==="FunctionTypeAnnotation"&&(Lr(r)||!((r.type==="ObjectTypeProperty"||r.type==="ObjectTypeInternalSlot")&&!r.variance&&!r.optional&&gt(r,t)||r.type==="ObjectTypeCallProperty"||((n=e.getParentNode(2))==null?void 0:n.type)==="DeclareFunction"))}function rn(e,t,r){let{node:n}=e,s=[Yt(e)];(n.type==="TSConstructorType"||n.type==="TSConstructSignatureDeclaration")&&s.push("new ");let u=Dt(e,r,t,!1,!0),i=[];return n.type==="FunctionTypeAnnotation"?i.push(ol(e)?" => ":": ",r("returnType")):i.push(Y(e,r,n.returnType?"returnType":"typeAnnotation")),kt(n,i)&&(u=D(u)),s.push(u,i),D(s)}function nn(e,t,r){return[r("objectType"),V(e),"[",r("indexType"),"]"]}function sn(e,t,r){return["infer ",r("typeParameter")]}function hs(e,t,r){let{node:n}=e;return[n.postfix?"":r,Y(e,t),n.postfix?r:""]}function un(e,t,r){let{node:n}=e;return["...",...n.type==="TupleTypeSpreadElement"&&n.label?[r("label"),": "]:[],r("typeAnnotation")]}function an(e,t,r){let{node:n}=e;return[n.variance?r("variance"):"",r("label"),n.optional?"?":"",": ",r("elementType")]}var pl=new WeakSet;function Y(e,t,r="typeAnnotation"){let{node:{[r]:n}}=e;if(!n)return"";let s=!1;if(n.type==="TSTypeAnnotation"||n.type==="TypeAnnotation"){let u=e.call(Gi,r);(u==="=>"||u===":"&&A(n,x.Leading))&&(s=!0),pl.add(n)}return s?[" ",t(r)]:t(r)}var Gi=e=>e.match(t=>t.type==="TSTypeAnnotation",(t,r)=>(r==="returnType"||r==="typeAnnotation")&&(t.type==="TSFunctionType"||t.type==="TSConstructorType"))?"=>":e.match(t=>t.type==="TSTypeAnnotation",(t,r)=>r==="typeAnnotation"&&(t.type==="TSJSDocNullableType"||t.type==="TSJSDocNonNullableType"||t.type==="TSTypePredicate"))||e.match(t=>t.type==="TypeAnnotation",(t,r)=>r==="typeAnnotation"&&t.type==="Identifier",(t,r)=>r==="id"&&t.type==="DeclareFunction")||e.match(t=>t.type==="TypeAnnotation",(t,r)=>r==="bound"&&t.type==="TypeParameter"&&t.usesExtendsBound)?"":":";function on(e,t,r){let n=Gi(e);return n?[n," ",r("typeAnnotation")]:r("typeAnnotation")}function pn(e){return[e("elementType"),"[]"]}function cn({node:e},t){let r=e.type==="TSTypeQuery"?"exprName":"argument",n=e.type==="TSTypeQuery"?"typeParameters":"typeArguments";return["typeof ",t(r),t(n)]}function ln(e,t){let{node:r}=e;return[r.asserts?"asserts ":"",t("parameterName"),r.typeAnnotation?[" is ",Y(e,t)]:""]}function V(e){let{node:t}=e;return!t.optional||t.type==="Identifier"&&t===e.parent.key?"":w(t)||q(t)&&t.computed||t.type==="OptionalIndexedAccessType"?"?.":"?"}function mn(e){return e.node.definite||e.match(void 0,(t,r)=>r==="id"&&t.type==="VariableDeclarator"&&t.definite)?"!":""}var cl=new Set(["DeclareClass","DeclareFunction","DeclareVariable","DeclareExportDeclaration","DeclareExportAllDeclaration","DeclareOpaqueType","DeclareTypeAlias","DeclareEnum","DeclareInterface"]);function ne(e){let{node:t}=e;return t.declare||cl.has(t.type)&&e.parent.type!=="DeclareExportDeclaration"?"declare ":""}var ll=new Set(["TSAbstractMethodDefinition","TSAbstractPropertyDefinition","TSAbstractAccessorProperty"]);function Yt({node:e}){return e.abstract||ll.has(e.type)?"abstract ":""}function ze(e,t,r){let n=e.node;return n.typeArguments?r("typeArguments"):n.typeParameters?r("typeParameters"):""}function Kr(e,t,r){return["::",r("callee")]}function yt(e,t,r){return e.type==="EmptyStatement"?";":e.type==="BlockStatement"||r?[" ",t]:E([d,t])}function Dn(e,t){return["...",t("argument"),Y(e,t)]}function Xt(e){return e.accessibility?e.accessibility+" ":""}function ml(e,t,r,n){let{node:s}=e;return A(s,x.Dangling)?D([r,J(e,t,{indent:!0}),F,n]):[r,n]}function Vt(e,t,r){let{node:n}=e,s=[],u=n.type==="TupleExpression"?"#[":"[",i="]",a=n.type==="TupleTypeAnnotation"&&n.types?"types":n.type==="TSTupleType"||n.type==="TupleTypeAnnotation"?"elementTypes":"elements",o=n[a];if(o.length===0)s.push(ml(e,t,u,i));else{let p=v(!1,o,-1),m=(p==null?void 0:p.type)!=="RestElement",y=p===null,c=Symbol("array"),f=!t.__inJestEach&&o.length>1&&o.every((g,S,B)=>{let O=g==null?void 0:g.type;if(!G(g)&&!se(g))return!1;let R=B[S+1];if(R&&O!==R.type)return!1;let _=G(g)?"elements":"properties";return g[_]&&g[_].length>1}),l=As(n,t),h=m?y?",":ye(t)?l?b(",","",{groupId:c}):b(","):"":"";s.push(D([u,E([F,l?yl(e,t,r,h):[Dl(e,t,a,r),h],J(e,t)]),F,i],{shouldBreak:f,id:c}))}return s.push(V(e),Y(e,r)),s}function As(e,t){return G(e)&&e.elements.length>1&&e.elements.every(r=>r&&(Pe(r)||qn(r)&&!A(r.argument))&&!A(r,x.Trailing|x.Line,n=>!Z(t.originalText,j(n),{backwards:!0})))}function Ui({node:e},{originalText:t}){let r=s=>Ot(t,vt(t,s)),n=s=>t[s]===","?s:n(r(s+1));return _t(t,n(I(e)))}function Dl(e,t,r,n){let s=[];return e.each(({node:u,isLast:i})=>{s.push(u?D(n()):""),i||s.push([",",d,u&&Ui(e,t)?F:""])},r),s}function yl(e,t,r,n){let s=[];return e.each(({isLast:u,next:i})=>{s.push([r(),u?n:","]),u||s.push(Ui(e,t)?[C,C]:A(i,x.Leading|x.Line)?C:d)},"elements"),Wt(s)}var Yi=new Proxy(()=>{},{get:()=>Yi}),yn=Yi;function fl(e){return e.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(?=\d)/,"$1$2").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")}var Qe=fl;var fn=new WeakMap;function ft(e,t,r){let{node:n}=e;if(n.computed)return["[",r("key"),"]"];let{parent:s}=e,{key:u}=n;if(t.quoteProps==="consistent"&&!fn.has(s)){let i=(s.properties||s.body||s.members).some(a=>!a.computed&&a.key&&te(a.key)&&!Gn(a,t));fn.set(s,i)}if((u.type==="Identifier"||Pe(u)&&Un(Qe(De(u)))&&String(u.value)===Qe(De(u))&&!(t.parser==="typescript"||t.parser==="babel-ts"))&&(t.parser==="json"||t.parser==="jsonc"||t.quoteProps==="consistent"&&fn.get(s))){let i=at(JSON.stringify(u.type==="Identifier"?u.name:u.value.toString()),t);return e.call(a=>le(a,i,t),"key")}return Gn(n,t)&&(t.quoteProps==="as-needed"||t.quoteProps==="consistent"&&!fn.get(s))?e.call(i=>le(i,/^\d/.test(u.value)?Qe(u.value):u.value,t),"key"):r("key")}function Ss(e,t,r){let{node:n}=e;return n.shorthand?r("value"):dt(e,t,r,ft(e,t,r),":","value")}var El=({node:e,key:t,parent:r})=>t==="value"&&e.type==="FunctionExpression"&&(r.type==="ObjectMethod"||r.type==="ClassMethod"||r.type==="ClassPrivateMethod"||r.type==="MethodDefinition"||r.type==="TSAbstractMethodDefinition"||r.type==="TSDeclareMethod"||r.type==="Property"&&ht(r));function En(e,t,r,n){if(El(e))return Fn(e,r,t);let{node:s}=e,u=!1;if((s.type==="FunctionDeclaration"||s.type==="FunctionExpression")&&(n!=null&&n.expandLastArg)){let{parent:m}=e;w(m)&&(ae(m).length>1||$(s).every(y=>y.type==="Identifier"&&!y.typeAnnotation))&&(u=!0)}let i=[ne(e),s.async?"async ":"",`function${s.generator?"*":""} `,s.id?t("id"):""],a=Dt(e,t,r,u),o=An(e,t),p=kt(s,o);return i.push(ze(e,r,t),D([p?D(a):a,o]),s.body?" ":"",t("body")),r.semi&&(s.declare||!s.body)&&i.push(";"),i}function yr(e,t,r){let{node:n}=e,{kind:s}=n,u=n.value||n,i=[];return!s||s==="init"||s==="method"||s==="constructor"?u.async&&i.push("async "):(yn.ok(s==="get"||s==="set"),i.push(s," ")),u.generator&&i.push("*"),i.push(ft(e,t,r),n.optional||n.key.optional?"?":"",n===u?Fn(e,t,r):r("value")),i}function Fn(e,t,r){let{node:n}=e,s=Dt(e,r,t),u=An(e,r),i=Wi(n),a=kt(n,u),o=[ze(e,t,r),D([i?D(s,{shouldBreak:!0}):a?D(s):s,u])];return n.body?o.push(" ",r("body")):o.push(t.semi?";":""),o}function Fl(e){let t=$(e);return t.length===1&&!e.typeParameters&&!A(e,x.Dangling)&&t[0].type==="Identifier"&&!t[0].typeAnnotation&&!A(t[0])&&!t[0].optional&&!e.predicate&&!e.returnType}function Cn(e,t){if(t.arrowParens==="always")return!1;if(t.arrowParens==="avoid"){let{node:r}=e;return Fl(r)}return!1}function An(e,t){let{node:r}=e,s=[Y(e,t,"returnType")];return r.predicate&&s.push(t("predicate")),s}function Xi(e,t,r){let{node:n}=e,s=t.semi?";":"",u=[];if(n.argument){let o=r("argument");Cl(t,n.argument)?o=["(",E([C,o]),C,")"]:(me(n.argument)||n.argument.type==="SequenceExpression"||t.experimentalTernaries&&n.argument.type==="ConditionalExpression"&&(n.argument.consequent.type==="ConditionalExpression"||n.argument.alternate.type==="ConditionalExpression"))&&(o=D([b("("),E([F,o]),F,b(")")])),u.push(" ",o)}let i=A(n,x.Dangling),a=s&&i&&A(n,x.Last|x.Line);return a&&u.push(s),i&&u.push(" ",J(e,t)),a||u.push(s),u}function Vi(e,t,r){return["return",Xi(e,t,r)]}function $i(e,t,r){return["throw",Xi(e,t,r)]}function Cl(e,t){if(we(e.originalText,t)||A(t,x.Leading,r=>Te(e.originalText,j(r),I(r)))&&!U(t))return!0;if(jt(t)){let r=t,n;for(;n=lu(r);)if(r=n,we(e.originalText,r))return!0}return!1}var Bs=new WeakMap;function Hi(e){return Bs.has(e)||Bs.set(e,e.type==="ConditionalExpression"&&!ie(e,t=>t.type==="ObjectExpression")),Bs.get(e)}var Ki=e=>e.type==="SequenceExpression";function zi(e,t,r,n={}){let s=[],u,i=[],a=!1,o=!n.expandLastArg&&e.node.body.type==="ArrowFunctionExpression",p;(function g(){let{node:S}=e,B=Al(e,t,r,n);if(s.length===0)s.push(B);else{let{leading:O,trailing:R}=ms(e,t);s.push([O,B]),i.unshift(R)}o&&(a||(a=S.returnType&&$(S).length>0||S.typeParameters||$(S).some(O=>O.type!=="Identifier"))),!o||S.body.type!=="ArrowFunctionExpression"?(u=r("body",n),p=S.body):e.call(g,"body")})();let m=!we(t.originalText,p)&&(Ki(p)||dl(p,u,t)||!a&&Hi(p)),y=e.key==="callee"&&pt(e.parent),c=Symbol("arrow-chain"),f=Tl(e,n,{signatureDocs:s,shouldBreak:a}),l,h=!1;return o&&(y||n.assignmentLayout)&&(h=!0,l=n.assignmentLayout==="chain-tail-arrow-chain"||y&&!m),u=xl(e,t,n,{bodyDoc:u,bodyComments:i,functionBody:p,shouldPutBodyOnSameLine:m}),D([D(h?E([F,f]):f,{shouldBreak:l,id:c})," =>",o?Ct(u,{groupId:c}):D(u),o&&y?b(F,"",{groupId:c}):""])}function Al(e,t,r,n){let{node:s}=e,u=[];if(s.async&&u.push("async "),Cn(e,t))u.push(r(["params",0]));else{let a=n.expandLastArg||n.expandFirstArg,o=An(e,r);if(a){if(re(o))throw new mt;o=D(ar(o))}u.push(D([Dt(e,r,t,a,!0),o]))}let i=J(e,t,{filter(a){let o=lt(t.originalText,I(a));return o!==!1&&t.originalText.slice(o,o+2)==="=>"}});return i&&u.push(" ",i),u}function dl(e,t,r){var n,s;return G(e)||se(e)||e.type==="ArrowFunctionExpression"||e.type==="DoExpression"||e.type==="BlockStatement"||U(e)||((n=t.label)==null?void 0:n.hug)!==!1&&(((s=t.label)==null?void 0:s.embed)||wr(e,r.originalText))}function Tl(e,t,{signatureDocs:r,shouldBreak:n}){if(r.length===1)return r[0];let{parent:s,key:u}=e;return u!=="callee"&&pt(s)||me(s)?D([r[0]," =>",E([d,P([" =>",d],r.slice(1))])],{shouldBreak:n}):u==="callee"&&pt(s)||t.assignmentLayout?D(P([" =>",d],r),{shouldBreak:n}):D(E(P([" =>",d],r)),{shouldBreak:n})}function xl(e,t,r,{bodyDoc:n,bodyComments:s,functionBody:u,shouldPutBodyOnSameLine:i}){let{node:a,parent:o}=e,p=r.expandLastArg&&ye(t,"all")?b(","):"",m=(r.expandLastArg||o.type==="JSXExpressionContainer")&&!A(a)?F:"";return i&&Hi(u)?[" ",D([b("","("),E([F,n]),b("",")"),p,m]),s]:(Ki(u)&&(n=D(["(",E([F,n]),F,")"])),i?[" ",n,s]:[E([d,n,s]),p,m])}var gl=(e,t,r)=>{if(!(e&&t==null)){if(t.findLast)return t.findLast(r);for(let n=t.length-1;n>=0;n--){let s=t[n];if(r(s,n,t))return s}}},Qi=gl;function fr(e,t,r,n){let{node:s}=e,u=[],i=Qi(!1,s[n],a=>a.type!=="EmptyStatement");return e.each(({node:a})=>{a.type!=="EmptyStatement"&&(u.push(r()),a!==i&&(u.push(C),fe(a,t)&&u.push(C)))},n),u}function dn(e,t,r){let{node:n}=e,s=[];n.type==="StaticBlock"&&s.push("static "),s.push("{");let u=bs(e,t,r);if(u)s.push(E([C,u]),C);else{let{parent:i}=e,a=e.grandparent;i.type==="ArrowFunctionExpression"||i.type==="FunctionExpression"||i.type==="FunctionDeclaration"||i.type==="ObjectMethod"||i.type==="ClassMethod"||i.type==="ClassPrivateMethod"||i.type==="ForStatement"||i.type==="WhileStatement"||i.type==="DoWhileStatement"||i.type==="DoExpression"||i.type==="CatchClause"&&!a.finalizer||i.type==="TSModuleDeclaration"||i.type==="TSDeclareFunction"||n.type==="StaticBlock"||s.push(C)}return s.push("}"),s}function bs(e,t,r){var o;let{node:n}=e,s=L(n.directives),u=n.body.some(p=>p.type!=="EmptyStatement"),i=A(n,x.Dangling);if(!s&&!u&&!i)return"";let a=[];return s&&(a.push(fr(e,t,r,"directives")),(u||i)&&(a.push(C),fe(v(!1,n.directives,-1),t)&&a.push(C))),u&&a.push(fr(e,t,r,"body")),i&&a.push(J(e,t)),n.type==="Program"&&((o=e.parent)==null?void 0:o.type)!=="ModuleExpression"&&a.push(C),a}function hl(e){let t=new WeakMap;return function(r){return t.has(r)||t.set(r,Symbol(e)),t.get(r)}}var Tn=hl;function Sl(e){switch(e){case null:return"";case"PlusOptional":return"+?";case"MinusOptional":return"-?";case"Optional":return"?"}}function Zi(e,t,r){let{node:n}=e;return D([n.variance?r("variance"):"","[",E([r("keyTparam")," in ",r("sourceType")]),"]",Sl(n.optional),": ",r("propType")])}function Ps(e,t){return e==="+"||e==="-"?e+t:t}function ea(e,t,r){let{node:n}=e,s=Te(t.originalText,j(n),j(n.typeParameter));return D(["{",E([t.bracketSpacing?d:F,D([r("typeParameter"),n.optional?Ps(n.optional,"?"):"",n.typeAnnotation?": ":"",r("typeAnnotation")]),t.semi?b(";"):""]),J(e,t),t.bracketSpacing?d:F,"}"],{shouldBreak:s})}var Er=Tn("typeParameters");function Bl(e,t,r){let{node:n}=e;return $(n).length===1&&n.type.startsWith("TS")&&!n[r][0].constraint&&e.parent.type==="ArrowFunctionExpression"&&!(t.filepath&&/\.ts$/.test(t.filepath))}function It(e,t,r,n){let{node:s}=e;if(!s[n])return"";if(!Array.isArray(s[n]))return r(n);let u=e.getNode(2),i=u&&St(u),a=e.match(m=>!(m[n].length===1&&Le(m[n][0])),void 0,(m,y)=>y==="typeAnnotation",m=>m.type==="Identifier",Ts);if(s[n].length===0||!a&&(i||s[n].length===1&&(s[n][0].type==="NullableTypeAnnotation"||gs(s[n][0]))))return["<",P(", ",e.map(r,n)),bl(e,t),">"];let p=s.type==="TSTypeParameterInstantiation"?"":Bl(e,t,n)?",":ye(t)?b(","):"";return D(["<",E([F,P([",",d],e.map(r,n))]),p,F,">"],{id:Er(s)})}function bl(e,t){let{node:r}=e;if(!A(r,x.Dangling))return"";let n=!A(r,x.Line),s=J(e,t,{indent:!n});return n?s:[s,C]}function xn(e,t,r){let{node:n,parent:s}=e,u=[n.type==="TSTypeParameter"&&n.const?"const ":""],i=n.type==="TSTypeParameter"?r("name"):n.name;if(s.type==="TSMappedType")return s.readonly&&u.push(Ps(s.readonly,"readonly")," "),u.push("[",i),n.constraint&&u.push(" in ",r("constraint")),s.nameType&&u.push(" as ",e.callParent(()=>r("nameType"))),u.push("]"),u;if(n.variance&&u.push(r("variance")),n.in&&u.push("in "),n.out&&u.push("out "),u.push(i),n.bound&&(n.usesExtendsBound&&u.push(" extends "),u.push(Y(e,r,"bound"))),n.constraint){let a=Symbol("constraint");u.push(" extends",D(E(d),{id:a}),ke,Ct(r("constraint"),{groupId:a}))}return n.default&&u.push(" = ",r("default")),D(u)}var ta=M(["ClassProperty","PropertyDefinition","ClassPrivateProperty","ClassAccessorProperty","AccessorProperty","TSAbstractPropertyDefinition","TSAbstractAccessorProperty"]);function gn(e,t,r){let{node:n}=e,s=[ne(e),Yt(e),"class"],u=A(n.id,x.Trailing)||A(n.typeParameters,x.Trailing)||A(n.superClass)||L(n.extends)||L(n.mixins)||L(n.implements),i=[],a=[];if(n.id&&i.push(" ",r("id")),i.push(r("typeParameters")),n.superClass){let o=[kl(e,t,r),r("superTypeParameters")],p=e.call(m=>["extends ",le(m,o,t)],"superClass");u?a.push(d,D(p)):a.push(" ",p)}else a.push(ks(e,t,r,"extends"));if(a.push(ks(e,t,r,"mixins"),ks(e,t,r,"implements")),u){let o;na(n)?o=[...i,E(a)]:o=E([...i,a]),s.push(D(o,{id:ra(n)}))}else s.push(...i,...a);return s.push(" ",r("body")),s}var ra=Tn("heritageGroup");function Is(e){return b(C,"",{groupId:ra(e)})}function Pl(e){return["extends","mixins","implements"].reduce((t,r)=>t+(Array.isArray(e[r])?e[r].length:0),e.superClass?1:0)>1}function na(e){return e.typeParameters&&!A(e.typeParameters,x.Trailing|x.Line)&&!Pl(e)}function ks(e,t,r,n){let{node:s}=e;if(!L(s[n]))return"";let u=J(e,t,{marker:n});return[na(s)?b(" ",d,{groupId:Er(s.typeParameters)}):d,u,u&&C,n,D(E([d,P([",",d],e.map(r,n))]))]}function kl(e,t,r){let n=r("superClass"),{parent:s}=e;return s.type==="AssignmentExpression"?D(b(["(",E([F,n]),F,")"],n)):n}function hn(e,t,r){let{node:n}=e,s=[];return L(n.decorators)&&s.push(Cs(e,t,r)),s.push(Xt(n)),n.static&&s.push("static "),s.push(Yt(e)),n.override&&s.push("override "),s.push(yr(e,t,r)),s}function Sn(e,t,r){let{node:n}=e,s=[],u=t.semi?";":"";L(n.decorators)&&s.push(Cs(e,t,r)),s.push(Xt(n),ne(e)),n.static&&s.push("static "),s.push(Yt(e)),n.override&&s.push("override "),n.readonly&&s.push("readonly "),n.variance&&s.push(r("variance")),(n.type==="ClassAccessorProperty"||n.type==="AccessorProperty"||n.type==="TSAbstractAccessorProperty")&&s.push("accessor "),s.push(ft(e,t,r),V(e),mn(e),Y(e,r));let i=n.type==="TSAbstractPropertyDefinition"||n.type==="TSAbstractAccessorProperty";return[dt(e,t,r,s," =",i?void 0:"value"),u]}function sa(e,t,r){let{node:n}=e,s=[];return e.each(({node:u,next:i,isLast:a})=>{s.push(r()),!t.semi&&ta(u)&&Il(u,i)&&s.push(";"),a||(s.push(C),fe(u,t)&&s.push(C))},"body"),A(n,x.Dangling)&&s.push(J(e,t)),[L(n.body)?Is(e.parent):"","{",s.length>0?[E([C,s]),C]:"","}"]}function Il(e,t){var s;let{type:r,name:n}=e.key;if(!e.computed&&r==="Identifier"&&(n==="static"||n==="get"||n==="set")&&!e.value&&!e.typeAnnotation)return!0;if(!t||t.static||t.accessibility)return!1;if(!t.computed){let u=(s=t.key)==null?void 0:s.name;if(u==="in"||u==="instanceof")return!0}if(ta(t)&&t.variance&&!t.static&&!t.declare)return!0;switch(t.type){case"ClassProperty":case"PropertyDefinition":case"TSAbstractPropertyDefinition":return t.computed;case"MethodDefinition":case"TSAbstractMethodDefinition":case"ClassMethod":case"ClassPrivateMethod":{if((t.value?t.value.async:t.async)||t.kind==="get"||t.kind==="set")return!1;let i=t.value?t.value.generator:t.generator;return!!(t.computed||i)}case"TSIndexSignature":return!0}return!1}function ua(e,t){if(t.semi||Ls(e,t)||Os(e,t))return!1;let{node:r,key:n,parent:s}=e;return!!(r.type==="ExpressionStatement"&&(n==="body"&&(s.type==="Program"||s.type==="BlockStatement"||s.type==="StaticBlock"||s.type==="TSModuleBlock")||n==="consequent"&&s.type==="SwitchCase")&&e.call(()=>ia(e,t),"expression"))}function ia(e,t){let{node:r}=e;switch(r.type){case"ParenthesizedExpression":case"TypeCastExpression":case"ArrayExpression":case"ArrayPattern":case"TemplateLiteral":case"TemplateElement":case"RegExpLiteral":return!0;case"ArrowFunctionExpression":if(!Cn(e,t))return!0;break;case"UnaryExpression":{let{prefix:n,operator:s}=r;if(n&&(s==="+"||s==="-"))return!0;break}case"BindExpression":if(!r.object)return!0;break;case"Literal":if(r.regex)return!0;break;default:if(U(r))return!0}return Be(e,t)?!0:jt(r)?e.call(()=>ia(e,t),...Ir(r)):!1}function Ls({node:e,parent:t},r){return(r.parentParser==="markdown"||r.parentParser==="mdx")&&e.type==="ExpressionStatement"&&U(e.expression)&&t.type==="Program"&&t.body.length===1}function ws(e){switch(e.type){case"MemberExpression":switch(e.property.type){case"Identifier":case"NumericLiteral":case"StringLiteral":return ws(e.object)}return!1;case"Identifier":return!0;default:return!1}}function Os({node:e,parent:t},r){return(r.parser==="__vue_event_binding"||r.parser==="__vue_ts_event_binding")&&e.type==="ExpressionStatement"&&t.type==="Program"&&t.body.length===1}function aa(e,t,r){let n=[r("expression")];return Os(e,t)?ws(e.node.expression)&&n.push(";"):Ls(e,t)||t.semi&&n.push(";"),n}function oa(e,t,r){if(t.__isVueBindings||t.__isVueForBindingLeft){let n=e.map(r,"program","body",0,"params");if(n.length===1)return n[0];let s=P([",",d],n);return t.__isVueForBindingLeft?["(",E([F,D(s)]),F,")"]:s}if(t.__isEmbeddedTypescriptGenericParameters){let n=e.map(r,"program","body",0,"typeParameters","params");return P([",",d],n)}}function la(e,t){let{node:r}=e;switch(r.type){case"RegExpLiteral":return pa(r);case"BigIntLiteral":return Bn(r.extra.raw);case"NumericLiteral":return Qe(r.extra.raw);case"StringLiteral":return Ie(at(r.extra.raw,t));case"NullLiteral":return"null";case"BooleanLiteral":return String(r.value);case"DecimalLiteral":return Qe(r.value)+"m";case"DirectiveLiteral":return ca(r.extra.raw,t);case"Literal":{if(r.regex)return pa(r.regex);if(r.bigint)return Bn(r.raw);if(r.decimal)return Qe(r.decimal)+"m";let{value:n}=r;return typeof n=="number"?Qe(r.raw):typeof n=="string"?Ll(e)?ca(r.raw,t):Ie(at(r.raw,t)):String(n)}}}function Ll(e){if(e.key!=="expression")return;let{parent:t}=e;return t.type==="ExpressionStatement"&&t.directive}function Bn(e){return e.toLowerCase()}function pa({pattern:e,flags:t}){return t=[...t].sort().join(""),`/${e}/${t}`}function ca(e,t){let r=e.slice(1,-1);if(r.includes('"')||r.includes("'"))return e;let n=t.singleQuote?"'":'"';return n+r+n}function wl(e,t,r){let n=e.originalText.slice(t,r);for(let s of e[Symbol.for("comments")]){let u=j(s);if(u>r)break;let i=I(s);if(i<t)continue;let a=i-u;n=n.slice(0,u-t)+" ".repeat(a)+n.slice(i-t)}return n}var Fr=wl;function ma(e,t,r){let{node:n}=e;return["import",n.module?" module":"",n.phase?` ${n.phase}`:"",_s(n),fa(e,t,r),ya(e,t,r),Fa(e,t,r),t.semi?";":""]}var Da=e=>e.type==="ExportDefaultDeclaration"||e.type==="DeclareExportDeclaration"&&e.default;function bn(e,t,r){let{node:n}=e,s=[Pi(e,t,r),ne(e),"export",Da(n)?" default":""],{declaration:u,exported:i}=n;return A(n,x.Dangling)&&(s.push(" ",J(e,t)),Or(n)&&s.push(C)),u?s.push(" ",r("declaration")):(s.push(_l(n)),n.type==="ExportAllDeclaration"||n.type==="DeclareExportAllDeclaration"?(s.push(" *"),i&&s.push(" as ",r("exported"))):s.push(fa(e,t,r)),s.push(ya(e,t,r),Fa(e,t,r))),s.push(vl(n,t)),s}var Ol=M(["ClassDeclaration","FunctionDeclaration","TSInterfaceDeclaration","DeclareClass","DeclareFunction","TSDeclareFunction","EnumDeclaration"]);function vl(e,t){return t.semi&&(!e.declaration||Da(e)&&!Ol(e.declaration))?";":""}function vs(e,t=!0){return e&&e!=="value"?`${t?" ":""}${e}${t?"":" "}`:""}function _s(e,t){return vs(e.importKind,t)}function _l(e){return vs(e.exportKind)}function ya(e,t,r){let{node:n}=e;if(!n.source)return"";let s=[];return Ea(n,t)&&s.push(" from"),s.push(" ",r("source")),s}function fa(e,t,r){let{node:n}=e;if(!Ea(n,t))return"";let s=[" "];if(L(n.specifiers)){let u=[],i=[];e.each(()=>{let a=e.node.type;if(a==="ExportNamespaceSpecifier"||a==="ExportDefaultSpecifier"||a==="ImportNamespaceSpecifier"||a==="ImportDefaultSpecifier")u.push(r());else if(a==="ExportSpecifier"||a==="ImportSpecifier")i.push(r());else throw new _e(n,"specifier")},"specifiers"),s.push(P(", ",u)),i.length>0&&(u.length>0&&s.push(", "),i.length>1||u.length>0||n.specifiers.some(o=>A(o))?s.push(D(["{",E([t.bracketSpacing?d:F,P([",",d],i)]),b(ye(t)?",":""),t.bracketSpacing?d:F,"}"])):s.push(["{",t.bracketSpacing?" ":"",...i,t.bracketSpacing?" ":"","}"]))}else s.push("{}");return s}function Ea(e,t){return e.type!=="ImportDeclaration"||L(e.specifiers)||e.importKind==="type"?!0:Fr(t,j(e),j(e.source)).trimEnd().endsWith("from")}function Ml(e,t){var n,s;if((n=e.extra)!=null&&n.deprecatedAssertSyntax)return"assert";let r=Fr(t,I(e.source),(s=e.attributes)!=null&&s[0]?j(e.attributes[0]):I(e)).trimStart();return r.startsWith("assert")?"assert":r.startsWith("with")||L(e.attributes)?"with":void 0}function Fa(e,t,r){let{node:n}=e;if(!n.source)return"";let s=Ml(n,t);if(!s)return"";let u=[` ${s} {`];return L(n.attributes)&&(t.bracketSpacing&&u.push(" "),u.push(P(", ",e.map(r,"attributes"))),t.bracketSpacing&&u.push(" ")),u.push("}"),u}function Ca(e,t,r){let{node:n}=e,{type:s}=n,u=s.startsWith("Import"),i=u?"imported":"local",a=u?"local":"exported",o=n[i],p=n[a],m="",y="";return s==="ExportNamespaceSpecifier"||s==="ImportNamespaceSpecifier"?m="*":o&&(m=r(i)),p&&!jl(n)&&(y=r(a)),[vs(s==="ImportSpecifier"?n.importKind:n.exportKind,!1),m,m&&y?" as ":"",y]}function jl(e){if(e.type!=="ImportSpecifier"&&e.type!=="ExportSpecifier")return!1;let{local:t,[e.type==="ImportSpecifier"?"imported":"exported"]:r}=e;if(t.type!==r.type||!iu(t,r))return!1;if(te(t))return t.value===r.value&&De(t)===De(r);switch(t.type){case"Identifier":return t.name===r.name;default:return!1}}function Tt(e,t,r){var _;let n=t.semi?";":"",{node:s}=e,u=s.type==="ObjectTypeAnnotation",i=s.type==="TSEnumDeclaration"||s.type==="EnumBooleanBody"||s.type==="EnumNumberBody"||s.type==="EnumStringBody"||s.type==="EnumSymbolBody",a=[s.type==="TSTypeLiteral"||i?"members":s.type==="TSInterfaceBody"?"body":"properties"];u&&a.push("indexers","callProperties","internalSlots");let o=a.flatMap(T=>e.map(({node:W})=>({node:W,printed:r(),loc:j(W)}),T));a.length>1&&o.sort((T,W)=>T.loc-W.loc);let{parent:p,key:m}=e,y=u&&m==="body"&&(p.type==="InterfaceDeclaration"||p.type==="DeclareInterface"||p.type==="DeclareClass"),c=s.type==="TSInterfaceBody"||i||y||s.type==="ObjectPattern"&&p.type!=="FunctionDeclaration"&&p.type!=="FunctionExpression"&&p.type!=="ArrowFunctionExpression"&&p.type!=="ObjectMethod"&&p.type!=="ClassMethod"&&p.type!=="ClassPrivateMethod"&&p.type!=="AssignmentPattern"&&p.type!=="CatchClause"&&s.properties.some(T=>T.value&&(T.value.type==="ObjectPattern"||T.value.type==="ArrayPattern"))||s.type!=="ObjectPattern"&&o.length>0&&Te(t.originalText,j(s),o[0].loc),f=y?";":s.type==="TSInterfaceBody"||s.type==="TSTypeLiteral"?b(n,";"):",",l=s.type==="RecordExpression"?"#{":s.exact?"{|":"{",h=s.exact?"|}":"}",g=[],S=o.map(T=>{let W=[...g,D(T.printed)];return g=[f,d],(T.node.type==="TSPropertySignature"||T.node.type==="TSMethodSignature"||T.node.type==="TSConstructSignatureDeclaration"||T.node.type==="TSCallSignatureDeclaration")&&A(T.node,x.PrettierIgnore)&&g.shift(),fe(T.node,t)&&g.push(C),W});if(s.inexact||s.hasUnknownMembers){let T;if(A(s,x.Dangling)){let W=A(s,x.Line);T=[J(e,t),W||Z(t.originalText,I(v(!1,ot(s),-1)))?C:d,"..."]}else T=["..."];S.push([...g,...T])}let B=(_=v(!1,o,-1))==null?void 0:_.node,O=!(s.inexact||s.hasUnknownMembers||B&&(B.type==="RestElement"||(B.type==="TSPropertySignature"||B.type==="TSCallSignatureDeclaration"||B.type==="TSMethodSignature"||B.type==="TSConstructSignatureDeclaration")&&A(B,x.PrettierIgnore))),R;if(S.length===0){if(!A(s,x.Dangling))return[l,h,Y(e,r)];R=D([l,J(e,t,{indent:!0}),F,h,V(e),Y(e,r)])}else R=[y&&L(s.properties)?Is(p):"",l,E([t.bracketSpacing?d:F,...S]),b(O&&(f!==","||ye(t))?f:""),t.bracketSpacing?d:F,h,V(e),Y(e,r)];return e.match(T=>T.type==="ObjectPattern"&&!L(T.decorators),Ms)||Le(s)&&(e.match(void 0,(T,W)=>W==="typeAnnotation",(T,W)=>W==="typeAnnotation",Ms)||e.match(void 0,(T,W)=>T.type==="FunctionTypeParam"&&W==="typeAnnotation",Ms))||!c&&e.match(T=>T.type==="ObjectPattern",T=>T.type==="AssignmentExpression"||T.type==="VariableDeclarator")?R:D(R,{shouldBreak:c})}function Ms(e,t){return(t==="params"||t==="parameters"||t==="this"||t==="rest")&&xs(e)}function Rl(e){let t=[e];for(let r=0;r<t.length;r++){let n=t[r];for(let s of["test","consequent","alternate"]){let u=n[s];if(U(u))return!0;u.type==="ConditionalExpression"&&t.push(u)}}return!1}function Jl(e,t,r){let{node:n}=e,s=n.type==="ConditionalExpression",u=s?"alternate":"falseType",{parent:i}=e,a=s?r("test"):[r("checkType")," ","extends"," ",r("extendsType")];return i.type===n.type&&i[u]===n?ge(2,a):a}var ql=new Map([["AssignmentExpression","right"],["VariableDeclarator","init"],["ReturnStatement","argument"],["ThrowStatement","argument"],["UnaryExpression","argument"],["YieldExpression","argument"],["AwaitExpression","argument"]]);function Wl(e){let{node:t}=e;if(t.type!=="ConditionalExpression")return!1;let r,n=t;for(let s=0;!r;s++){let u=e.getParentNode(s);if(u.type==="ChainExpression"&&u.expression===n||w(u)&&u.callee===n||q(u)&&u.object===n||u.type==="TSNonNullExpression"&&u.expression===n){n=u;continue}u.type==="NewExpression"&&u.callee===n||Ae(u)&&u.expression===n?(r=e.getParentNode(s+1),n=u):r=u}return n===t?!1:r[ql.get(r.type)]===n}function Aa(e,t,r){let{node:n}=e,s=n.type==="ConditionalExpression",u=s?"consequent":"trueType",i=s?"alternate":"falseType",a=s?["test"]:["checkType","extendsType"],o=n[u],p=n[i],m=[],y=!1,{parent:c}=e,f=c.type===n.type&&a.some(X=>c[X]===n),l=c.type===n.type&&!f,h,g,S=0;do g=h||n,h=e.getParentNode(S),S++;while(h&&h.type===n.type&&a.every(X=>h[X]!==g));let B=h||c,O=g;if(s&&(U(n[a[0]])||U(o)||U(p)||Rl(O))){y=!0,l=!0;let X=z=>[b("("),E([F,z]),F,b(")")],ue=z=>z.type==="NullLiteral"||z.type==="Literal"&&z.value===null||z.type==="Identifier"&&z.name==="undefined";m.push(" ? ",ue(o)?r(u):X(r(u))," : ",p.type===n.type||ue(p)?r(i):X(r(i)))}else{let X=z=>t.useTabs?E(r(z)):ge(2,r(z)),ue=[d,"? ",o.type===n.type?b("","("):"",X(u),o.type===n.type?b("",")"):"",d,": ",X(i)];m.push(c.type!==n.type||c[i]===n||f?ue:t.useTabs?Rr(E(ue)):ge(Math.max(0,t.tabWidth-2),ue))}let R=[u,i,...a].some(X=>A(n[X],ue=>ee(ue)&&Te(t.originalText,j(ue),I(ue)))),_=X=>c===B?D(X,{shouldBreak:R}):R?[X,Ee]:X,T=!y&&(q(c)||c.type==="NGPipeExpression"&&c.left===n)&&!c.computed,W=Wl(e),Fe=_([Jl(e,t,r),l?m:E(m),s&&T&&!W?F:""]);return f||W?D([E([F,Fe]),F]):Fe}function Nl(e,t){return(q(t)||t.type==="NGPipeExpression"&&t.left===e)&&!t.computed}function Gl(e,t,r,n){return[...e.map(u=>ot(u)),ot(t),ot(r)].flat().some(u=>ee(u)&&Te(n.originalText,j(u),I(u)))}var Ul=new Map([["AssignmentExpression","right"],["VariableDeclarator","init"],["ReturnStatement","argument"],["ThrowStatement","argument"],["UnaryExpression","argument"],["YieldExpression","argument"],["AwaitExpression","argument"]]);function Yl(e){let{node:t}=e;if(t.type!=="ConditionalExpression")return!1;let r,n=t;for(let s=0;!r;s++){let u=e.getParentNode(s);if(u.type==="ChainExpression"&&u.expression===n||w(u)&&u.callee===n||q(u)&&u.object===n||u.type==="TSNonNullExpression"&&u.expression===n){n=u;continue}u.type==="NewExpression"&&u.callee===n||Ae(u)&&u.expression===n?(r=e.getParentNode(s+1),n=u):r=u}return n===t?!1:r[Ul.get(r.type)]===n}var js=e=>[b("("),E([F,e]),F,b(")")];function $t(e,t,r,n){if(!t.experimentalTernaries)return Aa(e,t,r);let{node:s}=e,u=s.type==="ConditionalExpression",i=s.type==="TSConditionalType"||s.type==="ConditionalTypeAnnotation",a=u?"consequent":"trueType",o=u?"alternate":"falseType",p=u?["test"]:["checkType","extendsType"],m=s[a],y=s[o],c=p.map(Re=>s[Re]),{parent:f}=e,l=f.type===s.type,h=l&&p.some(Re=>f[Re]===s),g=l&&f[o]===s,S=m.type===s.type,B=y.type===s.type,O=B||g,R=t.tabWidth>2||t.useTabs,_,T,W=0;do T=_||s,_=e.getParentNode(W),W++;while(_&&_.type===s.type&&p.every(Re=>_[Re]!==T));let Fe=_||f,X=n&&n.assignmentLayout&&n.assignmentLayout!=="break-after-operator"&&(f.type==="AssignmentExpression"||f.type==="VariableDeclarator"||f.type==="ClassProperty"||f.type==="PropertyDefinition"||f.type==="ClassPrivateProperty"||f.type==="ObjectProperty"||f.type==="Property"),ue=(f.type==="ReturnStatement"||f.type==="ThrowStatement")&&!(S||B),z=u&&Fe.type==="JSXExpressionContainer"&&e.grandparent.type!=="JSXAttribute",wt=Yl(e),Ht=Nl(s,f),k=i&&Be(e,t),N=R?t.useTabs?"	":" ".repeat(t.tabWidth-1):"",Q=Gl(c,m,y,t)||S||B,je=!O&&!l&&!i&&(z?m.type==="NullLiteral"||m.type==="Literal"&&m.value===null:nr(m,t)&&Nn(s.test,3)),xt=O||g||i&&!l||l&&u&&Nn(s.test,1)||je,Ws=[];!S&&A(m,x.Dangling)&&e.call(Re=>{Ws.push(J(Re,t),C)},"consequent");let Kt=[];A(s.test,x.Dangling)&&e.call(Re=>{Kt.push(J(Re,t))},"test"),!B&&A(y,x.Dangling)&&e.call(Re=>{Kt.push(J(Re,t))},"alternate"),A(s,x.Dangling)&&Kt.push(J(e,t));let Ns=Symbol("test"),wa=Symbol("consequent"),Ar=Symbol("test-and-consequent"),Oa=u?[js(r("test")),s.test.type==="ConditionalExpression"?Ee:""]:[r("checkType")," ","extends"," ",s.extendsType.type==="TSConditionalType"||s.extendsType.type==="ConditionalTypeAnnotation"||s.extendsType.type==="TSMappedType"?r("extendsType"):D(js(r("extendsType")))],Gs=D([Oa," ?"],{id:Ns}),va=r(a),dr=E([S||z&&(U(m)||l||O)?C:d,Ws,va]),_a=xt?D([Gs,O?dr:b(dr,D(dr,{id:wa}),{groupId:Ns})],{id:Ar}):[Gs,dr],wn=r(o),Us=je?b(wn,Rr(js(wn)),{groupId:Ar}):wn,zt=[_a,Kt.length>0?[E([C,Kt]),C]:B?C:je?b(d," ",{groupId:Ar}):d,":",B?" ":R?xt?b(N,b(O||je?" ":N," "),{groupId:Ar}):b(N," "):" ",B?Us:D([E(Us),z&&!je?F:""]),Ht&&!wt?F:"",Q?Ee:""];return X&&!Q?D(E([F,D(zt)])):X||ue?D(E(zt)):wt||i&&h?D([E([F,zt]),k?F:""]):f===Fe?D(zt):zt}function da(e,t,r,n){let{node:s}=e;if(rr(s))return la(e,t);let u=t.semi?";":"",i=[];switch(s.type){case"JsExpressionRoot":return r("node");case"JsonRoot":return[r("node"),C];case"File":return oa(e,t,r)??r("program");case"Program":return bs(e,t,r);case"EmptyStatement":return"";case"ExpressionStatement":return aa(e,t,r);case"ChainExpression":return r("expression");case"ParenthesizedExpression":return!A(s.expression)&&(se(s.expression)||G(s.expression))?["(",r("expression"),")"]:D(["(",E([F,r("expression")]),F,")"]);case"AssignmentExpression":return ji(e,t,r);case"VariableDeclarator":return Ri(e,t,r);case"BinaryExpression":case"LogicalExpression":return Hr(e,t,r);case"AssignmentPattern":return[r("left")," = ",r("right")];case"OptionalMemberExpression":case"MemberExpression":return Oi(e,t,r);case"MetaProperty":return[r("meta"),".",r("property")];case"BindExpression":return s.object&&i.push(r("object")),i.push(D(E([F,Kr(e,t,r)]))),i;case"Identifier":return[s.name,V(e),mn(e),Y(e,r)];case"V8IntrinsicIdentifier":return["%",s.name];case"SpreadElement":case"SpreadElementPattern":case"SpreadPropertyPattern":case"RestElement":return Dn(e,r);case"FunctionDeclaration":case"FunctionExpression":return En(e,r,t,n);case"ArrowFunctionExpression":return zi(e,t,r,n);case"YieldExpression":return i.push("yield"),s.delegate&&i.push("*"),s.argument&&i.push(" ",r("argument")),i;case"AwaitExpression":if(i.push("await"),s.argument){i.push(" ",r("argument"));let{parent:a}=e;if(w(a)&&a.callee===s||q(a)&&a.object===s){i=[E([F,...i]),F];let o=e.findAncestor(p=>p.type==="AwaitExpression"||p.type==="BlockStatement");if((o==null?void 0:o.type)!=="AwaitExpression"||!ie(o.argument,p=>p===s))return D(i)}}return i;case"ExportDefaultDeclaration":case"ExportNamedDeclaration":case"ExportAllDeclaration":return bn(e,t,r);case"ImportDeclaration":return ma(e,t,r);case"ImportSpecifier":case"ExportSpecifier":case"ImportNamespaceSpecifier":case"ExportNamespaceSpecifier":case"ImportDefaultSpecifier":case"ExportDefaultSpecifier":return Ca(e,t,r);case"ImportAttribute":return[r("key"),": ",r("value")];case"Import":return"import";case"BlockStatement":case"StaticBlock":return dn(e,t,r);case"ClassBody":return sa(e,t,r);case"ThrowStatement":return $i(e,t,r);case"ReturnStatement":return Vi(e,t,r);case"NewExpression":case"ImportExpression":case"OptionalCallExpression":case"CallExpression":return zr(e,t,r);case"ObjectExpression":case"ObjectPattern":case"RecordExpression":return Tt(e,t,r);case"Property":return ht(s)?yr(e,t,r):Ss(e,t,r);case"ObjectProperty":return Ss(e,t,r);case"ObjectMethod":return yr(e,t,r);case"Decorator":return["@",r("expression")];case"ArrayExpression":case"ArrayPattern":case"TupleExpression":return Vt(e,t,r);case"SequenceExpression":{let{parent:a}=e;if(a.type==="ExpressionStatement"||a.type==="ForStatement"){let o=[];return e.each(({isFirst:p})=>{p?o.push(r()):o.push(",",E([d,r()]))},"expressions"),D(o)}return D(P([",",d],e.map(r,"expressions")))}case"ThisExpression":return"this";case"Super":return"super";case"Directive":return[r("value"),u];case"UnaryExpression":return i.push(s.operator),/[a-z]$/.test(s.operator)&&i.push(" "),A(s.argument)?i.push(D(["(",E([F,r("argument")]),F,")"])):i.push(r("argument")),i;case"UpdateExpression":return i.push(r("argument"),s.operator),s.prefix&&i.reverse(),i;case"ConditionalExpression":return $t(e,t,r,n);case"VariableDeclaration":{let a=e.map(r,"declarations"),o=e.parent,p=o.type==="ForStatement"||o.type==="ForInStatement"||o.type==="ForOfStatement",m=s.declarations.some(c=>c.init),y;return a.length===1&&!A(s.declarations[0])?y=a[0]:a.length>0&&(y=E(a[0])),i=[ne(e),s.kind,y?[" ",y]:"",E(a.slice(1).map(c=>[",",m&&!p?C:d,c]))],p&&o.body!==s||i.push(u),D(i)}case"WithStatement":return D(["with (",r("object"),")",yt(s.body,r("body"))]);case"IfStatement":{let a=yt(s.consequent,r("consequent")),o=D(["if (",D([E([F,r("test")]),F]),")",a]);if(i.push(o),s.alternate){let p=A(s.consequent,x.Trailing|x.Line)||Or(s),m=s.consequent.type==="BlockStatement"&&!p;i.push(m?" ":C),A(s,x.Dangling)&&i.push(J(e,t),p?C:" "),i.push("else",D(yt(s.alternate,r("alternate"),s.alternate.type==="IfStatement")))}return i}case"ForStatement":{let a=yt(s.body,r("body")),o=J(e,t),p=o?[o,F]:"";return!s.init&&!s.test&&!s.update?[p,D(["for (;;)",a])]:[p,D(["for (",D([E([F,r("init"),";",d,r("test"),";",d,r("update")]),F]),")",a])]}case"WhileStatement":return D(["while (",D([E([F,r("test")]),F]),")",yt(s.body,r("body"))]);case"ForInStatement":return D(["for (",r("left")," in ",r("right"),")",yt(s.body,r("body"))]);case"ForOfStatement":return D(["for",s.await?" await":""," (",r("left")," of ",r("right"),")",yt(s.body,r("body"))]);case"DoWhileStatement":{let a=yt(s.body,r("body"));return i=[D(["do",a])],s.body.type==="BlockStatement"?i.push(" "):i.push(C),i.push("while (",D([E([F,r("test")]),F]),")",u),i}case"DoExpression":return[s.async?"async ":"","do ",r("body")];case"BreakStatement":case"ContinueStatement":return i.push(s.type==="BreakStatement"?"break":"continue"),s.label&&i.push(" ",r("label")),i.push(u),i;case"LabeledStatement":return s.body.type==="EmptyStatement"?[r("label"),":;"]:[r("label"),": ",r("body")];case"TryStatement":return["try ",r("block"),s.handler?[" ",r("handler")]:"",s.finalizer?[" finally ",r("finalizer")]:""];case"CatchClause":if(s.param){let a=A(s.param,p=>!ee(p)||p.leading&&Z(t.originalText,I(p))||p.trailing&&Z(t.originalText,j(p),{backwards:!0})),o=r("param");return["catch ",a?["(",E([F,o]),F,") "]:["(",o,") "],r("body")]}return["catch ",r("body")];case"SwitchStatement":return[D(["switch (",E([F,r("discriminant")]),F,")"])," {",s.cases.length>0?E([C,P(C,e.map(({node:a,isLast:o})=>[r(),!o&&fe(a,t)?C:""],"cases"))]):"",C,"}"];case"SwitchCase":{s.test?i.push("case ",r("test"),":"):i.push("default:"),A(s,x.Dangling)&&i.push(" ",J(e,t));let a=s.consequent.filter(o=>o.type!=="EmptyStatement");if(a.length>0){let o=fr(e,t,r,"consequent");i.push(a.length===1&&a[0].type==="BlockStatement"?[" ",o]:E([C,o]))}return i}case"DebuggerStatement":return["debugger",u];case"ClassDeclaration":case"ClassExpression":return gn(e,t,r);case"ClassMethod":case"ClassPrivateMethod":case"MethodDefinition":return hn(e,t,r);case"ClassProperty":case"PropertyDefinition":case"ClassPrivateProperty":case"ClassAccessorProperty":case"AccessorProperty":return Sn(e,t,r);case"TemplateElement":return Ie(s.value.raw);case"TemplateLiteral":return Gr(e,r,t);case"TaggedTemplateExpression":return zu(r);case"PrivateIdentifier":return["#",s.name];case"PrivateName":return["#",r("id")];case"TopicReference":return"%";case"ArgumentPlaceholder":return"?";case"ModuleExpression":{i.push("module {");let a=r("body");return a&&i.push(E([C,a]),C),i.push("}"),i}case"InterpreterDirective":default:throw new _e(s,"ESTree")}}function Pn(e,t,r){let{parent:n,node:s,key:u}=e,i=[r("expression")];switch(s.type){case"AsConstExpression":i.push(" as const");break;case"AsExpression":case"TSAsExpression":i.push(" as ",r("typeAnnotation"));break;case"SatisfiesExpression":case"TSSatisfiesExpression":i.push(" satisfies ",r("typeAnnotation"));break}return u==="callee"&&w(n)||u==="object"&&q(n)?D([E([F,...i]),F]):i}function Ta(e,t,r){return Tt(e,r,t)}function kn(e,t){let{node:r}=e,n=t("id");r.computed&&(n=["[",n,"]"]);let s="";return r.initializer&&(s=t("initializer")),r.init&&(s=t("init")),s?[n," = ",s]:n}function xa(e,t,r){let{node:n}=e,s;if(n.type==="EnumSymbolBody"||n.explicitType)switch(n.type){case"EnumBooleanBody":s="boolean";break;case"EnumNumberBody":s="number";break;case"EnumStringBody":s="string";break;case"EnumSymbolBody":s="symbol";break}return[s?`of ${s} `:"",Ta(e,t,r)]}function In(e,t,r){let{node:n}=e;return[ne(e),n.const?"const ":"","enum ",t("id")," ",n.type==="TSEnumDeclaration"?Ta(e,t,r):t("body")]}function Ln(e,t,r){let{node:n}=e,s=[ne(e),"interface"],u=[],i=[];n.type!=="InterfaceTypeAnnotation"&&u.push(" ",r("id"),r("typeParameters"));let a=n.typeParameters&&!A(n.typeParameters,x.Trailing|x.Line);return L(n.extends)&&i.push(a?b(" ",d,{groupId:Er(n.typeParameters)}):d,"extends ",(n.extends.length===1?du:E)(P([",",d],e.map(r,"extends")))),A(n.id,x.Trailing)||L(n.extends)?a?s.push(D([...u,E(i)])):s.push(D(E([...u,...i]))):s.push(...u,...i),s.push(" ",r("body")),D(s)}function ga(e,t,r){let{node:n}=e;if(br(n))return n.type.slice(0,-14).toLowerCase();let s=t.semi?";":"";switch(n.type){case"DeclareClass":return gn(e,t,r);case"DeclareFunction":return[ne(e),"function ",r("id"),r("predicate"),s];case"DeclareModule":return["declare module ",r("id")," ",r("body")];case"DeclareModuleExports":return["declare module.exports",Y(e,r),s];case"DeclareVariable":return[ne(e),n.kind??"var"," ",r("id"),s];case"DeclareExportDeclaration":case"DeclareExportAllDeclaration":return bn(e,t,r);case"DeclareOpaqueType":case"OpaqueType":return Ni(e,t,r);case"DeclareTypeAlias":case"TypeAlias":return Zr(e,t,r);case"IntersectionTypeAnnotation":return en(e,t,r);case"UnionTypeAnnotation":return tn(e,t,r);case"ConditionalTypeAnnotation":return $t(e,t,r);case"InferTypeAnnotation":return sn(e,t,r);case"FunctionTypeAnnotation":return rn(e,t,r);case"TupleTypeAnnotation":return Vt(e,t,r);case"TupleTypeLabeledElement":return an(e,t,r);case"TupleTypeSpreadElement":return un(e,t,r);case"GenericTypeAnnotation":return[r("id"),It(e,t,r,"typeParameters")];case"IndexedAccessType":case"OptionalIndexedAccessType":return nn(e,t,r);case"TypeAnnotation":return on(e,t,r);case"TypeParameter":return xn(e,t,r);case"TypeofTypeAnnotation":return cn(e,r);case"ExistsTypeAnnotation":return"*";case"ArrayTypeAnnotation":return pn(r);case"DeclareEnum":case"EnumDeclaration":return In(e,r,t);case"EnumBooleanBody":case"EnumNumberBody":case"EnumStringBody":case"EnumSymbolBody":return xa(e,r,t);case"EnumBooleanMember":case"EnumNumberMember":case"EnumStringMember":case"EnumDefaultedMember":return kn(e,r);case"FunctionTypeParam":{let u=n.name?r("name"):e.parent.this===n?"this":"";return[u,V(e),u?": ":"",r("typeAnnotation")]}case"DeclareInterface":case"InterfaceDeclaration":case"InterfaceTypeAnnotation":return Ln(e,t,r);case"ClassImplements":case"InterfaceExtends":return[r("id"),r("typeParameters")];case"NullableTypeAnnotation":return["?",r("typeAnnotation")];case"Variance":{let{kind:u}=n;return yn.ok(u==="plus"||u==="minus"),u==="plus"?"+":"-"}case"KeyofTypeAnnotation":return["keyof ",r("argument")];case"ObjectTypeCallProperty":return[n.static?"static ":"",r("value")];case"ObjectTypeMappedTypeProperty":return Zi(e,t,r);case"ObjectTypeIndexer":return[n.static?"static ":"",n.variance?r("variance"):"","[",r("id"),n.id?": ":"",r("key"),"]: ",r("value")];case"ObjectTypeProperty":{let u="";return n.proto?u="proto ":n.static&&(u="static "),[u,n.kind!=="init"?n.kind+" ":"",n.variance?r("variance"):"",ft(e,t,r),V(e),ht(n)?"":": ",r("value")]}case"ObjectTypeAnnotation":return Tt(e,t,r);case"ObjectTypeInternalSlot":return[n.static?"static ":"","[[",r("id"),"]]",V(e),n.method?"":": ",r("value")];case"ObjectTypeSpreadProperty":return Dn(e,r);case"QualifiedTypeofIdentifier":case"QualifiedTypeIdentifier":return[r("qualification"),".",r("id")];case"NullLiteralTypeAnnotation":return"null";case"BooleanLiteralTypeAnnotation":return String(n.value);case"StringLiteralTypeAnnotation":return Ie(at(De(n),t));case"NumberLiteralTypeAnnotation":return Qe(n.raw??n.extra.raw);case"BigIntLiteralTypeAnnotation":return Bn(n.raw??n.extra.raw);case"TypeCastExpression":return["(",r("expression"),Y(e,r),")"];case"TypePredicate":return ln(e,r);case"TypeParameterDeclaration":case"TypeParameterInstantiation":return It(e,t,r,"params");case"InferredPredicate":case"DeclaredPredicate":return[e.key==="predicate"&&e.parent.type!=="DeclareFunction"&&!e.parent.returnType?": ":" ","%checks",...n.type==="DeclaredPredicate"?["(",r("value"),")"]:[]];case"AsExpression":case"AsConstExpression":case"SatisfiesExpression":return Pn(e,t,r)}}function ha(e,t,r){var i;let{node:n}=e;if(!n.type.startsWith("TS"))return;if(Pr(n))return n.type.slice(2,-7).toLowerCase();let s=t.semi?";":"",u=[];switch(n.type){case"TSThisType":return"this";case"TSTypeAssertion":{let a=!(G(n.expression)||se(n.expression)),o=D(["<",E([F,r("typeAnnotation")]),F,">"]),p=[b("("),E([F,r("expression")]),F,b(")")];return a?He([[o,r("expression")],[o,D(p,{shouldBreak:!0})],[o,r("expression")]]):D([o,r("expression")])}case"TSDeclareFunction":return En(e,r,t);case"TSExportAssignment":return["export = ",r("expression"),s];case"TSModuleBlock":return dn(e,t,r);case"TSInterfaceBody":case"TSTypeLiteral":return Tt(e,t,r);case"TSTypeAliasDeclaration":return Zr(e,t,r);case"TSQualifiedName":return[r("left"),".",r("right")];case"TSAbstractMethodDefinition":case"TSDeclareMethod":return hn(e,t,r);case"TSAbstractAccessorProperty":case"TSAbstractPropertyDefinition":return Sn(e,t,r);case"TSInterfaceHeritage":case"TSClassImplements":case"TSExpressionWithTypeArguments":case"TSInstantiationExpression":return[r("expression"),r("typeParameters")];case"TSTemplateLiteralType":return Gr(e,r,t);case"TSNamedTupleMember":return an(e,t,r);case"TSRestType":return un(e,t,r);case"TSOptionalType":return[r("typeAnnotation"),"?"];case"TSInterfaceDeclaration":return Ln(e,t,r);case"TSTypeParameterDeclaration":case"TSTypeParameterInstantiation":return It(e,t,r,"params");case"TSTypeParameter":return xn(e,t,r);case"TSAsExpression":case"TSSatisfiesExpression":return Pn(e,t,r);case"TSArrayType":return pn(r);case"TSPropertySignature":return[n.readonly?"readonly ":"",ft(e,t,r),V(e),Y(e,r)];case"TSParameterProperty":return[Xt(n),n.static?"static ":"",n.override?"override ":"",n.readonly?"readonly ":"",r("parameter")];case"TSTypeQuery":return cn(e,r);case"TSIndexSignature":{let a=n.parameters.length>1?b(ye(t)?",":""):"",o=D([E([F,P([", ",F],e.map(r,"parameters"))]),a,F]),p=e.parent.type==="ClassBody"&&e.key==="body";return[p&&n.static?"static ":"",n.readonly?"readonly ":"","[",n.parameters?o:"","]",Y(e,r),p?s:""]}case"TSTypePredicate":return ln(e,r);case"TSNonNullExpression":return[r("expression"),"!"];case"TSImportType":return[n.isTypeOf?"typeof ":"","import(",r("argument"),")",n.qualifier?[".",r("qualifier")]:"",It(e,t,r,n.typeArguments?"typeArguments":"typeParameters")];case"TSLiteralType":return r("literal");case"TSIndexedAccessType":return nn(e,t,r);case"TSTypeOperator":return[n.operator," ",r("typeAnnotation")];case"TSMappedType":return ea(e,t,r);case"TSMethodSignature":{let a=n.kind&&n.kind!=="method"?`${n.kind} `:"";u.push(Xt(n),a,n.computed?"[":"",r("key"),n.computed?"]":"",V(e));let o=Dt(e,r,t,!1,!0),p=n.returnType?"returnType":"typeAnnotation",m=n[p],y=m?Y(e,r,p):"",c=kt(n,y);return u.push(c?D(o):o),m&&u.push(D(y)),D(u)}case"TSNamespaceExportDeclaration":return["export as namespace ",r("id"),t.semi?";":""];case"TSEnumDeclaration":return In(e,r,t);case"TSEnumMember":return kn(e,r);case"TSImportEqualsDeclaration":return[n.isExport?"export ":"","import ",_s(n,!1),r("id")," = ",r("moduleReference"),t.semi?";":""];case"TSExternalModuleReference":return["require(",r("expression"),")"];case"TSModuleDeclaration":{let{parent:a}=e,o=a.type==="TSModuleDeclaration",p=((i=n.body)==null?void 0:i.type)==="TSModuleDeclaration";if(o)u.push(".");else if(u.push(ne(e)),!(n.kind==="global"||n.global)){let y=n.kind??(te(n.id)||Fr(t,j(n),j(n.id)).trim().endsWith("module")?"module":"namespace");u.push(y," ")}return u.push(r("id")),p?u.push(r("body")):n.body?u.push(" ",D(r("body"))):u.push(s),u}case"TSConditionalType":return $t(e,t,r);case"TSInferType":return sn(e,t,r);case"TSIntersectionType":return en(e,t,r);case"TSUnionType":return tn(e,t,r);case"TSFunctionType":case"TSCallSignatureDeclaration":case"TSConstructorType":case"TSConstructSignatureDeclaration":return rn(e,t,r);case"TSTupleType":return Vt(e,t,r);case"TSTypeReference":return[r("typeName"),It(e,t,r,"typeParameters")];case"TSTypeAnnotation":return on(e,t,r);case"TSEmptyBodyFunctionExpression":return Fn(e,t,r);case"TSJSDocAllType":return"*";case"TSJSDocUnknownType":return"?";case"TSJSDocNullableType":return hs(e,r,"?");case"TSJSDocNonNullableType":return hs(e,r,"!");case"TSParenthesizedType":default:throw new _e(n,"TypeScript")}}function Xl(e,t,r,n){if($r(e))return Fi(e,t);for(let s of[bi,gi,ga,ha,da]){let u=s(e,t,r,n);if(u!==void 0)return u}}var Vl=M(["ClassMethod","ClassPrivateMethod","ClassProperty","ClassAccessorProperty","AccessorProperty","TSAbstractAccessorProperty","PropertyDefinition","TSAbstractPropertyDefinition","ClassPrivateProperty","MethodDefinition","TSAbstractMethodDefinition","TSDeclareMethod"]);function $l(e,t,r,n){var y;e.isRoot&&((y=t.__onHtmlBindingRoot)==null||y.call(t,e.node,t));let s=Xl(e,t,r,n);if(!s)return"";let{node:u}=e;if(Vl(u))return s;let i=L(u.decorators),a=ki(e,t,r),o=u.type==="ClassExpression";if(i&&!o)return or(s,c=>D([a,c]));let p=Be(e,t),m=ua(e,t);return!a&&!p&&!m?s:or(s,c=>[m?";":"",p?"(":"",p&&o&&i?[E([d,a,c]),d]:[a,c],p?")":""])}var Sa=$l;var Hl={avoidAstMutation:!0};var Ba=[{linguistLanguageId:174,name:"JSON.stringify",type:"data",color:"#292929",tmScope:"source.json",aceMode:"json",codemirrorMode:"javascript",codemirrorMimeType:"application/json",aliases:["geojson","jsonl","topojson"],extensions:[".importmap"],filenames:["package.json","package-lock.json","composer.json"],parsers:["json-stringify"],vscodeLanguageIds:["json"]},{linguistLanguageId:174,name:"JSON",type:"data",color:"#292929",tmScope:"source.json",aceMode:"json",codemirrorMode:"javascript",codemirrorMimeType:"application/json",aliases:["geojson","jsonl","topojson"],extensions:[".json",".4DForm",".4DProject",".avsc",".geojson",".gltf",".har",".ice",".JSON-tmLanguage",".mcmeta",".tfstate",".tfstate.backup",".topojson",".webapp",".webmanifest",".yy",".yyp"],filenames:[".all-contributorsrc",".arcconfig",".auto-changelog",".c8rc",".htmlhintrc",".imgbotconfig",".nycrc",".tern-config",".tern-project",".watchmanconfig","Pipfile.lock","composer.lock","flake.lock","mcmod.info",".babelrc",".jscsrc",".jshintrc",".jslintrc",".swcrc"],parsers:["json"],vscodeLanguageIds:["json"]},{linguistLanguageId:423,name:"JSON with Comments",type:"data",color:"#292929",group:"JSON",tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"text/javascript",aliases:["jsonc"],extensions:[".jsonc",".code-snippets",".code-workspace",".sublime-build",".sublime-commands",".sublime-completions",".sublime-keymap",".sublime-macro",".sublime-menu",".sublime-mousemap",".sublime-project",".sublime-settings",".sublime-theme",".sublime-workspace",".sublime_metrics",".sublime_session"],filenames:[],parsers:["jsonc"],vscodeLanguageIds:["jsonc"]},{linguistLanguageId:175,name:"JSON5",type:"data",color:"#267CB9",extensions:[".json5"],tmScope:"source.js",aceMode:"javascript",codemirrorMode:"javascript",codemirrorMimeType:"application/json",parsers:["json5"],vscodeLanguageIds:["json5"]}];var Js={};Tr(Js,{getVisitorKeys:()=>Pa,massageAstNode:()=>Ia,print:()=>Ql});var Kl={JsonRoot:["node"],ArrayExpression:["elements"],ObjectExpression:["properties"],ObjectProperty:["key","value"],UnaryExpression:["argument"],NullLiteral:[],BooleanLiteral:[],StringLiteral:[],NumericLiteral:[],Identifier:[],TemplateLiteral:["quasis"],TemplateElement:[]},ba=Kl;var zl=Sr(ba),Pa=zl;function Ql(e,t,r){let{node:n}=e;switch(n.type){case"JsonRoot":return[r("node"),C];case"ArrayExpression":{if(n.elements.length===0)return"[]";let s=e.map(()=>e.node===null?"null":r(),"elements");return["[",E([C,P([",",C],s)]),C,"]"]}case"ObjectExpression":return n.properties.length===0?"{}":["{",E([C,P([",",C],e.map(r,"properties"))]),C,"}"];case"ObjectProperty":return[r("key"),": ",r("value")];case"UnaryExpression":return[n.operator==="+"?"":n.operator,r("argument")];case"NullLiteral":return"null";case"BooleanLiteral":return n.value?"true":"false";case"StringLiteral":return JSON.stringify(n.value);case"NumericLiteral":return ka(e)?JSON.stringify(String(n.value)):JSON.stringify(n.value);case"Identifier":return ka(e)?JSON.stringify(n.name):n.name;case"TemplateLiteral":return r(["quasis",0]);case"TemplateElement":return JSON.stringify(n.value.cooked);default:throw new _e(n,"JSON")}}function ka(e){return e.key==="key"&&e.parent.type==="ObjectProperty"}var Zl=new Set(["start","end","extra","loc","comments","leadingComments","trailingComments","innerComments","errors","range","tokens"]);function Ia(e,t){let{type:r}=e;if(r==="ObjectProperty"){let{key:n}=e;n.type==="Identifier"?t.key={type:"StringLiteral",value:n.name}:n.type==="NumericLiteral"&&(t.key={type:"StringLiteral",value:String(n.value)});return}if(r==="UnaryExpression"&&e.operator==="+")return t.argument;if(r==="ArrayExpression"){for(let[n,s]of e.elements.entries())s===null&&t.elements.splice(n,0,{type:"NullLiteral"});return}if(r==="TemplateLiteral")return{type:"StringLiteral",value:e.quasis[0].value.cooked}}Ia.ignoredProperties=Zl;var Cr={bracketSpacing:{category:"Common",type:"boolean",default:!0,description:"Print spaces between brackets.",oppositeDescription:"Do not print spaces between brackets."},singleQuote:{category:"Common",type:"boolean",default:!1,description:"Use single quotes instead of double quotes."},proseWrap:{category:"Common",type:"choice",default:"preserve",description:"How to wrap prose.",choices:[{value:"always",description:"Wrap prose if it exceeds the print width."},{value:"never",description:"Do not wrap prose."},{value:"preserve",description:"Wrap prose as-is."}]},bracketSameLine:{category:"Common",type:"boolean",default:!1,description:"Put > of opening tags on the last line instead of on a new line."},singleAttributePerLine:{category:"Common",type:"boolean",default:!1,description:"Enforce single attribute per line in HTML, Vue and JSX."}};var Lt="JavaScript",em={arrowParens:{category:Lt,type:"choice",default:"always",description:"Include parentheses around a sole arrow function parameter.",choices:[{value:"always",description:"Always include parens. Example: `(x) => x`"},{value:"avoid",description:"Omit parens when possible. Example: `x => x`"}]},bracketSameLine:Cr.bracketSameLine,bracketSpacing:Cr.bracketSpacing,jsxBracketSameLine:{category:Lt,type:"boolean",description:"Put > on the last line instead of at a new line.",deprecated:"2.4.0"},semi:{category:Lt,type:"boolean",default:!0,description:"Print semicolons.",oppositeDescription:"Do not print semicolons, except at the beginning of lines which may need them."},experimentalTernaries:{category:Lt,type:"boolean",default:!1,description:"Use curious ternaries, with the question mark after the condition.",oppositeDescription:"Default behavior of ternaries; keep question marks on the same line as the consequent."},singleQuote:Cr.singleQuote,jsxSingleQuote:{category:Lt,type:"boolean",default:!1,description:"Use single quotes in JSX."},quoteProps:{category:Lt,type:"choice",default:"as-needed",description:"Change when properties in objects are quoted.",choices:[{value:"as-needed",description:"Only add quotes around object properties where required."},{value:"consistent",description:"If at least one property in an object requires quotes, quote all properties."},{value:"preserve",description:"Respect the input use of quotes in object properties."}]},trailingComma:{category:Lt,type:"choice",default:"all",description:"Print trailing commas wherever possible when multi-line.",choices:[{value:"all",description:"Trailing commas wherever possible (including function arguments)."},{value:"es5",description:"Trailing commas where valid in ES5 (objects, arrays, etc.)"},{value:"none",description:"No trailing commas."}]},singleAttributePerLine:Cr.singleAttributePerLine},La=em;var tm={estree:Rs,"estree-json":Js},rm=[...$s,...Ba];var LT=qs;export{LT as default,rm as languages,La as options,tm as printers};
