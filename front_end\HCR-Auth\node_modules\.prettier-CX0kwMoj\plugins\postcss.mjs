var ul=Object.create;var $r=Object.defineProperty;var ll=Object.getOwnPropertyDescriptor;var cl=Object.getOwnPropertyNames;var fl=Object.getPrototypeOf,pl=Object.prototype.hasOwnProperty;var y=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),en=(t,e)=>{for(var s in e)$r(t,s,{get:e[s],enumerable:!0})},hl=(t,e,s,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of cl(e))!pl.call(t,n)&&n!==s&&$r(t,n,{get:()=>e[n],enumerable:!(r=ll(e,n))||r.enumerable});return t};var ue=(t,e,s)=>(s=t!=null?ul(fl(t)):{},hl(e||!t||!t.__esModule?$r(s,"default",{value:t,enumerable:!0}):s,t));var On=y(pe=>{"use strict";Object.defineProperty(pe,"__esModule",{value:!0});pe.extract=$l;pe.parse=zl;pe.parseWithComments=An;pe.print=Yl;pe.strip=Wl;var Dl=/\*\/$/,Ml=/^\/\*\*?/,Tn=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,Bl=/(^|\s+)\/\/([^\r\n]*)/g,kn=/^(\r?\n)+/,Ul=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,En=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,Fl=/(\r?\n|^) *\* ?/g,Cn=[];function $l(t){let e=t.match(Tn);return e?e[0].trimLeft():""}function Wl(t){let e=t.match(Tn);return e&&e[0]?t.substring(e[0].length):t}function zl(t){return An(t).pragmas}function An(t){let e=`
`;t=t.replace(Ml,"").replace(Dl,"").replace(Fl,"$1");let s="";for(;s!==t;)s=t,t=t.replace(Ul,`${e}$1 $2${e}`);t=t.replace(kn,"").trimRight();let r=Object.create(null),n=t.replace(En,"").replace(kn,"").trimRight(),i;for(;i=En.exec(t);){let o=i[2].replace(Bl,"");typeof r[i[1]]=="string"||Array.isArray(r[i[1]])?r[i[1]]=Cn.concat(r[i[1]],o):r[i[1]]=o}return{comments:n,pragmas:r}}function Yl({comments:t="",pragmas:e={}}){let s=`
`,r="/**",n=" *",i=" */",o=Object.keys(e),a=o.flatMap(c=>Sn(c,e[c])).map(c=>`${n} ${c}${s}`).join("");if(!t){if(o.length===0)return"";if(o.length===1&&!Array.isArray(e[o[0]])){let c=e[o[0]];return`${r} ${Sn(o[0],c)[0]}${i}`}}let u=t.split(s).map(c=>`${n} ${c}`).join(s)+s;return r+s+(t?u:"")+(t&&o.length?n+s:"")+a+i}function Sn(t,e){return Cn.concat(e).map(s=>`@${t} ${s}`.trim())}});var $t=y((iv,ss)=>{"use strict";ss.exports.isClean=Symbol("isClean");ss.exports.my=Symbol("my")});var gi=y((ov,ns)=>{var S=String,yi=function(){return{isColorSupported:!1,reset:S,bold:S,dim:S,italic:S,underline:S,inverse:S,hidden:S,strikethrough:S,black:S,red:S,green:S,yellow:S,blue:S,magenta:S,cyan:S,white:S,gray:S,bgBlack:S,bgRed:S,bgGreen:S,bgYellow:S,bgBlue:S,bgMagenta:S,bgCyan:S,bgWhite:S}};ns.exports=yi();ns.exports.createColors=yi});var is=y(()=>{});var Wt=y((lv,xi)=>{"use strict";var wi=gi(),vi=is(),nt=class t extends Error{constructor(e,s,r,n,i,o){super(e),this.name="CssSyntaxError",this.reason=e,i&&(this.file=i),n&&(this.source=n),o&&(this.plugin=o),typeof s<"u"&&typeof r<"u"&&(typeof s=="number"?(this.line=s,this.column=r):(this.line=s.line,this.column=s.column,this.endLine=r.line,this.endColumn=r.column)),this.setMessage(),Error.captureStackTrace&&Error.captureStackTrace(this,t)}setMessage(){this.message=this.plugin?this.plugin+": ":"",this.message+=this.file?this.file:"<css input>",typeof this.line<"u"&&(this.message+=":"+this.line+":"+this.column),this.message+=": "+this.reason}showSourceCode(e){if(!this.source)return"";let s=this.source;e==null&&(e=wi.isColorSupported),vi&&e&&(s=vi(s));let r=s.split(/\r?\n/),n=Math.max(this.line-3,0),i=Math.min(this.line+2,r.length),o=String(i).length,a,u;if(e){let{bold:c,gray:f,red:p}=wi.createColors(!0);a=l=>c(p(l)),u=l=>f(l)}else a=u=c=>c;return r.slice(n,i).map((c,f)=>{let p=n+1+f,l=" "+(" "+p).slice(-o)+" | ";if(p===this.line){let g=u(l.replace(/\d/g," "))+c.slice(0,this.column-1).replace(/[^\t]/g," ");return a(">")+u(l)+c+`
 `+g+a("^")}return" "+u(l)+c}).join(`
`)}toString(){let e=this.showSourceCode();return e&&(e=`

`+e+`
`),this.name+": "+this.message+e}};xi.exports=nt;nt.default=nt});var zt=y((cv,bi)=>{"use strict";var _i={after:`
`,beforeClose:`
`,beforeComment:`
`,beforeDecl:`
`,beforeOpen:" ",beforeRule:`
`,colon:": ",commentLeft:" ",commentRight:" ",emptyBody:"",indent:"    ",semicolon:!1};function dc(t){return t[0].toUpperCase()+t.slice(1)}var it=class{constructor(e){this.builder=e}atrule(e,s){let r="@"+e.name,n=e.params?this.rawValue(e,"params"):"";if(typeof e.raws.afterName<"u"?r+=e.raws.afterName:n&&(r+=" "),e.nodes)this.block(e,r+n);else{let i=(e.raws.between||"")+(s?";":"");this.builder(r+n+i,e)}}beforeAfter(e,s){let r;e.type==="decl"?r=this.raw(e,null,"beforeDecl"):e.type==="comment"?r=this.raw(e,null,"beforeComment"):s==="before"?r=this.raw(e,null,"beforeRule"):r=this.raw(e,null,"beforeClose");let n=e.parent,i=0;for(;n&&n.type!=="root";)i+=1,n=n.parent;if(r.includes(`
`)){let o=this.raw(e,null,"indent");if(o.length)for(let a=0;a<i;a++)r+=o}return r}block(e,s){let r=this.raw(e,"between","beforeOpen");this.builder(s+r+"{",e,"start");let n;e.nodes&&e.nodes.length?(this.body(e),n=this.raw(e,"after")):n=this.raw(e,"after","emptyBody"),n&&this.builder(n),this.builder("}",e,"end")}body(e){let s=e.nodes.length-1;for(;s>0&&e.nodes[s].type==="comment";)s-=1;let r=this.raw(e,"semicolon");for(let n=0;n<e.nodes.length;n++){let i=e.nodes[n],o=this.raw(i,"before");o&&this.builder(o),this.stringify(i,s!==n||r)}}comment(e){let s=this.raw(e,"left","commentLeft"),r=this.raw(e,"right","commentRight");this.builder("/*"+s+e.text+r+"*/",e)}decl(e,s){let r=this.raw(e,"between","colon"),n=e.prop+r+this.rawValue(e,"value");e.important&&(n+=e.raws.important||" !important"),s&&(n+=";"),this.builder(n,e)}document(e){this.body(e)}raw(e,s,r){let n;if(r||(r=s),s&&(n=e.raws[s],typeof n<"u"))return n;let i=e.parent;if(r==="before"&&(!i||i.type==="root"&&i.first===e||i&&i.type==="document"))return"";if(!i)return _i[r];let o=e.root();if(o.rawCache||(o.rawCache={}),typeof o.rawCache[r]<"u")return o.rawCache[r];if(r==="before"||r==="after")return this.beforeAfter(e,r);{let a="raw"+dc(r);this[a]?n=this[a](o,e):o.walk(u=>{if(n=u.raws[s],typeof n<"u")return!1})}return typeof n>"u"&&(n=_i[r]),o.rawCache[r]=n,n}rawBeforeClose(e){let s;return e.walk(r=>{if(r.nodes&&r.nodes.length>0&&typeof r.raws.after<"u")return s=r.raws.after,s.includes(`
`)&&(s=s.replace(/[^\n]+$/,"")),!1}),s&&(s=s.replace(/\S/g,"")),s}rawBeforeComment(e,s){let r;return e.walkComments(n=>{if(typeof n.raws.before<"u")return r=n.raws.before,r.includes(`
`)&&(r=r.replace(/[^\n]+$/,"")),!1}),typeof r>"u"?r=this.raw(s,null,"beforeDecl"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeDecl(e,s){let r;return e.walkDecls(n=>{if(typeof n.raws.before<"u")return r=n.raws.before,r.includes(`
`)&&(r=r.replace(/[^\n]+$/,"")),!1}),typeof r>"u"?r=this.raw(s,null,"beforeRule"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeOpen(e){let s;return e.walk(r=>{if(r.type!=="decl"&&(s=r.raws.between,typeof s<"u"))return!1}),s}rawBeforeRule(e){let s;return e.walk(r=>{if(r.nodes&&(r.parent!==e||e.first!==r)&&typeof r.raws.before<"u")return s=r.raws.before,s.includes(`
`)&&(s=s.replace(/[^\n]+$/,"")),!1}),s&&(s=s.replace(/\S/g,"")),s}rawColon(e){let s;return e.walkDecls(r=>{if(typeof r.raws.between<"u")return s=r.raws.between.replace(/[^\s:]/g,""),!1}),s}rawEmptyBody(e){let s;return e.walk(r=>{if(r.nodes&&r.nodes.length===0&&(s=r.raws.after,typeof s<"u"))return!1}),s}rawIndent(e){if(e.raws.indent)return e.raws.indent;let s;return e.walk(r=>{let n=r.parent;if(n&&n!==e&&n.parent&&n.parent===e&&typeof r.raws.before<"u"){let i=r.raws.before.split(`
`);return s=i[i.length-1],s=s.replace(/\S/g,""),!1}}),s}rawSemicolon(e){let s;return e.walk(r=>{if(r.nodes&&r.nodes.length&&r.last.type==="decl"&&(s=r.raws.semicolon,typeof s<"u"))return!1}),s}rawValue(e,s){let r=e[s],n=e.raws[s];return n&&n.value===r?n.raw:r}root(e){this.body(e),e.raws.after&&this.builder(e.raws.after)}rule(e){this.block(e,this.rawValue(e,"selector")),e.raws.ownSemicolon&&this.builder(e.raws.ownSemicolon,e,"end")}stringify(e,s){if(!this[e.type])throw new Error("Unknown AST node type "+e.type+". Maybe you need to change PostCSS stringifier.");this[e.type](e,s)}};bi.exports=it;it.default=it});var ot=y((fv,ki)=>{"use strict";var mc=zt();function os(t,e){new mc(e).stringify(t)}ki.exports=os;os.default=os});var ut=y((pv,Ei)=>{"use strict";var{isClean:Yt,my:yc}=$t(),gc=Wt(),wc=zt(),vc=ot();function as(t,e){let s=new t.constructor;for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r)||r==="proxyCache")continue;let n=t[r],i=typeof n;r==="parent"&&i==="object"?e&&(s[r]=e):r==="source"?s[r]=n:Array.isArray(n)?s[r]=n.map(o=>as(o,s)):(i==="object"&&n!==null&&(n=as(n)),s[r]=n)}return s}var at=class{constructor(e={}){this.raws={},this[Yt]=!1,this[yc]=!0;for(let s in e)if(s==="nodes"){this.nodes=[];for(let r of e[s])typeof r.clone=="function"?this.append(r.clone()):this.append(r)}else this[s]=e[s]}addToError(e){if(e.postcssNode=this,e.stack&&this.source&&/\n\s{4}at /.test(e.stack)){let s=this.source;e.stack=e.stack.replace(/\n\s{4}at /,`$&${s.input.from}:${s.start.line}:${s.start.column}$&`)}return e}after(e){return this.parent.insertAfter(this,e),this}assign(e={}){for(let s in e)this[s]=e[s];return this}before(e){return this.parent.insertBefore(this,e),this}cleanRaws(e){delete this.raws.before,delete this.raws.after,e||delete this.raws.between}clone(e={}){let s=as(this);for(let r in e)s[r]=e[r];return s}cloneAfter(e={}){let s=this.clone(e);return this.parent.insertAfter(this,s),s}cloneBefore(e={}){let s=this.clone(e);return this.parent.insertBefore(this,s),s}error(e,s={}){if(this.source){let{end:r,start:n}=this.rangeBy(s);return this.source.input.error(e,{column:n.column,line:n.line},{column:r.column,line:r.line},s)}return new gc(e)}getProxyProcessor(){return{get(e,s){return s==="proxyOf"?e:s==="root"?()=>e.root().toProxy():e[s]},set(e,s,r){return e[s]===r||(e[s]=r,(s==="prop"||s==="value"||s==="name"||s==="params"||s==="important"||s==="text")&&e.markDirty()),!0}}}markDirty(){if(this[Yt]){this[Yt]=!1;let e=this;for(;e=e.parent;)e[Yt]=!1}}next(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e+1]}positionBy(e,s){let r=this.source.start;if(e.index)r=this.positionInside(e.index,s);else if(e.word){s=this.toString();let n=s.indexOf(e.word);n!==-1&&(r=this.positionInside(n,s))}return r}positionInside(e,s){let r=s||this.toString(),n=this.source.start.column,i=this.source.start.line;for(let o=0;o<e;o++)r[o]===`
`?(n=1,i+=1):n+=1;return{column:n,line:i}}prev(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e-1]}rangeBy(e){let s={column:this.source.start.column,line:this.source.start.line},r=this.source.end?{column:this.source.end.column+1,line:this.source.end.line}:{column:s.column+1,line:s.line};if(e.word){let n=this.toString(),i=n.indexOf(e.word);i!==-1&&(s=this.positionInside(i,n),r=this.positionInside(i+e.word.length,n))}else e.start?s={column:e.start.column,line:e.start.line}:e.index&&(s=this.positionInside(e.index)),e.end?r={column:e.end.column,line:e.end.line}:e.endIndex?r=this.positionInside(e.endIndex):e.index&&(r=this.positionInside(e.index+1));return(r.line<s.line||r.line===s.line&&r.column<=s.column)&&(r={column:s.column+1,line:s.line}),{end:r,start:s}}raw(e,s){return new wc().raw(this,e,s)}remove(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this}replaceWith(...e){if(this.parent){let s=this,r=!1;for(let n of e)n===this?r=!0:r?(this.parent.insertAfter(s,n),s=n):this.parent.insertBefore(s,n);r||this.remove()}return this}root(){let e=this;for(;e.parent&&e.parent.type!=="document";)e=e.parent;return e}toJSON(e,s){let r={},n=s==null;s=s||new Map;let i=0;for(let o in this){if(!Object.prototype.hasOwnProperty.call(this,o)||o==="parent"||o==="proxyCache")continue;let a=this[o];if(Array.isArray(a))r[o]=a.map(u=>typeof u=="object"&&u.toJSON?u.toJSON(null,s):u);else if(typeof a=="object"&&a.toJSON)r[o]=a.toJSON(null,s);else if(o==="source"){let u=s.get(a.input);u==null&&(u=i,s.set(a.input,i),i++),r[o]={end:a.end,inputId:u,start:a.start}}else r[o]=a}return n&&(r.inputs=[...s.keys()].map(o=>o.toJSON())),r}toProxy(){return this.proxyCache||(this.proxyCache=new Proxy(this,this.getProxyProcessor())),this.proxyCache}toString(e=vc){e.stringify&&(e=e.stringify);let s="";return e(this,r=>{s+=r}),s}warn(e,s,r){let n={node:this};for(let i in r)n[i]=r[i];return e.warn(s,n)}get proxyOf(){return this}};Ei.exports=at;at.default=at});var ct=y((hv,Si)=>{"use strict";var xc=ut(),lt=class extends xc{constructor(e){e&&typeof e.value<"u"&&typeof e.value!="string"&&(e={...e,value:String(e.value)}),super(e),this.type="decl"}get variable(){return this.prop.startsWith("--")||this.prop[0]==="$"}};Si.exports=lt;lt.default=lt});var Oe=y((dv,Ti)=>{"use strict";var _c=ut(),ft=class extends _c{constructor(e){super(e),this.type="comment"}};Ti.exports=ft;ft.default=ft});var ne=y((mv,Li)=>{"use strict";var{isClean:Ci,my:Ai}=$t(),Oi=ct(),Ni=Oe(),bc=ut(),Pi,us,ls,Ri;function Ii(t){return t.map(e=>(e.nodes&&(e.nodes=Ii(e.nodes)),delete e.source,e))}function qi(t){if(t[Ci]=!1,t.proxyOf.nodes)for(let e of t.proxyOf.nodes)qi(e)}var z=class t extends bc{append(...e){for(let s of e){let r=this.normalize(s,this.last);for(let n of r)this.proxyOf.nodes.push(n)}return this.markDirty(),this}cleanRaws(e){if(super.cleanRaws(e),this.nodes)for(let s of this.nodes)s.cleanRaws(e)}each(e){if(!this.proxyOf.nodes)return;let s=this.getIterator(),r,n;for(;this.indexes[s]<this.proxyOf.nodes.length&&(r=this.indexes[s],n=e(this.proxyOf.nodes[r],r),n!==!1);)this.indexes[s]+=1;return delete this.indexes[s],n}every(e){return this.nodes.every(e)}getIterator(){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach+=1;let e=this.lastEach;return this.indexes[e]=0,e}getProxyProcessor(){return{get(e,s){return s==="proxyOf"?e:e[s]?s==="each"||typeof s=="string"&&s.startsWith("walk")?(...r)=>e[s](...r.map(n=>typeof n=="function"?(i,o)=>n(i.toProxy(),o):n)):s==="every"||s==="some"?r=>e[s]((n,...i)=>r(n.toProxy(),...i)):s==="root"?()=>e.root().toProxy():s==="nodes"?e.nodes.map(r=>r.toProxy()):s==="first"||s==="last"?e[s].toProxy():e[s]:e[s]},set(e,s,r){return e[s]===r||(e[s]=r,(s==="name"||s==="params"||s==="selector")&&e.markDirty()),!0}}}index(e){return typeof e=="number"?e:(e.proxyOf&&(e=e.proxyOf),this.proxyOf.nodes.indexOf(e))}insertAfter(e,s){let r=this.index(e),n=this.normalize(s,this.proxyOf.nodes[r]).reverse();r=this.index(e);for(let o of n)this.proxyOf.nodes.splice(r+1,0,o);let i;for(let o in this.indexes)i=this.indexes[o],r<i&&(this.indexes[o]=i+n.length);return this.markDirty(),this}insertBefore(e,s){let r=this.index(e),n=r===0?"prepend":!1,i=this.normalize(s,this.proxyOf.nodes[r],n).reverse();r=this.index(e);for(let a of i)this.proxyOf.nodes.splice(r,0,a);let o;for(let a in this.indexes)o=this.indexes[a],r<=o&&(this.indexes[a]=o+i.length);return this.markDirty(),this}normalize(e,s){if(typeof e=="string")e=Ii(Pi(e).nodes);else if(Array.isArray(e)){e=e.slice(0);for(let n of e)n.parent&&n.parent.removeChild(n,"ignore")}else if(e.type==="root"&&this.type!=="document"){e=e.nodes.slice(0);for(let n of e)n.parent&&n.parent.removeChild(n,"ignore")}else if(e.type)e=[e];else if(e.prop){if(typeof e.value>"u")throw new Error("Value field is missed in node creation");typeof e.value!="string"&&(e.value=String(e.value)),e=[new Oi(e)]}else if(e.selector)e=[new us(e)];else if(e.name)e=[new ls(e)];else if(e.text)e=[new Ni(e)];else throw new Error("Unknown node type in node creation");return e.map(n=>(n[Ai]||t.rebuild(n),n=n.proxyOf,n.parent&&n.parent.removeChild(n),n[Ci]&&qi(n),typeof n.raws.before>"u"&&s&&typeof s.raws.before<"u"&&(n.raws.before=s.raws.before.replace(/\S/g,"")),n.parent=this.proxyOf,n))}prepend(...e){e=e.reverse();for(let s of e){let r=this.normalize(s,this.first,"prepend").reverse();for(let n of r)this.proxyOf.nodes.unshift(n);for(let n in this.indexes)this.indexes[n]=this.indexes[n]+r.length}return this.markDirty(),this}push(e){return e.parent=this,this.proxyOf.nodes.push(e),this}removeAll(){for(let e of this.proxyOf.nodes)e.parent=void 0;return this.proxyOf.nodes=[],this.markDirty(),this}removeChild(e){e=this.index(e),this.proxyOf.nodes[e].parent=void 0,this.proxyOf.nodes.splice(e,1);let s;for(let r in this.indexes)s=this.indexes[r],s>=e&&(this.indexes[r]=s-1);return this.markDirty(),this}replaceValues(e,s,r){return r||(r=s,s={}),this.walkDecls(n=>{s.props&&!s.props.includes(n.prop)||s.fast&&!n.value.includes(s.fast)||(n.value=n.value.replace(e,r))}),this.markDirty(),this}some(e){return this.nodes.some(e)}walk(e){return this.each((s,r)=>{let n;try{n=e(s,r)}catch(i){throw s.addToError(i)}return n!==!1&&s.walk&&(n=s.walk(e)),n})}walkAtRules(e,s){return s?e instanceof RegExp?this.walk((r,n)=>{if(r.type==="atrule"&&e.test(r.name))return s(r,n)}):this.walk((r,n)=>{if(r.type==="atrule"&&r.name===e)return s(r,n)}):(s=e,this.walk((r,n)=>{if(r.type==="atrule")return s(r,n)}))}walkComments(e){return this.walk((s,r)=>{if(s.type==="comment")return e(s,r)})}walkDecls(e,s){return s?e instanceof RegExp?this.walk((r,n)=>{if(r.type==="decl"&&e.test(r.prop))return s(r,n)}):this.walk((r,n)=>{if(r.type==="decl"&&r.prop===e)return s(r,n)}):(s=e,this.walk((r,n)=>{if(r.type==="decl")return s(r,n)}))}walkRules(e,s){return s?e instanceof RegExp?this.walk((r,n)=>{if(r.type==="rule"&&e.test(r.selector))return s(r,n)}):this.walk((r,n)=>{if(r.type==="rule"&&r.selector===e)return s(r,n)}):(s=e,this.walk((r,n)=>{if(r.type==="rule")return s(r,n)}))}get first(){if(this.proxyOf.nodes)return this.proxyOf.nodes[0]}get last(){if(this.proxyOf.nodes)return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}};z.registerParse=t=>{Pi=t};z.registerRule=t=>{us=t};z.registerAtRule=t=>{ls=t};z.registerRoot=t=>{Ri=t};Li.exports=z;z.default=z;z.rebuild=t=>{t.type==="atrule"?Object.setPrototypeOf(t,ls.prototype):t.type==="rule"?Object.setPrototypeOf(t,us.prototype):t.type==="decl"?Object.setPrototypeOf(t,Oi.prototype):t.type==="comment"?Object.setPrototypeOf(t,Ni.prototype):t.type==="root"&&Object.setPrototypeOf(t,Ri.prototype),t[Ai]=!0,t.nodes&&t.nodes.forEach(e=>{z.rebuild(e)})}});var Ht=y((yv,Mi)=>{"use strict";var Vt=/[\t\n\f\r "#'()/;[\\\]{}]/g,Gt=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g,kc=/.[\r\n"'(/\\]/,Di=/[\da-f]/i;Mi.exports=function(e,s={}){let r=e.css.valueOf(),n=s.ignoreErrors,i,o,a,u,c,f,p,l,g,x,h=r.length,d=0,m=[],_=[];function w(){return d}function v($){throw e.error("Unclosed "+$,d)}function R(){return _.length===0&&d>=h}function F($){if(_.length)return _.pop();if(d>=h)return;let T=$?$.ignoreUnclosed:!1;switch(i=r.charCodeAt(d),i){case 10:case 32:case 9:case 13:case 12:{o=d;do o+=1,i=r.charCodeAt(o);while(i===32||i===10||i===9||i===13||i===12);x=["space",r.slice(d,o)],d=o-1;break}case 91:case 93:case 123:case 125:case 58:case 59:case 41:{let C=String.fromCharCode(i);x=[C,C,d];break}case 40:{if(l=m.length?m.pop()[1]:"",g=r.charCodeAt(d+1),l==="url"&&g!==39&&g!==34&&g!==32&&g!==10&&g!==9&&g!==12&&g!==13){o=d;do{if(f=!1,o=r.indexOf(")",o+1),o===-1)if(n||T){o=d;break}else v("bracket");for(p=o;r.charCodeAt(p-1)===92;)p-=1,f=!f}while(f);x=["brackets",r.slice(d,o+1),d,o],d=o}else o=r.indexOf(")",d+1),u=r.slice(d,o+1),o===-1||kc.test(u)?x=["(","(",d]:(x=["brackets",u,d,o],d=o);break}case 39:case 34:{a=i===39?"'":'"',o=d;do{if(f=!1,o=r.indexOf(a,o+1),o===-1)if(n||T){o=d+1;break}else v("string");for(p=o;r.charCodeAt(p-1)===92;)p-=1,f=!f}while(f);x=["string",r.slice(d,o+1),d,o],d=o;break}case 64:{Vt.lastIndex=d+1,Vt.test(r),Vt.lastIndex===0?o=r.length-1:o=Vt.lastIndex-2,x=["at-word",r.slice(d,o+1),d,o],d=o;break}case 92:{for(o=d,c=!0;r.charCodeAt(o+1)===92;)o+=1,c=!c;if(i=r.charCodeAt(o+1),c&&i!==47&&i!==32&&i!==10&&i!==9&&i!==13&&i!==12&&(o+=1,Di.test(r.charAt(o)))){for(;Di.test(r.charAt(o+1));)o+=1;r.charCodeAt(o+1)===32&&(o+=1)}x=["word",r.slice(d,o+1),d,o],d=o;break}default:{i===47&&r.charCodeAt(d+1)===42?(o=r.indexOf("*/",d+2)+1,o===0&&(n||T?o=r.length:v("comment")),x=["comment",r.slice(d,o+1),d,o],d=o):(Gt.lastIndex=d+1,Gt.test(r),Gt.lastIndex===0?o=r.length-1:o=Gt.lastIndex-2,x=["word",r.slice(d,o+1),d,o],m.push(x),d=o);break}}return d++,x}function K($){_.push($)}return{back:K,endOfFile:R,nextToken:F,position:w}}});var Kt=y((gv,Ui)=>{"use strict";var Bi=ne(),Ne=class extends Bi{constructor(e){super(e),this.type="atrule"}append(...e){return this.proxyOf.nodes||(this.nodes=[]),super.append(...e)}prepend(...e){return this.proxyOf.nodes||(this.nodes=[]),super.prepend(...e)}};Ui.exports=Ne;Ne.default=Ne;Bi.registerAtRule(Ne)});var Pe=y((wv,zi)=>{"use strict";var Fi=ne(),$i,Wi,ie=class extends Fi{constructor(e){super(e),this.type="root",this.nodes||(this.nodes=[])}normalize(e,s,r){let n=super.normalize(e);if(s){if(r==="prepend")this.nodes.length>1?s.raws.before=this.nodes[1].raws.before:delete s.raws.before;else if(this.first!==s)for(let i of n)i.raws.before=s.raws.before}return n}removeChild(e,s){let r=this.index(e);return!s&&r===0&&this.nodes.length>1&&(this.nodes[1].raws.before=this.nodes[r].raws.before),super.removeChild(e)}toResult(e={}){return new $i(new Wi,this,e).stringify()}};ie.registerLazyResult=t=>{$i=t};ie.registerProcessor=t=>{Wi=t};zi.exports=ie;ie.default=ie;Fi.registerRoot(ie)});var cs=y((vv,Yi)=>{"use strict";var pt={comma(t){return pt.split(t,[","],!0)},space(t){let e=[" ",`
`,"	"];return pt.split(t,e)},split(t,e,s){let r=[],n="",i=!1,o=0,a=!1,u="",c=!1;for(let f of t)c?c=!1:f==="\\"?c=!0:a?f===u&&(a=!1):f==='"'||f==="'"?(a=!0,u=f):f==="("?o+=1:f===")"?o>0&&(o-=1):o===0&&e.includes(f)&&(i=!0),i?(n!==""&&r.push(n.trim()),n="",i=!1):n+=f;return(s||n!=="")&&r.push(n.trim()),r}};Yi.exports=pt;pt.default=pt});var Qt=y((xv,Gi)=>{"use strict";var Vi=ne(),Ec=cs(),Re=class extends Vi{constructor(e){super(e),this.type="rule",this.nodes||(this.nodes=[])}get selectors(){return Ec.comma(this.selector)}set selectors(e){let s=this.selector?this.selector.match(/,\s*/):null,r=s?s[0]:","+this.raw("between","beforeOpen");this.selector=e.join(r)}};Gi.exports=Re;Re.default=Re;Vi.registerRule(Re)});var jt=y((_v,Qi)=>{"use strict";var Sc=ct(),Tc=Ht(),Cc=Oe(),Ac=Kt(),Oc=Pe(),Hi=Qt(),Ki={empty:!0,space:!0};function Nc(t){for(let e=t.length-1;e>=0;e--){let s=t[e],r=s[3]||s[2];if(r)return r}}var fs=class{constructor(e){this.input=e,this.root=new Oc,this.current=this.root,this.spaces="",this.semicolon=!1,this.customProperty=!1,this.createTokenizer(),this.root.source={input:e,start:{column:1,line:1,offset:0}}}atrule(e){let s=new Ac;s.name=e[1].slice(1),s.name===""&&this.unnamedAtrule(s,e),this.init(s,e[2]);let r,n,i,o=!1,a=!1,u=[],c=[];for(;!this.tokenizer.endOfFile();){if(e=this.tokenizer.nextToken(),r=e[0],r==="("||r==="["?c.push(r==="("?")":"]"):r==="{"&&c.length>0?c.push("}"):r===c[c.length-1]&&c.pop(),c.length===0)if(r===";"){s.source.end=this.getPosition(e[2]),s.source.end.offset++,this.semicolon=!0;break}else if(r==="{"){a=!0;break}else if(r==="}"){if(u.length>0){for(i=u.length-1,n=u[i];n&&n[0]==="space";)n=u[--i];n&&(s.source.end=this.getPosition(n[3]||n[2]),s.source.end.offset++)}this.end(e);break}else u.push(e);else u.push(e);if(this.tokenizer.endOfFile()){o=!0;break}}s.raws.between=this.spacesAndCommentsFromEnd(u),u.length?(s.raws.afterName=this.spacesAndCommentsFromStart(u),this.raw(s,"params",u),o&&(e=u[u.length-1],s.source.end=this.getPosition(e[3]||e[2]),s.source.end.offset++,this.spaces=s.raws.between,s.raws.between="")):(s.raws.afterName="",s.params=""),a&&(s.nodes=[],this.current=s)}checkMissedSemicolon(e){let s=this.colon(e);if(s===!1)return;let r=0,n;for(let i=s-1;i>=0&&(n=e[i],!(n[0]!=="space"&&(r+=1,r===2)));i--);throw this.input.error("Missed semicolon",n[0]==="word"?n[3]+1:n[2])}colon(e){let s=0,r,n,i;for(let[o,a]of e.entries()){if(r=a,n=r[0],n==="("&&(s+=1),n===")"&&(s-=1),s===0&&n===":")if(!i)this.doubleColon(r);else{if(i[0]==="word"&&i[1]==="progid")continue;return o}i=r}return!1}comment(e){let s=new Cc;this.init(s,e[2]),s.source.end=this.getPosition(e[3]||e[2]),s.source.end.offset++;let r=e[1].slice(2,-2);if(/^\s*$/.test(r))s.text="",s.raws.left=r,s.raws.right="";else{let n=r.match(/^(\s*)([^]*\S)(\s*)$/);s.text=n[2],s.raws.left=n[1],s.raws.right=n[3]}}createTokenizer(){this.tokenizer=Tc(this.input)}decl(e,s){let r=new Sc;this.init(r,e[0][2]);let n=e[e.length-1];for(n[0]===";"&&(this.semicolon=!0,e.pop()),r.source.end=this.getPosition(n[3]||n[2]||Nc(e)),r.source.end.offset++;e[0][0]!=="word";)e.length===1&&this.unknownWord(e),r.raws.before+=e.shift()[1];for(r.source.start=this.getPosition(e[0][2]),r.prop="";e.length;){let c=e[0][0];if(c===":"||c==="space"||c==="comment")break;r.prop+=e.shift()[1]}r.raws.between="";let i;for(;e.length;)if(i=e.shift(),i[0]===":"){r.raws.between+=i[1];break}else i[0]==="word"&&/\w/.test(i[1])&&this.unknownWord([i]),r.raws.between+=i[1];(r.prop[0]==="_"||r.prop[0]==="*")&&(r.raws.before+=r.prop[0],r.prop=r.prop.slice(1));let o=[],a;for(;e.length&&(a=e[0][0],!(a!=="space"&&a!=="comment"));)o.push(e.shift());this.precheckMissedSemicolon(e);for(let c=e.length-1;c>=0;c--){if(i=e[c],i[1].toLowerCase()==="!important"){r.important=!0;let f=this.stringFrom(e,c);f=this.spacesFromEnd(e)+f,f!==" !important"&&(r.raws.important=f);break}else if(i[1].toLowerCase()==="important"){let f=e.slice(0),p="";for(let l=c;l>0;l--){let g=f[l][0];if(p.trim().indexOf("!")===0&&g!=="space")break;p=f.pop()[1]+p}p.trim().indexOf("!")===0&&(r.important=!0,r.raws.important=p,e=f)}if(i[0]!=="space"&&i[0]!=="comment")break}e.some(c=>c[0]!=="space"&&c[0]!=="comment")&&(r.raws.between+=o.map(c=>c[1]).join(""),o=[]),this.raw(r,"value",o.concat(e),s),r.value.includes(":")&&!s&&this.checkMissedSemicolon(e)}doubleColon(e){throw this.input.error("Double colon",{offset:e[2]},{offset:e[2]+e[1].length})}emptyRule(e){let s=new Hi;this.init(s,e[2]),s.selector="",s.raws.between="",this.current=s}end(e){this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.semicolon=!1,this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.spaces="",this.current.parent?(this.current.source.end=this.getPosition(e[2]),this.current.source.end.offset++,this.current=this.current.parent):this.unexpectedClose(e)}endFile(){this.current.parent&&this.unclosedBlock(),this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.root.source.end=this.getPosition(this.tokenizer.position())}freeSemicolon(e){if(this.spaces+=e[1],this.current.nodes){let s=this.current.nodes[this.current.nodes.length-1];s&&s.type==="rule"&&!s.raws.ownSemicolon&&(s.raws.ownSemicolon=this.spaces,this.spaces="")}}getPosition(e){let s=this.input.fromOffset(e);return{column:s.col,line:s.line,offset:e}}init(e,s){this.current.push(e),e.source={input:this.input,start:this.getPosition(s)},e.raws.before=this.spaces,this.spaces="",e.type!=="comment"&&(this.semicolon=!1)}other(e){let s=!1,r=null,n=!1,i=null,o=[],a=e[1].startsWith("--"),u=[],c=e;for(;c;){if(r=c[0],u.push(c),r==="("||r==="[")i||(i=c),o.push(r==="("?")":"]");else if(a&&n&&r==="{")i||(i=c),o.push("}");else if(o.length===0)if(r===";")if(n){this.decl(u,a);return}else break;else if(r==="{"){this.rule(u);return}else if(r==="}"){this.tokenizer.back(u.pop()),s=!0;break}else r===":"&&(n=!0);else r===o[o.length-1]&&(o.pop(),o.length===0&&(i=null));c=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile()&&(s=!0),o.length>0&&this.unclosedBracket(i),s&&n){if(!a)for(;u.length&&(c=u[u.length-1][0],!(c!=="space"&&c!=="comment"));)this.tokenizer.back(u.pop());this.decl(u,a)}else this.unknownWord(u)}parse(){let e;for(;!this.tokenizer.endOfFile();)switch(e=this.tokenizer.nextToken(),e[0]){case"space":this.spaces+=e[1];break;case";":this.freeSemicolon(e);break;case"}":this.end(e);break;case"comment":this.comment(e);break;case"at-word":this.atrule(e);break;case"{":this.emptyRule(e);break;default:this.other(e);break}this.endFile()}precheckMissedSemicolon(){}raw(e,s,r,n){let i,o,a=r.length,u="",c=!0,f,p;for(let l=0;l<a;l+=1)i=r[l],o=i[0],o==="space"&&l===a-1&&!n?c=!1:o==="comment"?(p=r[l-1]?r[l-1][0]:"empty",f=r[l+1]?r[l+1][0]:"empty",!Ki[p]&&!Ki[f]?u.slice(-1)===","?c=!1:u+=i[1]:c=!1):u+=i[1];if(!c){let l=r.reduce((g,x)=>g+x[1],"");e.raws[s]={raw:l,value:u}}e[s]=u}rule(e){e.pop();let s=new Hi;this.init(s,e[0][2]),s.raws.between=this.spacesAndCommentsFromEnd(e),this.raw(s,"selector",e),this.current=s}spacesAndCommentsFromEnd(e){let s,r="";for(;e.length&&(s=e[e.length-1][0],!(s!=="space"&&s!=="comment"));)r=e.pop()[1]+r;return r}spacesAndCommentsFromStart(e){let s,r="";for(;e.length&&(s=e[0][0],!(s!=="space"&&s!=="comment"));)r+=e.shift()[1];return r}spacesFromEnd(e){let s,r="";for(;e.length&&(s=e[e.length-1][0],s==="space");)r=e.pop()[1]+r;return r}stringFrom(e,s){let r="";for(let n=s;n<e.length;n++)r+=e[n][1];return e.splice(s,e.length-s),r}unclosedBlock(){let e=this.current.source.start;throw this.input.error("Unclosed block",e.line,e.column)}unclosedBracket(e){throw this.input.error("Unclosed bracket",{offset:e[2]},{offset:e[2]+1})}unexpectedClose(e){throw this.input.error("Unexpected }",{offset:e[2]},{offset:e[2]+1})}unknownWord(e){throw this.input.error("Unknown word",{offset:e[0][2]},{offset:e[0][2]+e[0][1].length})}unnamedAtrule(e,s){throw this.input.error("At-rule without name",{offset:s[2]},{offset:s[2]+s[1].length})}};Qi.exports=fs});var ji=y(()=>{});var Xi=y((Ev,Ji)=>{var Pc="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",Rc=(t,e=21)=>(s=e)=>{let r="",n=s;for(;n--;)r+=t[Math.random()*t.length|0];return r},Ic=(t=21)=>{let e="",s=t;for(;s--;)e+=Pc[Math.random()*64|0];return e};Ji.exports={nanoid:Ic,customAlphabet:Rc}});var ps=y((Sv,Zi)=>{Zi.exports=class{}});var qe=y((Cv,so)=>{"use strict";var{SourceMapConsumer:qc,SourceMapGenerator:Lc}=ji(),{fileURLToPath:eo,pathToFileURL:Jt}={},{isAbsolute:ms,resolve:ys}={},{nanoid:Dc}=Xi(),hs=is(),to=Wt(),Mc=ps(),ds=Symbol("fromOffsetCache"),Bc=!!(qc&&Lc),ro=!!(ys&&ms),Ie=class{constructor(e,s={}){if(e===null||typeof e>"u"||typeof e=="object"&&!e.toString)throw new Error(`PostCSS received ${e} instead of CSS string`);if(this.css=e.toString(),this.css[0]==="\uFEFF"||this.css[0]==="\uFFFE"?(this.hasBOM=!0,this.css=this.css.slice(1)):this.hasBOM=!1,s.from&&(!ro||/^\w+:\/\//.test(s.from)||ms(s.from)?this.file=s.from:this.file=ys(s.from)),ro&&Bc){let r=new Mc(this.css,s);if(r.text){this.map=r;let n=r.consumer().file;!this.file&&n&&(this.file=this.mapResolve(n))}}this.file||(this.id="<input css "+Dc(6)+">"),this.map&&(this.map.file=this.from)}error(e,s,r,n={}){let i,o,a;if(s&&typeof s=="object"){let c=s,f=r;if(typeof c.offset=="number"){let p=this.fromOffset(c.offset);s=p.line,r=p.col}else s=c.line,r=c.column;if(typeof f.offset=="number"){let p=this.fromOffset(f.offset);o=p.line,a=p.col}else o=f.line,a=f.column}else if(!r){let c=this.fromOffset(s);s=c.line,r=c.col}let u=this.origin(s,r,o,a);return u?i=new to(e,u.endLine===void 0?u.line:{column:u.column,line:u.line},u.endLine===void 0?u.column:{column:u.endColumn,line:u.endLine},u.source,u.file,n.plugin):i=new to(e,o===void 0?s:{column:r,line:s},o===void 0?r:{column:a,line:o},this.css,this.file,n.plugin),i.input={column:r,endColumn:a,endLine:o,line:s,source:this.css},this.file&&(Jt&&(i.input.url=Jt(this.file).toString()),i.input.file=this.file),i}fromOffset(e){let s,r;if(this[ds])r=this[ds];else{let i=this.css.split(`
`);r=new Array(i.length);let o=0;for(let a=0,u=i.length;a<u;a++)r[a]=o,o+=i[a].length+1;this[ds]=r}s=r[r.length-1];let n=0;if(e>=s)n=r.length-1;else{let i=r.length-2,o;for(;n<i;)if(o=n+(i-n>>1),e<r[o])i=o-1;else if(e>=r[o+1])n=o+1;else{n=o;break}}return{col:e-r[n]+1,line:n+1}}mapResolve(e){return/^\w+:\/\//.test(e)?e:ys(this.map.consumer().sourceRoot||this.map.root||".",e)}origin(e,s,r,n){if(!this.map)return!1;let i=this.map.consumer(),o=i.originalPositionFor({column:s,line:e});if(!o.source)return!1;let a;typeof r=="number"&&(a=i.originalPositionFor({column:n,line:r}));let u;ms(o.source)?u=Jt(o.source):u=new URL(o.source,this.map.consumer().sourceRoot||Jt(this.map.mapFile));let c={column:o.column,endColumn:a&&a.column,endLine:a&&a.line,line:o.line,url:u.toString()};if(u.protocol==="file:")if(eo)c.file=eo(u);else throw new Error("file: protocol is not available in this PostCSS build");let f=i.sourceContentFor(o.source);return f&&(c.source=f),c}toJSON(){let e={};for(let s of["hasBOM","css","file","id"])this[s]!=null&&(e[s]=this[s]);return this.map&&(e.map={...this.map},e.map.consumerCache&&(e.map.consumerCache=void 0)),e}get from(){return this.file||this.id}};so.exports=Ie;Ie.default=Ie;hs&&hs.registerInput&&hs.registerInput(Ie)});var ht=y((Av,no)=>{"use strict";var Uc=ne(),Fc=jt(),$c=qe();function Xt(t,e){let s=new $c(t,e),r=new Fc(s);try{r.parse()}catch(n){throw n}return r.root}no.exports=Xt;Xt.default=Xt;Uc.registerParse(Xt)});var io=y((Ov,gs)=>{var Wc=Ht(),zc=qe();gs.exports={isInlineComment(t){if(t[0]==="word"&&t[1].slice(0,2)==="//"){let e=t,s=[],r,n;for(;t;){if(/\r?\n/.test(t[1])){if(/['"].*\r?\n/.test(t[1])){s.push(t[1].substring(0,t[1].indexOf(`
`))),n=t[1].substring(t[1].indexOf(`
`));let o=this.input.css.valueOf().substring(this.tokenizer.position());n+=o,r=t[3]+o.length-n.length}else this.tokenizer.back(t);break}s.push(t[1]),r=t[2],t=this.tokenizer.nextToken({ignoreUnclosed:!0})}let i=["comment",s.join(""),e[2],r];return this.inlineComment(i),n&&(this.input=new zc(n),this.tokenizer=Wc(this.input)),!0}else if(t[1]==="/"){let e=this.tokenizer.nextToken({ignoreUnclosed:!0});if(e[0]==="comment"&&/^\/\*/.test(e[1]))return e[0]="word",e[1]=e[1].slice(1),t[1]="//",this.tokenizer.back(e),gs.exports.isInlineComment.bind(this)(t)}return!1}}});var ao=y((Nv,oo)=>{oo.exports={interpolation(t){let e=[t,this.tokenizer.nextToken()],s=["word","}"];if(e[0][1].length>1||e[1][0]!=="{")return this.tokenizer.back(e[1]),!1;for(t=this.tokenizer.nextToken();t&&s.includes(t[0]);)e.push(t),t=this.tokenizer.nextToken();let r=e.map(a=>a[1]),[n]=e,i=e.pop(),o=["word",r.join(""),n[2],i[2]];return this.tokenizer.back(t),this.tokenizer.back(o),!0}}});var lo=y((Pv,uo)=>{var Yc=/^#[0-9a-fA-F]{6}$|^#[0-9a-fA-F]{3}$/,Vc=/\.[0-9]/,Gc=t=>{let[,e]=t,[s]=e;return(s==="."||s==="#")&&Yc.test(e)===!1&&Vc.test(e)===!1};uo.exports={isMixinToken:Gc}});var fo=y((Rv,co)=>{var Hc=Ht(),Kc=/^url\((.+)\)/;co.exports=t=>{let{name:e,params:s=""}=t;if(e==="import"&&s.length){t.import=!0;let r=Hc({css:s});for(t.filename=s.replace(Kc,"$1");!r.endOfFile();){let[n,i]=r.nextToken();if(n==="word"&&i==="url")return;if(n==="brackets"){t.options=i,t.filename=s.replace(i,"").trim();break}}}}});var yo=y((Iv,mo)=>{var po=/:$/,ho=/^:(\s+)?/;mo.exports=t=>{let{name:e,params:s=""}=t;if(t.name.slice(-1)===":"){if(po.test(e)){let[r]=e.match(po);t.name=e.replace(r,""),t.raws.afterName=r+(t.raws.afterName||""),t.variable=!0,t.value=t.params}if(ho.test(s)){let[r]=s.match(ho);t.value=s.replace(r,""),t.raws.afterName=(t.raws.afterName||"")+r,t.variable=!0}}}});var vo=y((Lv,wo)=>{var Qc=Oe(),jc=jt(),{isInlineComment:Jc}=io(),{interpolation:go}=ao(),{isMixinToken:Xc}=lo(),Zc=fo(),ef=yo(),tf=/(!\s*important)$/i;wo.exports=class extends jc{constructor(...e){super(...e),this.lastNode=null}atrule(e){go.bind(this)(e)||(super.atrule(e),Zc(this.lastNode),ef(this.lastNode))}decl(...e){super.decl(...e),/extend\(.+\)/i.test(this.lastNode.value)&&(this.lastNode.extend=!0)}each(e){e[0][1]=` ${e[0][1]}`;let s=e.findIndex(a=>a[0]==="("),r=e.reverse().find(a=>a[0]===")"),n=e.reverse().indexOf(r),o=e.splice(s,n).map(a=>a[1]).join("");for(let a of e.reverse())this.tokenizer.back(a);this.atrule(this.tokenizer.nextToken()),this.lastNode.function=!0,this.lastNode.params=o}init(e,s,r){super.init(e,s,r),this.lastNode=e}inlineComment(e){let s=new Qc,r=e[1].slice(2);if(this.init(s,e[2]),s.source.end=this.getPosition(e[3]||e[2]),s.inline=!0,s.raws.begin="//",/^\s*$/.test(r))s.text="",s.raws.left=r,s.raws.right="";else{let n=r.match(/^(\s*)([^]*[^\s])(\s*)$/);[,s.raws.left,s.text,s.raws.right]=n}}mixin(e){let[s]=e,r=s[1].slice(0,1),n=e.findIndex(c=>c[0]==="brackets"),i=e.findIndex(c=>c[0]==="("),o="";if((n<0||n>3)&&i>0){let c=e.reduce((w,v,R)=>v[0]===")"?R:w),p=e.slice(i,c+i).map(w=>w[1]).join(""),[l]=e.slice(i),g=[l[2],l[3]],[x]=e.slice(c,c+1),h=[x[2],x[3]],d=["brackets",p].concat(g,h),m=e.slice(0,i),_=e.slice(c+1);e=m,e.push(d),e=e.concat(_)}let a=[];for(let c of e)if((c[1]==="!"||a.length)&&a.push(c),c[1]==="important")break;if(a.length){let[c]=a,f=e.indexOf(c),p=a[a.length-1],l=[c[2],c[3]],g=[p[4],p[5]],h=["word",a.map(d=>d[1]).join("")].concat(l,g);e.splice(f,a.length,h)}let u=e.findIndex(c=>tf.test(c[1]));u>0&&([,o]=e[u],e.splice(u,1));for(let c of e.reverse())this.tokenizer.back(c);this.atrule(this.tokenizer.nextToken()),this.lastNode.mixin=!0,this.lastNode.raws.identifier=r,o&&(this.lastNode.important=!0,this.lastNode.raws.important=o)}other(e){Jc.bind(this)(e)||super.other(e)}rule(e){let s=e[e.length-1],r=e[e.length-2];if(r[0]==="at-word"&&s[0]==="{"&&(this.tokenizer.back(s),go.bind(this)(r))){let i=this.tokenizer.nextToken();e=e.slice(0,e.length-2).concat([i]);for(let o of e.reverse())this.tokenizer.back(o);return}super.rule(e),/:extend\(.+\)/i.test(this.lastNode.selector)&&(this.lastNode.extend=!0)}unknownWord(e){let[s]=e;if(e[0][1]==="each"&&e[1][0]==="("){this.each(e);return}if(Xc(s)){this.mixin(e);return}super.unknownWord(e)}}});var _o=y((Mv,xo)=>{var rf=zt();xo.exports=class extends rf{atrule(e,s){if(!e.mixin&&!e.variable&&!e.function){super.atrule(e,s);return}let n=`${e.function?"":e.raws.identifier||"@"}${e.name}`,i=e.params?this.rawValue(e,"params"):"",o=e.raws.important||"";if(e.variable&&(i=e.value),typeof e.raws.afterName<"u"?n+=e.raws.afterName:i&&(n+=" "),e.nodes)this.block(e,n+i+o);else{let a=(e.raws.between||"")+o+(s?";":"");this.builder(n+i+a,e)}}comment(e){if(e.inline){let s=this.raw(e,"left","commentLeft"),r=this.raw(e,"right","commentRight");this.builder(`//${s}${e.text}${r}`,e)}else super.comment(e)}}});var bo=y((Bv,ws)=>{var sf=qe(),nf=vo(),of=_o();ws.exports={parse(t,e){let s=new sf(t,e),r=new nf(s);return r.parse(),r.root.walk(n=>{let i=s.css.lastIndexOf(n.source.input.css);if(i===0)return;if(i+n.source.input.css.length!==s.css.length)throw new Error("Invalid state detected in postcss-less");let o=i+n.source.start.offset,a=s.fromOffset(i+n.source.start.offset);if(n.source.start={offset:o,line:a.line,column:a.col},n.source.end){let u=i+n.source.end.offset,c=s.fromOffset(i+n.source.end.offset);n.source.end={offset:u,line:c.line,column:c.col}}}),r.root},stringify(t,e){new of(e).stringify(t)},nodeToString(t){let e="";return ws.exports.stringify(t,s=>{e+=s}),e}}});var vs=y((Uv,ko)=>{ko.exports=class{generate(){}}});var Zt=y(($v,To)=>{"use strict";var af=ne(),Eo,So,ye=class extends af{constructor(e){super({type:"document",...e}),this.nodes||(this.nodes=[])}toResult(e={}){return new Eo(new So,this,e).stringify()}};ye.registerLazyResult=t=>{Eo=t};ye.registerProcessor=t=>{So=t};To.exports=ye;ye.default=ye});var xs=y((Wv,Ao)=>{"use strict";var Co={};Ao.exports=function(e){Co[e]||(Co[e]=!0,typeof console<"u"&&console.warn&&console.warn(e))}});var _s=y((zv,Oo)=>{"use strict";var dt=class{constructor(e,s={}){if(this.type="warning",this.text=e,s.node&&s.node.source){let r=s.node.rangeBy(s);this.line=r.start.line,this.column=r.start.column,this.endLine=r.end.line,this.endColumn=r.end.column}for(let r in s)this[r]=s[r]}toString(){return this.node?this.node.error(this.text,{index:this.index,plugin:this.plugin,word:this.word}).message:this.plugin?this.plugin+": "+this.text:this.text}};Oo.exports=dt;dt.default=dt});var er=y((Yv,No)=>{"use strict";var uf=_s(),mt=class{constructor(e,s,r){this.processor=e,this.messages=[],this.root=s,this.opts=r,this.css=void 0,this.map=void 0}toString(){return this.css}warn(e,s={}){s.plugin||this.lastPlugin&&this.lastPlugin.postcssPlugin&&(s.plugin=this.lastPlugin.postcssPlugin);let r=new uf(e,s);return this.messages.push(r),r}warnings(){return this.messages.filter(e=>e.type==="warning")}get content(){return this.css}};No.exports=mt;mt.default=mt});var Es=y((Gv,qo)=>{"use strict";var{isClean:H,my:lf}=$t(),cf=vs(),ff=ot(),pf=ne(),hf=Zt(),Vv=xs(),Po=er(),df=ht(),mf=Pe(),yf={atrule:"AtRule",comment:"Comment",decl:"Declaration",document:"Document",root:"Root",rule:"Rule"},gf={AtRule:!0,AtRuleExit:!0,Comment:!0,CommentExit:!0,Declaration:!0,DeclarationExit:!0,Document:!0,DocumentExit:!0,Once:!0,OnceExit:!0,postcssPlugin:!0,prepare:!0,Root:!0,RootExit:!0,Rule:!0,RuleExit:!0},wf={Once:!0,postcssPlugin:!0,prepare:!0},Le=0;function yt(t){return typeof t=="object"&&typeof t.then=="function"}function Io(t){let e=!1,s=yf[t.type];return t.type==="decl"?e=t.prop.toLowerCase():t.type==="atrule"&&(e=t.name.toLowerCase()),e&&t.append?[s,s+"-"+e,Le,s+"Exit",s+"Exit-"+e]:e?[s,s+"-"+e,s+"Exit",s+"Exit-"+e]:t.append?[s,Le,s+"Exit"]:[s,s+"Exit"]}function Ro(t){let e;return t.type==="document"?e=["Document",Le,"DocumentExit"]:t.type==="root"?e=["Root",Le,"RootExit"]:e=Io(t),{eventIndex:0,events:e,iterator:0,node:t,visitorIndex:0,visitors:[]}}function bs(t){return t[H]=!1,t.nodes&&t.nodes.forEach(e=>bs(e)),t}var ks={},oe=class t{constructor(e,s,r){this.stringified=!1,this.processed=!1;let n;if(typeof s=="object"&&s!==null&&(s.type==="root"||s.type==="document"))n=bs(s);else if(s instanceof t||s instanceof Po)n=bs(s.root),s.map&&(typeof r.map>"u"&&(r.map={}),r.map.inline||(r.map.inline=!1),r.map.prev=s.map);else{let i=df;r.syntax&&(i=r.syntax.parse),r.parser&&(i=r.parser),i.parse&&(i=i.parse);try{n=i(s,r)}catch(o){this.processed=!0,this.error=o}n&&!n[lf]&&pf.rebuild(n)}this.result=new Po(e,n,r),this.helpers={...ks,postcss:ks,result:this.result},this.plugins=this.processor.plugins.map(i=>typeof i=="object"&&i.prepare?{...i,...i.prepare(this.result)}:i)}async(){return this.error?Promise.reject(this.error):this.processed?Promise.resolve(this.result):(this.processing||(this.processing=this.runAsync()),this.processing)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(e,s){let r=this.result.lastPlugin;try{s&&s.addToError(e),this.error=e,e.name==="CssSyntaxError"&&!e.plugin?(e.plugin=r.postcssPlugin,e.setMessage()):r.postcssVersion}catch(n){console&&console.error&&console.error(n)}return e}prepareVisitors(){this.listeners={};let e=(s,r,n)=>{this.listeners[r]||(this.listeners[r]=[]),this.listeners[r].push([s,n])};for(let s of this.plugins)if(typeof s=="object")for(let r in s){if(!gf[r]&&/^[A-Z]/.test(r))throw new Error(`Unknown event ${r} in ${s.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`);if(!wf[r])if(typeof s[r]=="object")for(let n in s[r])n==="*"?e(s,r,s[r][n]):e(s,r+"-"+n.toLowerCase(),s[r][n]);else typeof s[r]=="function"&&e(s,r,s[r])}this.hasListener=Object.keys(this.listeners).length>0}async runAsync(){this.plugin=0;for(let e=0;e<this.plugins.length;e++){let s=this.plugins[e],r=this.runOnRoot(s);if(yt(r))try{await r}catch(n){throw this.handleError(n)}}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[H];){e[H]=!0;let s=[Ro(e)];for(;s.length>0;){let r=this.visitTick(s);if(yt(r))try{await r}catch(n){let i=s[s.length-1].node;throw this.handleError(n,i)}}}if(this.listeners.OnceExit)for(let[s,r]of this.listeners.OnceExit){this.result.lastPlugin=s;try{if(e.type==="document"){let n=e.nodes.map(i=>r(i,this.helpers));await Promise.all(n)}else await r(e,this.helpers)}catch(n){throw this.handleError(n)}}}return this.processed=!0,this.stringify()}runOnRoot(e){this.result.lastPlugin=e;try{if(typeof e=="object"&&e.Once){if(this.result.root.type==="document"){let s=this.result.root.nodes.map(r=>e.Once(r,this.helpers));return yt(s[0])?Promise.all(s):s}return e.Once(this.result.root,this.helpers)}else if(typeof e=="function")return e(this.result.root,this.result)}catch(s){throw this.handleError(s)}}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=!0,this.sync();let e=this.result.opts,s=ff;e.syntax&&(s=e.syntax.stringify),e.stringifier&&(s=e.stringifier),s.stringify&&(s=s.stringify);let n=new cf(s,this.result.root,this.result.opts).generate();return this.result.css=n[0],this.result.map=n[1],this.result}sync(){if(this.error)throw this.error;if(this.processed)return this.result;if(this.processed=!0,this.processing)throw this.getAsyncError();for(let e of this.plugins){let s=this.runOnRoot(e);if(yt(s))throw this.getAsyncError()}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[H];)e[H]=!0,this.walkSync(e);if(this.listeners.OnceExit)if(e.type==="document")for(let s of e.nodes)this.visitSync(this.listeners.OnceExit,s);else this.visitSync(this.listeners.OnceExit,e)}return this.result}then(e,s){return this.async().then(e,s)}toString(){return this.css}visitSync(e,s){for(let[r,n]of e){this.result.lastPlugin=r;let i;try{i=n(s,this.helpers)}catch(o){throw this.handleError(o,s.proxyOf)}if(s.type!=="root"&&s.type!=="document"&&!s.parent)return!0;if(yt(i))throw this.getAsyncError()}}visitTick(e){let s=e[e.length-1],{node:r,visitors:n}=s;if(r.type!=="root"&&r.type!=="document"&&!r.parent){e.pop();return}if(n.length>0&&s.visitorIndex<n.length){let[o,a]=n[s.visitorIndex];s.visitorIndex+=1,s.visitorIndex===n.length&&(s.visitors=[],s.visitorIndex=0),this.result.lastPlugin=o;try{return a(r.toProxy(),this.helpers)}catch(u){throw this.handleError(u,r)}}if(s.iterator!==0){let o=s.iterator,a;for(;a=r.nodes[r.indexes[o]];)if(r.indexes[o]+=1,!a[H]){a[H]=!0,e.push(Ro(a));return}s.iterator=0,delete r.indexes[o]}let i=s.events;for(;s.eventIndex<i.length;){let o=i[s.eventIndex];if(s.eventIndex+=1,o===Le){r.nodes&&r.nodes.length&&(r[H]=!0,s.iterator=r.getIterator());return}else if(this.listeners[o]){s.visitors=this.listeners[o];return}}e.pop()}walkSync(e){e[H]=!0;let s=Io(e);for(let r of s)if(r===Le)e.nodes&&e.each(n=>{n[H]||this.walkSync(n)});else{let n=this.listeners[r];if(n&&this.visitSync(n,e.toProxy()))return}}warnings(){return this.sync().warnings()}get content(){return this.stringify().content}get css(){return this.stringify().css}get map(){return this.stringify().map}get messages(){return this.sync().messages}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){return this.sync().root}get[Symbol.toStringTag](){return"LazyResult"}};oe.registerPostcss=t=>{ks=t};qo.exports=oe;oe.default=oe;mf.registerLazyResult(oe);hf.registerLazyResult(oe)});var Do=y((Kv,Lo)=>{"use strict";var vf=vs(),xf=ot(),Hv=xs(),_f=ht(),bf=er(),gt=class{constructor(e,s,r){s=s.toString(),this.stringified=!1,this._processor=e,this._css=s,this._opts=r,this._map=void 0;let n,i=xf;this.result=new bf(this._processor,n,this._opts),this.result.css=s;let o=this;Object.defineProperty(this.result,"root",{get(){return o.root}});let a=new vf(i,n,this._opts,s);if(a.isMap()){let[u,c]=a.generate();u&&(this.result.css=u),c&&(this.result.map=c)}else a.clearAnnotation(),this.result.css=a.css}async(){return this.error?Promise.reject(this.error):Promise.resolve(this.result)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}sync(){if(this.error)throw this.error;return this.result}then(e,s){return this.async().then(e,s)}toString(){return this._css}warnings(){return[]}get content(){return this.result.css}get css(){return this.result.css}get map(){return this.result.map}get messages(){return[]}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){if(this._root)return this._root;let e,s=_f;try{e=s(this._css,this._opts)}catch(r){this.error=r}if(this.error)throw this.error;return this._root=e,e}get[Symbol.toStringTag](){return"NoWorkResult"}};Lo.exports=gt;gt.default=gt});var Bo=y((Qv,Mo)=>{"use strict";var kf=Do(),Ef=Es(),Sf=Zt(),Tf=Pe(),ge=class{constructor(e=[]){this.version="8.4.33",this.plugins=this.normalize(e)}normalize(e){let s=[];for(let r of e)if(r.postcss===!0?r=r():r.postcss&&(r=r.postcss),typeof r=="object"&&Array.isArray(r.plugins))s=s.concat(r.plugins);else if(typeof r=="object"&&r.postcssPlugin)s.push(r);else if(typeof r=="function")s.push(r);else if(!(typeof r=="object"&&(r.parse||r.stringify)))throw new Error(r+" is not a PostCSS plugin");return s}process(e,s={}){return!this.plugins.length&&!s.parser&&!s.stringifier&&!s.syntax?new kf(this,e,s):new Ef(this,e,s)}use(e){return this.plugins=this.plugins.concat(this.normalize([e])),this}};Mo.exports=ge;ge.default=ge;Tf.registerProcessor(ge);Sf.registerProcessor(ge)});var Fo=y((jv,Uo)=>{"use strict";var Cf=ct(),Af=ps(),Of=Oe(),Nf=Kt(),Pf=qe(),Rf=Pe(),If=Qt();function wt(t,e){if(Array.isArray(t))return t.map(n=>wt(n));let{inputs:s,...r}=t;if(s){e=[];for(let n of s){let i={...n,__proto__:Pf.prototype};i.map&&(i.map={...i.map,__proto__:Af.prototype}),e.push(i)}}if(r.nodes&&(r.nodes=t.nodes.map(n=>wt(n,e))),r.source){let{inputId:n,...i}=r.source;r.source=i,n!=null&&(r.source.input=e[n])}if(r.type==="root")return new Rf(r);if(r.type==="decl")return new Cf(r);if(r.type==="rule")return new If(r);if(r.type==="comment")return new Of(r);if(r.type==="atrule")return new Nf(r);throw new Error("Unknown node type: "+t.type)}Uo.exports=wt;wt.default=wt});var tr=y((Jv,Ho)=>{"use strict";var qf=Wt(),$o=ct(),Lf=Es(),Df=ne(),Ss=Bo(),Mf=ot(),Bf=Fo(),Wo=Zt(),Uf=_s(),zo=Oe(),Yo=Kt(),Ff=er(),$f=qe(),Wf=ht(),zf=cs(),Vo=Qt(),Go=Pe(),Yf=ut();function b(...t){return t.length===1&&Array.isArray(t[0])&&(t=t[0]),new Ss(t)}b.plugin=function(e,s){let r=!1;function n(...o){console&&console.warn&&!r&&(r=!0,console.warn(e+`: postcss.plugin was deprecated. Migration guide:
https://evilmartians.com/chronicles/postcss-8-plugin-migration`));let a=s(...o);return a.postcssPlugin=e,a.postcssVersion=new Ss().version,a}let i;return Object.defineProperty(n,"postcss",{get(){return i||(i=n()),i}}),n.process=function(o,a,u){return b([n(u)]).process(o,a)},n};b.stringify=Mf;b.parse=Wf;b.fromJSON=Bf;b.list=zf;b.comment=t=>new zo(t);b.atRule=t=>new Yo(t);b.decl=t=>new $o(t);b.rule=t=>new Vo(t);b.root=t=>new Go(t);b.document=t=>new Wo(t);b.CssSyntaxError=qf;b.Declaration=$o;b.Container=Df;b.Processor=Ss;b.Document=Wo;b.Comment=zo;b.Warning=Uf;b.AtRule=Yo;b.Result=Ff;b.Input=$f;b.Rule=Vo;b.Root=Go;b.Node=Yf;Lf.registerPostcss(b);Ho.exports=b;b.default=b});var Qo=y((Xv,Ko)=>{var{Container:Vf}=tr(),Ts=class extends Vf{constructor(e){super(e),this.type="decl",this.isNested=!0,this.nodes||(this.nodes=[])}};Ko.exports=Ts});var Xo=y((Zv,Jo)=>{"use strict";var rr=/[\t\n\f\r "#'()/;[\\\]{}]/g,sr=/[,\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g,Gf=/.[\r\n"'(/\\]/,jo=/[\da-f]/i,nr=/[\n\f\r]/g;Jo.exports=function(e,s={}){let r=e.css.valueOf(),n=s.ignoreErrors,i,o,a,u,c,f,p,l,g,x=r.length,h=0,d=[],m=[],_;function w(){return h}function v(T){throw e.error("Unclosed "+T,h)}function R(){return m.length===0&&h>=x}function F(){let T=1,C=!1,A=!1;for(;T>0;)o+=1,r.length<=o&&v("interpolation"),i=r.charCodeAt(o),l=r.charCodeAt(o+1),C?!A&&i===C?(C=!1,A=!1):i===92?A=!A:A&&(A=!1):i===39||i===34?C=i:i===125?T-=1:i===35&&l===123&&(T+=1)}function K(T){if(m.length)return m.pop();if(h>=x)return;let C=T?T.ignoreUnclosed:!1;switch(i=r.charCodeAt(h),i){case 10:case 32:case 9:case 13:case 12:{o=h;do o+=1,i=r.charCodeAt(o);while(i===32||i===10||i===9||i===13||i===12);g=["space",r.slice(h,o)],h=o-1;break}case 91:case 93:case 123:case 125:case 58:case 59:case 41:{let A=String.fromCharCode(i);g=[A,A,h];break}case 44:{g=["word",",",h,h+1];break}case 40:{if(p=d.length?d.pop()[1]:"",l=r.charCodeAt(h+1),p==="url"&&l!==39&&l!==34){for(_=1,f=!1,o=h+1;o<=r.length-1;){if(l=r.charCodeAt(o),l===92)f=!f;else if(l===40)_+=1;else if(l===41&&(_-=1,_===0))break;o+=1}u=r.slice(h,o+1),g=["brackets",u,h,o],h=o}else o=r.indexOf(")",h+1),u=r.slice(h,o+1),o===-1||Gf.test(u)?g=["(","(",h]:(g=["brackets",u,h,o],h=o);break}case 39:case 34:{for(a=i,o=h,f=!1;o<x&&(o++,o===x&&v("string"),i=r.charCodeAt(o),l=r.charCodeAt(o+1),!(!f&&i===a));)i===92?f=!f:f?f=!1:i===35&&l===123&&F();g=["string",r.slice(h,o+1),h,o],h=o;break}case 64:{rr.lastIndex=h+1,rr.test(r),rr.lastIndex===0?o=r.length-1:o=rr.lastIndex-2,g=["at-word",r.slice(h,o+1),h,o],h=o;break}case 92:{for(o=h,c=!0;r.charCodeAt(o+1)===92;)o+=1,c=!c;if(i=r.charCodeAt(o+1),c&&i!==47&&i!==32&&i!==10&&i!==9&&i!==13&&i!==12&&(o+=1,jo.test(r.charAt(o)))){for(;jo.test(r.charAt(o+1));)o+=1;r.charCodeAt(o+1)===32&&(o+=1)}g=["word",r.slice(h,o+1),h,o],h=o;break}default:l=r.charCodeAt(h+1),i===35&&l===123?(o=h,F(),u=r.slice(h,o+1),g=["word",u,h,o],h=o):i===47&&l===42?(o=r.indexOf("*/",h+2)+1,o===0&&(n||C?o=r.length:v("comment")),g=["comment",r.slice(h,o+1),h,o],h=o):i===47&&l===47?(nr.lastIndex=h+1,nr.test(r),nr.lastIndex===0?o=r.length-1:o=nr.lastIndex-2,u=r.slice(h,o+1),g=["comment",u,h,o,"inline"],h=o):(sr.lastIndex=h+1,sr.test(r),sr.lastIndex===0?o=r.length-1:o=sr.lastIndex-2,g=["word",r.slice(h,o+1),h,o],d.push(g),h=o);break}return h++,g}function $(T){m.push(T)}return{back:$,endOfFile:R,nextToken:K,position:w}}});var ea=y((ex,Zo)=>{var{Comment:Hf}=tr(),Kf=jt(),Qf=Qo(),jf=Xo(),Cs=class extends Kf{atrule(e){let s=e[1],r=e;for(;!this.tokenizer.endOfFile();){let n=this.tokenizer.nextToken();if(n[0]==="word"&&n[2]===r[3]+1)s+=n[1],r=n;else{this.tokenizer.back(n);break}}super.atrule(["at-word",s,e[2],r[3]])}comment(e){if(e[4]==="inline"){let s=new Hf;this.init(s,e[2]),s.raws.inline=!0;let r=this.input.fromOffset(e[3]);s.source.end={column:r.col,line:r.line,offset:e[3]+1};let n=e[1].slice(2);if(/^\s*$/.test(n))s.text="",s.raws.left=n,s.raws.right="";else{let i=n.match(/^(\s*)([^]*\S)(\s*)$/),o=i[2].replace(/(\*\/|\/\*)/g,"*//*");s.text=o,s.raws.left=i[1],s.raws.right=i[3],s.raws.text=i[2]}}else super.comment(e)}createTokenizer(){this.tokenizer=jf(this.input)}raw(e,s,r,n){if(super.raw(e,s,r,n),e.raws[s]){let i=e.raws[s].raw;e.raws[s].raw=r.reduce((o,a)=>{if(a[0]==="comment"&&a[4]==="inline"){let u=a[1].slice(2).replace(/(\*\/|\/\*)/g,"*//*");return o+"/*"+u+"*/"}else return o+a[1]},""),i!==e.raws[s].raw&&(e.raws[s].scss=i)}}rule(e){let s=!1,r=0,n="";for(let i of e)if(s)i[0]!=="comment"&&i[0]!=="{"&&(n+=i[1]);else{if(i[0]==="space"&&i[1].includes(`
`))break;i[0]==="("?r+=1:i[0]===")"?r-=1:r===0&&i[0]===":"&&(s=!0)}if(!s||n.trim()===""||/^[#:A-Za-z-]/.test(n))super.rule(e);else{e.pop();let i=new Qf;this.init(i,e[0][2]);let o;for(let u=e.length-1;u>=0;u--)if(e[u][0]!=="space"){o=e[u];break}if(o[3]){let u=this.input.fromOffset(o[3]);i.source.end={column:u.col,line:u.line,offset:o[3]+1}}else{let u=this.input.fromOffset(o[2]);i.source.end={column:u.col,line:u.line,offset:o[2]+1}}for(;e[0][0]!=="word";)i.raws.before+=e.shift()[1];if(e[0][2]){let u=this.input.fromOffset(e[0][2]);i.source.start={column:u.col,line:u.line,offset:e[0][2]}}for(i.prop="";e.length;){let u=e[0][0];if(u===":"||u==="space"||u==="comment")break;i.prop+=e.shift()[1]}i.raws.between="";let a;for(;e.length;)if(a=e.shift(),a[0]===":"){i.raws.between+=a[1];break}else i.raws.between+=a[1];(i.prop[0]==="_"||i.prop[0]==="*")&&(i.raws.before+=i.prop[0],i.prop=i.prop.slice(1)),i.raws.between+=this.spacesAndCommentsFromStart(e),this.precheckMissedSemicolon(e);for(let u=e.length-1;u>0;u--){if(a=e[u],a[1]==="!important"){i.important=!0;let c=this.stringFrom(e,u);c=this.spacesFromEnd(e)+c,c!==" !important"&&(i.raws.important=c);break}else if(a[1]==="important"){let c=e.slice(0),f="";for(let p=u;p>0;p--){let l=c[p][0];if(f.trim().indexOf("!")===0&&l!=="space")break;f=c.pop()[1]+f}f.trim().indexOf("!")===0&&(i.important=!0,i.raws.important=f,e=c)}if(a[0]!=="space"&&a[0]!=="comment")break}this.raw(i,"value",e),i.value.includes(":")&&this.checkMissedSemicolon(e),this.current=i}}};Zo.exports=Cs});var ra=y((tx,ta)=>{var{Input:Jf}=tr(),Xf=ea();ta.exports=function(e,s){let r=new Jf(e,s),n=new Xf(r);return n.parse(),n.root}});var Os=y(As=>{"use strict";Object.defineProperty(As,"__esModule",{value:!0});function ep(t){this.after=t.after,this.before=t.before,this.type=t.type,this.value=t.value,this.sourceIndex=t.sourceIndex}As.default=ep});var Ps=y(Ns=>{"use strict";Object.defineProperty(Ns,"__esModule",{value:!0});var tp=Os(),na=rp(tp);function rp(t){return t&&t.__esModule?t:{default:t}}function vt(t){var e=this;this.constructor(t),this.nodes=t.nodes,this.after===void 0&&(this.after=this.nodes.length>0?this.nodes[this.nodes.length-1].after:""),this.before===void 0&&(this.before=this.nodes.length>0?this.nodes[0].before:""),this.sourceIndex===void 0&&(this.sourceIndex=this.before.length),this.nodes.forEach(function(s){s.parent=e})}vt.prototype=Object.create(na.default.prototype);vt.constructor=na.default;vt.prototype.walk=function(e,s){for(var r=typeof e=="string"||e instanceof RegExp,n=r?s:e,i=typeof e=="string"?new RegExp(e):e,o=0;o<this.nodes.length;o++){var a=this.nodes[o],u=r?i.test(a.type):!0;if(u&&n&&n(a,o,this.nodes)===!1||a.nodes&&a.walk(e,s)===!1)return!1}return!0};vt.prototype.each=function(){for(var e=arguments.length<=0||arguments[0]===void 0?function(){}:arguments[0],s=0;s<this.nodes.length;s++){var r=this.nodes[s];if(e(r,s,this.nodes)===!1)return!1}return!0};Ns.default=vt});var ua=y(xt=>{"use strict";Object.defineProperty(xt,"__esModule",{value:!0});xt.parseMediaFeature=aa;xt.parseMediaQuery=Is;xt.parseMediaList=ip;var sp=Os(),ia=oa(sp),np=Ps(),Rs=oa(np);function oa(t){return t&&t.__esModule?t:{default:t}}function aa(t){var e=arguments.length<=1||arguments[1]===void 0?0:arguments[1],s=[{mode:"normal",character:null}],r=[],n=0,i="",o=null,a=null,u=e,c=t;t[0]==="("&&t[t.length-1]===")"&&(c=t.substring(1,t.length-1),u++);for(var f=0;f<c.length;f++){var p=c[f];if((p==="'"||p==='"')&&(s[n].isCalculationEnabled===!0?(s.push({mode:"string",isCalculationEnabled:!1,character:p}),n++):s[n].mode==="string"&&s[n].character===p&&c[f-1]!=="\\"&&(s.pop(),n--)),p==="{"?(s.push({mode:"interpolation",isCalculationEnabled:!0}),n++):p==="}"&&(s.pop(),n--),s[n].mode==="normal"&&p===":"){var l=c.substring(f+1);a={type:"value",before:/^(\s*)/.exec(l)[1],after:/(\s*)$/.exec(l)[1],value:l.trim()},a.sourceIndex=a.before.length+f+1+u,o={type:"colon",sourceIndex:f+u,after:a.before,value:":"};break}i+=p}return i={type:"media-feature",before:/^(\s*)/.exec(i)[1],after:/(\s*)$/.exec(i)[1],value:i.trim()},i.sourceIndex=i.before.length+u,r.push(i),o!==null&&(o.before=i.after,r.push(o)),a!==null&&r.push(a),r}function Is(t){var e=arguments.length<=1||arguments[1]===void 0?0:arguments[1],s=[],r=0,n=!1,i=void 0;function o(){return{before:"",after:"",value:""}}i=o();for(var a=0;a<t.length;a++){var u=t[a];n?(i.value+=u,(u==="{"||u==="(")&&r++,(u===")"||u==="}")&&r--):u.search(/\s/)!==-1?i.before+=u:(u==="("&&(i.type="media-feature-expression",r++),i.value=u,i.sourceIndex=e+a,n=!0),n&&r===0&&(u===")"||a===t.length-1||t[a+1].search(/\s/)!==-1)&&(["not","only","and"].indexOf(i.value)!==-1&&(i.type="keyword"),i.type==="media-feature-expression"&&(i.nodes=aa(i.value,i.sourceIndex)),s.push(Array.isArray(i.nodes)?new Rs.default(i):new ia.default(i)),i=o(),n=!1)}for(var c=0;c<s.length;c++)if(i=s[c],c>0&&(s[c-1].after=i.before),i.type===void 0){if(c>0){if(s[c-1].type==="media-feature-expression"){i.type="keyword";continue}if(s[c-1].value==="not"||s[c-1].value==="only"){i.type="media-type";continue}if(s[c-1].value==="and"){i.type="media-feature-expression";continue}s[c-1].type==="media-type"&&(s[c+1]?i.type=s[c+1].type==="media-feature-expression"?"keyword":"media-feature-expression":i.type="media-feature-expression")}if(c===0){if(!s[c+1]){i.type="media-type";continue}if(s[c+1]&&(s[c+1].type==="media-feature-expression"||s[c+1].type==="keyword")){i.type="media-type";continue}if(s[c+2]){if(s[c+2].type==="media-feature-expression"){i.type="media-type",s[c+1].type="keyword";continue}if(s[c+2].type==="keyword"){i.type="keyword",s[c+1].type="media-type";continue}}if(s[c+3]&&s[c+3].type==="media-feature-expression"){i.type="keyword",s[c+1].type="media-type",s[c+2].type="keyword";continue}}}return s}function ip(t){var e=[],s=0,r=0,n=/^(\s*)url\s*\(/.exec(t);if(n!==null){for(var i=n[0].length,o=1;o>0;){var a=t[i];a==="("&&o++,a===")"&&o--,i++}e.unshift(new ia.default({type:"url",value:t.substring(0,i).trim(),sourceIndex:n[1].length,before:n[1],after:/^(\s*)/.exec(t.substring(i))[1]})),s=i}for(var u=s;u<t.length;u++){var c=t[u];if(c==="("&&r++,c===")"&&r--,r===0&&c===","){var f=t.substring(s,u),p=/^(\s*)/.exec(f)[1];e.push(new Rs.default({type:"media-query",value:f.trim(),sourceIndex:s+p.length,nodes:Is(f,s),before:p,after:/(\s*)$/.exec(f)[1]})),s=u+1}}var l=t.substring(s),g=/^(\s*)/.exec(l)[1];return e.push(new Rs.default({type:"media-query",value:l.trim(),sourceIndex:s+g.length,nodes:Is(l,s),before:g,after:/(\s*)$/.exec(l)[1]})),e}});var la=y(qs=>{"use strict";Object.defineProperty(qs,"__esModule",{value:!0});qs.default=cp;var op=Ps(),ap=lp(op),up=ua();function lp(t){return t&&t.__esModule?t:{default:t}}function cp(t){return new ap.default({nodes:(0,up.parseMediaList)(t),type:"media-query-list",value:t.trim()})}});var Ds=y((cx,pa)=>{pa.exports=function(e,s){if(s=typeof s=="number"?s:1/0,!s)return Array.isArray(e)?e.map(function(n){return n}):e;return r(e,1);function r(n,i){return n.reduce(function(o,a){return Array.isArray(a)&&i<s?o.concat(r(a,i+1)):o.concat(a)},[])}}});var Ms=y((fx,ha)=>{ha.exports=function(t,e){for(var s=-1,r=[];(s=t.indexOf(e,s+1))!==-1;)r.push(s);return r}});var Bs=y((px,da)=>{"use strict";function hp(t,e){for(var s=1,r=t.length,n=t[0],i=t[0],o=1;o<r;++o)if(i=n,n=t[o],e(n,i)){if(o===s){s++;continue}t[s++]=n}return t.length=s,t}function dp(t){for(var e=1,s=t.length,r=t[0],n=t[0],i=1;i<s;++i,n=r)if(n=r,r=t[i],r!==n){if(i===e){e++;continue}t[e++]=r}return t.length=e,t}function mp(t,e,s){return t.length===0?t:e?(s||t.sort(e),hp(t,e)):(s||t.sort(),dp(t))}da.exports=mp});var we=y((ir,ya)=>{"use strict";ir.__esModule=!0;var ma=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function yp(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var gp=function t(e,s){if((typeof e>"u"?"undefined":ma(e))!=="object")return e;var r=new e.constructor;for(var n in e)if(e.hasOwnProperty(n)){var i=e[n],o=typeof i>"u"?"undefined":ma(i);n==="parent"&&o==="object"?s&&(r[n]=s):i instanceof Array?r[n]=i.map(function(a){return t(a,r)}):r[n]=t(i,r)}return r},wp=function(){function t(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};yp(this,t);for(var s in e)this[s]=e[s];var r=e.spaces;r=r===void 0?{}:r;var n=r.before,i=n===void 0?"":n,o=r.after,a=o===void 0?"":o;this.spaces={before:i,after:a}}return t.prototype.remove=function(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this},t.prototype.replaceWith=function(){if(this.parent){for(var s in arguments)this.parent.insertBefore(this,arguments[s]);this.remove()}return this},t.prototype.next=function(){return this.parent.at(this.parent.index(this)+1)},t.prototype.prev=function(){return this.parent.at(this.parent.index(this)-1)},t.prototype.clone=function(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=gp(this);for(var n in s)r[n]=s[n];return r},t.prototype.toString=function(){return[this.spaces.before,String(this.value),this.spaces.after].join("")},t}();ir.default=wp;ya.exports=ir.default});var D=y(B=>{"use strict";B.__esModule=!0;var hx=B.TAG="tag",dx=B.STRING="string",mx=B.SELECTOR="selector",yx=B.ROOT="root",gx=B.PSEUDO="pseudo",wx=B.NESTING="nesting",vx=B.ID="id",xx=B.COMMENT="comment",_x=B.COMBINATOR="combinator",bx=B.CLASS="class",kx=B.ATTRIBUTE="attribute",Ex=B.UNIVERSAL="universal"});var ar=y((or,ga)=>{"use strict";or.__esModule=!0;var vp=function(){function t(e,s){for(var r=0;r<s.length;r++){var n=s[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(e,s,r){return s&&t(e.prototype,s),r&&t(e,r),e}}(),xp=we(),_p=Ep(xp),bp=D(),X=kp(bp);function kp(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e.default=t,e}function Ep(t){return t&&t.__esModule?t:{default:t}}function Sp(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Tp(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Cp(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Ap=function(t){Cp(e,t);function e(s){Sp(this,e);var r=Tp(this,t.call(this,s));return r.nodes||(r.nodes=[]),r}return e.prototype.append=function(r){return r.parent=this,this.nodes.push(r),this},e.prototype.prepend=function(r){return r.parent=this,this.nodes.unshift(r),this},e.prototype.at=function(r){return this.nodes[r]},e.prototype.index=function(r){return typeof r=="number"?r:this.nodes.indexOf(r)},e.prototype.removeChild=function(r){r=this.index(r),this.at(r).parent=void 0,this.nodes.splice(r,1);var n=void 0;for(var i in this.indexes)n=this.indexes[i],n>=r&&(this.indexes[i]=n-1);return this},e.prototype.removeAll=function(){for(var i=this.nodes,r=Array.isArray(i),n=0,i=r?i:i[Symbol.iterator]();;){var o;if(r){if(n>=i.length)break;o=i[n++]}else{if(n=i.next(),n.done)break;o=n.value}var a=o;a.parent=void 0}return this.nodes=[],this},e.prototype.empty=function(){return this.removeAll()},e.prototype.insertAfter=function(r,n){var i=this.index(r);this.nodes.splice(i+1,0,n);var o=void 0;for(var a in this.indexes)o=this.indexes[a],i<=o&&(this.indexes[a]=o+this.nodes.length);return this},e.prototype.insertBefore=function(r,n){var i=this.index(r);this.nodes.splice(i,0,n);var o=void 0;for(var a in this.indexes)o=this.indexes[a],i<=o&&(this.indexes[a]=o+this.nodes.length);return this},e.prototype.each=function(r){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach++;var n=this.lastEach;if(this.indexes[n]=0,!!this.length){for(var i=void 0,o=void 0;this.indexes[n]<this.length&&(i=this.indexes[n],o=r(this.at(i),i),o!==!1);)this.indexes[n]+=1;if(delete this.indexes[n],o===!1)return!1}},e.prototype.walk=function(r){return this.each(function(n,i){var o=r(n,i);if(o!==!1&&n.length&&(o=n.walk(r)),o===!1)return!1})},e.prototype.walkAttributes=function(r){var n=this;return this.walk(function(i){if(i.type===X.ATTRIBUTE)return r.call(n,i)})},e.prototype.walkClasses=function(r){var n=this;return this.walk(function(i){if(i.type===X.CLASS)return r.call(n,i)})},e.prototype.walkCombinators=function(r){var n=this;return this.walk(function(i){if(i.type===X.COMBINATOR)return r.call(n,i)})},e.prototype.walkComments=function(r){var n=this;return this.walk(function(i){if(i.type===X.COMMENT)return r.call(n,i)})},e.prototype.walkIds=function(r){var n=this;return this.walk(function(i){if(i.type===X.ID)return r.call(n,i)})},e.prototype.walkNesting=function(r){var n=this;return this.walk(function(i){if(i.type===X.NESTING)return r.call(n,i)})},e.prototype.walkPseudos=function(r){var n=this;return this.walk(function(i){if(i.type===X.PSEUDO)return r.call(n,i)})},e.prototype.walkTags=function(r){var n=this;return this.walk(function(i){if(i.type===X.TAG)return r.call(n,i)})},e.prototype.walkUniversals=function(r){var n=this;return this.walk(function(i){if(i.type===X.UNIVERSAL)return r.call(n,i)})},e.prototype.split=function(r){var n=this,i=[];return this.reduce(function(o,a,u){var c=r.call(n,a);return i.push(a),c?(o.push(i),i=[]):u===n.length-1&&o.push(i),o},[])},e.prototype.map=function(r){return this.nodes.map(r)},e.prototype.reduce=function(r,n){return this.nodes.reduce(r,n)},e.prototype.every=function(r){return this.nodes.every(r)},e.prototype.some=function(r){return this.nodes.some(r)},e.prototype.filter=function(r){return this.nodes.filter(r)},e.prototype.sort=function(r){return this.nodes.sort(r)},e.prototype.toString=function(){return this.map(String).join("")},vp(e,[{key:"first",get:function(){return this.at(0)}},{key:"last",get:function(){return this.at(this.length-1)}},{key:"length",get:function(){return this.nodes.length}}]),e}(_p.default);or.default=Ap;ga.exports=or.default});var va=y((ur,wa)=>{"use strict";ur.__esModule=!0;var Op=ar(),Np=Rp(Op),Pp=D();function Rp(t){return t&&t.__esModule?t:{default:t}}function Ip(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function qp(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Lp(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Dp=function(t){Lp(e,t);function e(s){Ip(this,e);var r=qp(this,t.call(this,s));return r.type=Pp.ROOT,r}return e.prototype.toString=function(){var r=this.reduce(function(n,i){var o=String(i);return o?n+o+",":""},"").slice(0,-1);return this.trailingComma?r+",":r},e}(Np.default);ur.default=Dp;wa.exports=ur.default});var _a=y((lr,xa)=>{"use strict";lr.__esModule=!0;var Mp=ar(),Bp=Fp(Mp),Up=D();function Fp(t){return t&&t.__esModule?t:{default:t}}function $p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Wp(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function zp(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Yp=function(t){zp(e,t);function e(s){$p(this,e);var r=Wp(this,t.call(this,s));return r.type=Up.SELECTOR,r}return e}(Bp.default);lr.default=Yp;xa.exports=lr.default});var De=y((cr,ba)=>{"use strict";cr.__esModule=!0;var Vp=function(){function t(e,s){for(var r=0;r<s.length;r++){var n=s[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(e,s,r){return s&&t(e.prototype,s),r&&t(e,r),e}}(),Gp=we(),Hp=Kp(Gp);function Kp(t){return t&&t.__esModule?t:{default:t}}function Qp(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function jp(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Jp(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Xp=function(t){Jp(e,t);function e(){return Qp(this,e),jp(this,t.apply(this,arguments))}return e.prototype.toString=function(){return[this.spaces.before,this.ns,String(this.value),this.spaces.after].join("")},Vp(e,[{key:"ns",get:function(){var r=this.namespace;return r?(typeof r=="string"?r:"")+"|":""}}]),e}(Hp.default);cr.default=Xp;ba.exports=cr.default});var Ea=y((fr,ka)=>{"use strict";fr.__esModule=!0;var Zp=De(),eh=rh(Zp),th=D();function rh(t){return t&&t.__esModule?t:{default:t}}function sh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function nh(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function ih(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var oh=function(t){ih(e,t);function e(s){sh(this,e);var r=nh(this,t.call(this,s));return r.type=th.CLASS,r}return e.prototype.toString=function(){return[this.spaces.before,this.ns,"."+this.value,this.spaces.after].join("")},e}(eh.default);fr.default=oh;ka.exports=fr.default});var Ta=y((pr,Sa)=>{"use strict";pr.__esModule=!0;var ah=we(),uh=ch(ah),lh=D();function ch(t){return t&&t.__esModule?t:{default:t}}function fh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ph(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function hh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var dh=function(t){hh(e,t);function e(s){fh(this,e);var r=ph(this,t.call(this,s));return r.type=lh.COMMENT,r}return e}(uh.default);pr.default=dh;Sa.exports=pr.default});var Aa=y((hr,Ca)=>{"use strict";hr.__esModule=!0;var mh=De(),yh=wh(mh),gh=D();function wh(t){return t&&t.__esModule?t:{default:t}}function vh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function xh(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function _h(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var bh=function(t){_h(e,t);function e(s){vh(this,e);var r=xh(this,t.call(this,s));return r.type=gh.ID,r}return e.prototype.toString=function(){return[this.spaces.before,this.ns,"#"+this.value,this.spaces.after].join("")},e}(yh.default);hr.default=bh;Ca.exports=hr.default});var Na=y((dr,Oa)=>{"use strict";dr.__esModule=!0;var kh=De(),Eh=Th(kh),Sh=D();function Th(t){return t&&t.__esModule?t:{default:t}}function Ch(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ah(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Oh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Nh=function(t){Oh(e,t);function e(s){Ch(this,e);var r=Ah(this,t.call(this,s));return r.type=Sh.TAG,r}return e}(Eh.default);dr.default=Nh;Oa.exports=dr.default});var Ra=y((mr,Pa)=>{"use strict";mr.__esModule=!0;var Ph=we(),Rh=qh(Ph),Ih=D();function qh(t){return t&&t.__esModule?t:{default:t}}function Lh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Dh(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Mh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Bh=function(t){Mh(e,t);function e(s){Lh(this,e);var r=Dh(this,t.call(this,s));return r.type=Ih.STRING,r}return e}(Rh.default);mr.default=Bh;Pa.exports=mr.default});var qa=y((yr,Ia)=>{"use strict";yr.__esModule=!0;var Uh=ar(),Fh=Wh(Uh),$h=D();function Wh(t){return t&&t.__esModule?t:{default:t}}function zh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Yh(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Vh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Gh=function(t){Vh(e,t);function e(s){zh(this,e);var r=Yh(this,t.call(this,s));return r.type=$h.PSEUDO,r}return e.prototype.toString=function(){var r=this.length?"("+this.map(String).join(",")+")":"";return[this.spaces.before,String(this.value),r,this.spaces.after].join("")},e}(Fh.default);yr.default=Gh;Ia.exports=yr.default});var Da=y((gr,La)=>{"use strict";gr.__esModule=!0;var Hh=De(),Kh=jh(Hh),Qh=D();function jh(t){return t&&t.__esModule?t:{default:t}}function Jh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Xh(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Zh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var ed=function(t){Zh(e,t);function e(s){Jh(this,e);var r=Xh(this,t.call(this,s));return r.type=Qh.ATTRIBUTE,r.raws={},r}return e.prototype.toString=function(){var r=[this.spaces.before,"[",this.ns,this.attribute];return this.operator&&r.push(this.operator),this.value&&r.push(this.value),this.raws.insensitive?r.push(this.raws.insensitive):this.insensitive&&r.push(" i"),r.push("]"),r.concat(this.spaces.after).join("")},e}(Kh.default);gr.default=ed;La.exports=gr.default});var Ba=y((wr,Ma)=>{"use strict";wr.__esModule=!0;var td=De(),rd=nd(td),sd=D();function nd(t){return t&&t.__esModule?t:{default:t}}function id(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function od(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function ad(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var ud=function(t){ad(e,t);function e(s){id(this,e);var r=od(this,t.call(this,s));return r.type=sd.UNIVERSAL,r.value="*",r}return e}(rd.default);wr.default=ud;Ma.exports=wr.default});var Fa=y((vr,Ua)=>{"use strict";vr.__esModule=!0;var ld=we(),cd=pd(ld),fd=D();function pd(t){return t&&t.__esModule?t:{default:t}}function hd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function dd(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function md(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var yd=function(t){md(e,t);function e(s){hd(this,e);var r=dd(this,t.call(this,s));return r.type=fd.COMBINATOR,r}return e}(cd.default);vr.default=yd;Ua.exports=vr.default});var Wa=y((xr,$a)=>{"use strict";xr.__esModule=!0;var gd=we(),wd=xd(gd),vd=D();function xd(t){return t&&t.__esModule?t:{default:t}}function _d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function bd(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function kd(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Ed=function(t){kd(e,t);function e(s){_d(this,e);var r=bd(this,t.call(this,s));return r.type=vd.NESTING,r.value="&",r}return e}(wd.default);xr.default=Ed;$a.exports=xr.default});var Ya=y((_r,za)=>{"use strict";_r.__esModule=!0;_r.default=Sd;function Sd(t){return t.sort(function(e,s){return e-s})}za.exports=_r.default});var Za=y((Er,Xa)=>{"use strict";Er.__esModule=!0;Er.default=Dd;var Va=39,Td=34,Us=92,Ga=47,_t=10,Fs=32,$s=12,Ws=9,zs=13,Ha=43,Ka=62,Qa=126,ja=124,Cd=44,Ad=40,Od=41,Nd=91,Pd=93,Rd=59,Ja=42,Id=58,qd=38,Ld=64,br=/[ \n\t\r\{\(\)'"\\;/]/g,kr=/[ \n\t\r\(\)\*:;@!&'"\+\|~>,\[\]\\]|\/(?=\*)/g;function Dd(t){for(var e=[],s=t.css.valueOf(),r=void 0,n=void 0,i=void 0,o=void 0,a=void 0,u=void 0,c=void 0,f=void 0,p=void 0,l=void 0,g=void 0,x=s.length,h=-1,d=1,m=0,_=function(v,R){if(t.safe)s+=R,n=s.length-1;else throw t.error("Unclosed "+v,d,m-h,m)};m<x;){switch(r=s.charCodeAt(m),r===_t&&(h=m,d+=1),r){case _t:case Fs:case Ws:case zs:case $s:n=m;do n+=1,r=s.charCodeAt(n),r===_t&&(h=n,d+=1);while(r===Fs||r===_t||r===Ws||r===zs||r===$s);e.push(["space",s.slice(m,n),d,m-h,m]),m=n-1;break;case Ha:case Ka:case Qa:case ja:n=m;do n+=1,r=s.charCodeAt(n);while(r===Ha||r===Ka||r===Qa||r===ja);e.push(["combinator",s.slice(m,n),d,m-h,m]),m=n-1;break;case Ja:e.push(["*","*",d,m-h,m]);break;case qd:e.push(["&","&",d,m-h,m]);break;case Cd:e.push([",",",",d,m-h,m]);break;case Nd:e.push(["[","[",d,m-h,m]);break;case Pd:e.push(["]","]",d,m-h,m]);break;case Id:e.push([":",":",d,m-h,m]);break;case Rd:e.push([";",";",d,m-h,m]);break;case Ad:e.push(["(","(",d,m-h,m]);break;case Od:e.push([")",")",d,m-h,m]);break;case Va:case Td:i=r===Va?"'":'"',n=m;do for(l=!1,n=s.indexOf(i,n+1),n===-1&&_("quote",i),g=n;s.charCodeAt(g-1)===Us;)g-=1,l=!l;while(l);e.push(["string",s.slice(m,n+1),d,m-h,d,n-h,m]),m=n;break;case Ld:br.lastIndex=m+1,br.test(s),br.lastIndex===0?n=s.length-1:n=br.lastIndex-2,e.push(["at-word",s.slice(m,n+1),d,m-h,d,n-h,m]),m=n;break;case Us:for(n=m,c=!0;s.charCodeAt(n+1)===Us;)n+=1,c=!c;r=s.charCodeAt(n+1),c&&r!==Ga&&r!==Fs&&r!==_t&&r!==Ws&&r!==zs&&r!==$s&&(n+=1),e.push(["word",s.slice(m,n+1),d,m-h,d,n-h,m]),m=n;break;default:r===Ga&&s.charCodeAt(m+1)===Ja?(n=s.indexOf("*/",m+2)+1,n===0&&_("comment","*/"),u=s.slice(m,n+1),o=u.split(`
`),a=o.length-1,a>0?(f=d+a,p=n-o[a].length):(f=d,p=h),e.push(["comment",u,d,m-h,f,n-p,m]),h=p,d=f,m=n):(kr.lastIndex=m+1,kr.test(s),kr.lastIndex===0?n=s.length-1:n=kr.lastIndex-2,e.push(["word",s.slice(m,n+1),d,m-h,d,n-h,m]),m=n);break}m++}return e}Xa.exports=Er.default});var ru=y((Sr,tu)=>{"use strict";Sr.__esModule=!0;var Md=function(){function t(e,s){for(var r=0;r<s.length;r++){var n=s[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(e,s,r){return s&&t(e.prototype,s),r&&t(e,r),e}}(),Bd=Ds(),Ud=I(Bd),Fd=Ms(),Ys=I(Fd),$d=Bs(),Wd=I($d),zd=va(),Yd=I(zd),Vd=_a(),Vs=I(Vd),Gd=Ea(),Hd=I(Gd),Kd=Ta(),Qd=I(Kd),jd=Aa(),Jd=I(jd),Xd=Na(),Zd=I(Xd),em=Ra(),tm=I(em),rm=qa(),sm=I(rm),nm=Da(),im=I(nm),om=Ba(),am=I(om),um=Fa(),lm=I(um),cm=Wa(),fm=I(cm),pm=Ya(),hm=I(pm),dm=Za(),eu=I(dm),mm=D(),ym=gm(mm);function gm(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e.default=t,e}function I(t){return t&&t.__esModule?t:{default:t}}function wm(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var vm=function(){function t(e){wm(this,t),this.input=e,this.lossy=e.options.lossless===!1,this.position=0,this.root=new Yd.default;var s=new Vs.default;return this.root.append(s),this.current=s,this.lossy?this.tokens=(0,eu.default)({safe:e.safe,css:e.css.trim()}):this.tokens=(0,eu.default)(e),this.loop()}return t.prototype.attribute=function(){var s="",r=void 0,n=this.currToken;for(this.position++;this.position<this.tokens.length&&this.currToken[0]!=="]";)s+=this.tokens[this.position][1],this.position++;this.position===this.tokens.length&&!~s.indexOf("]")&&this.error("Expected a closing square bracket.");var i=s.split(/((?:[*~^$|]?=))([^]*)/),o=i[0].split(/(\|)/g),a={operator:i[1],value:i[2],source:{start:{line:n[2],column:n[3]},end:{line:this.currToken[2],column:this.currToken[3]}},sourceIndex:n[4]};if(o.length>1?(o[0]===""&&(o[0]=!0),a.attribute=this.parseValue(o[2]),a.namespace=this.parseNamespace(o[0])):a.attribute=this.parseValue(i[0]),r=new im.default(a),i[2]){var u=i[2].split(/(\s+i\s*?)$/),c=u[0].trim();r.value=this.lossy?c:u[0],u[1]&&(r.insensitive=!0,this.lossy||(r.raws.insensitive=u[1])),r.quoted=c[0]==="'"||c[0]==='"',r.raws.unquoted=r.quoted?c.slice(1,-1):c}this.newNode(r),this.position++},t.prototype.combinator=function(){if(this.currToken[1]==="|")return this.namespace();for(var s=new lm.default({value:"",source:{start:{line:this.currToken[2],column:this.currToken[3]},end:{line:this.currToken[2],column:this.currToken[3]}},sourceIndex:this.currToken[4]});this.position<this.tokens.length&&this.currToken&&(this.currToken[0]==="space"||this.currToken[0]==="combinator");)this.nextToken&&this.nextToken[0]==="combinator"?(s.spaces.before=this.parseSpace(this.currToken[1]),s.source.start.line=this.nextToken[2],s.source.start.column=this.nextToken[3],s.source.end.column=this.nextToken[3],s.source.end.line=this.nextToken[2],s.sourceIndex=this.nextToken[4]):this.prevToken&&this.prevToken[0]==="combinator"?s.spaces.after=this.parseSpace(this.currToken[1]):this.currToken[0]==="combinator"?s.value=this.currToken[1]:this.currToken[0]==="space"&&(s.value=this.parseSpace(this.currToken[1]," ")),this.position++;return this.newNode(s)},t.prototype.comma=function(){if(this.position===this.tokens.length-1){this.root.trailingComma=!0,this.position++;return}var s=new Vs.default;this.current.parent.append(s),this.current=s,this.position++},t.prototype.comment=function(){var s=new Qd.default({value:this.currToken[1],source:{start:{line:this.currToken[2],column:this.currToken[3]},end:{line:this.currToken[4],column:this.currToken[5]}},sourceIndex:this.currToken[6]});this.newNode(s),this.position++},t.prototype.error=function(s){throw new this.input.error(s)},t.prototype.missingBackslash=function(){return this.error("Expected a backslash preceding the semicolon.")},t.prototype.missingParenthesis=function(){return this.error("Expected opening parenthesis.")},t.prototype.missingSquareBracket=function(){return this.error("Expected opening square bracket.")},t.prototype.namespace=function(){var s=this.prevToken&&this.prevToken[1]||!0;if(this.nextToken[0]==="word")return this.position++,this.word(s);if(this.nextToken[0]==="*")return this.position++,this.universal(s)},t.prototype.nesting=function(){this.newNode(new fm.default({value:this.currToken[1],source:{start:{line:this.currToken[2],column:this.currToken[3]},end:{line:this.currToken[2],column:this.currToken[3]}},sourceIndex:this.currToken[4]})),this.position++},t.prototype.parentheses=function(){var s=this.current.last;if(s&&s.type===ym.PSEUDO){var r=new Vs.default,n=this.current;s.append(r),this.current=r;var i=1;for(this.position++;this.position<this.tokens.length&&i;)this.currToken[0]==="("&&i++,this.currToken[0]===")"&&i--,i?this.parse():(r.parent.source.end.line=this.currToken[2],r.parent.source.end.column=this.currToken[3],this.position++);i&&this.error("Expected closing parenthesis."),this.current=n}else{var o=1;for(this.position++,s.value+="(";this.position<this.tokens.length&&o;)this.currToken[0]==="("&&o++,this.currToken[0]===")"&&o--,s.value+=this.parseParenthesisToken(this.currToken),this.position++;o&&this.error("Expected closing parenthesis.")}},t.prototype.pseudo=function(){for(var s=this,r="",n=this.currToken;this.currToken&&this.currToken[0]===":";)r+=this.currToken[1],this.position++;if(!this.currToken)return this.error("Expected pseudo-class or pseudo-element");if(this.currToken[0]==="word"){var i=void 0;this.splitWord(!1,function(o,a){r+=o,i=new sm.default({value:r,source:{start:{line:n[2],column:n[3]},end:{line:s.currToken[4],column:s.currToken[5]}},sourceIndex:n[4]}),s.newNode(i),a>1&&s.nextToken&&s.nextToken[0]==="("&&s.error("Misplaced parenthesis.")})}else this.error('Unexpected "'+this.currToken[0]+'" found.')},t.prototype.space=function(){var s=this.currToken;this.position===0||this.prevToken[0]===","||this.prevToken[0]==="("?(this.spaces=this.parseSpace(s[1]),this.position++):this.position===this.tokens.length-1||this.nextToken[0]===","||this.nextToken[0]===")"?(this.current.last.spaces.after=this.parseSpace(s[1]),this.position++):this.combinator()},t.prototype.string=function(){var s=this.currToken;this.newNode(new tm.default({value:this.currToken[1],source:{start:{line:s[2],column:s[3]},end:{line:s[4],column:s[5]}},sourceIndex:s[6]})),this.position++},t.prototype.universal=function(s){var r=this.nextToken;if(r&&r[1]==="|")return this.position++,this.namespace();this.newNode(new am.default({value:this.currToken[1],source:{start:{line:this.currToken[2],column:this.currToken[3]},end:{line:this.currToken[2],column:this.currToken[3]}},sourceIndex:this.currToken[4]}),s),this.position++},t.prototype.splitWord=function(s,r){for(var n=this,i=this.nextToken,o=this.currToken[1];i&&i[0]==="word";){this.position++;var a=this.currToken[1];if(o+=a,a.lastIndexOf("\\")===a.length-1){var u=this.nextToken;u&&u[0]==="space"&&(o+=this.parseSpace(u[1]," "),this.position++)}i=this.nextToken}var c=(0,Ys.default)(o,"."),f=(0,Ys.default)(o,"#"),p=(0,Ys.default)(o,"#{");p.length&&(f=f.filter(function(g){return!~p.indexOf(g)}));var l=(0,hm.default)((0,Wd.default)((0,Ud.default)([[0],c,f])));l.forEach(function(g,x){var h=l[x+1]||o.length,d=o.slice(g,h);if(x===0&&r)return r.call(n,d,l.length);var m=void 0;~c.indexOf(g)?m=new Hd.default({value:d.slice(1),source:{start:{line:n.currToken[2],column:n.currToken[3]+g},end:{line:n.currToken[4],column:n.currToken[3]+(h-1)}},sourceIndex:n.currToken[6]+l[x]}):~f.indexOf(g)?m=new Jd.default({value:d.slice(1),source:{start:{line:n.currToken[2],column:n.currToken[3]+g},end:{line:n.currToken[4],column:n.currToken[3]+(h-1)}},sourceIndex:n.currToken[6]+l[x]}):m=new Zd.default({value:d,source:{start:{line:n.currToken[2],column:n.currToken[3]+g},end:{line:n.currToken[4],column:n.currToken[3]+(h-1)}},sourceIndex:n.currToken[6]+l[x]}),n.newNode(m,s)}),this.position++},t.prototype.word=function(s){var r=this.nextToken;return r&&r[1]==="|"?(this.position++,this.namespace()):this.splitWord(s)},t.prototype.loop=function(){for(;this.position<this.tokens.length;)this.parse(!0);return this.root},t.prototype.parse=function(s){switch(this.currToken[0]){case"space":this.space();break;case"comment":this.comment();break;case"(":this.parentheses();break;case")":s&&this.missingParenthesis();break;case"[":this.attribute();break;case"]":this.missingSquareBracket();break;case"at-word":case"word":this.word();break;case":":this.pseudo();break;case";":this.missingBackslash();break;case",":this.comma();break;case"*":this.universal();break;case"&":this.nesting();break;case"combinator":this.combinator();break;case"string":this.string();break}},t.prototype.parseNamespace=function(s){if(this.lossy&&typeof s=="string"){var r=s.trim();return r.length?r:!0}return s},t.prototype.parseSpace=function(s,r){return this.lossy?r||"":s},t.prototype.parseValue=function(s){return this.lossy&&s&&typeof s=="string"?s.trim():s},t.prototype.parseParenthesisToken=function(s){return this.lossy?s[0]==="space"?this.parseSpace(s[1]," "):this.parseValue(s[1]):s[1]},t.prototype.newNode=function(s,r){return r&&(s.namespace=this.parseNamespace(r)),this.spaces&&(s.spaces.before=this.spaces,this.spaces=""),this.current.append(s)},Md(t,[{key:"currToken",get:function(){return this.tokens[this.position]}},{key:"nextToken",get:function(){return this.tokens[this.position+1]}},{key:"prevToken",get:function(){return this.tokens[this.position-1]}}]),t}();Sr.default=vm;tu.exports=Sr.default});var nu=y((Tr,su)=>{"use strict";Tr.__esModule=!0;var xm=function(){function t(e,s){for(var r=0;r<s.length;r++){var n=s[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(e,s,r){return s&&t(e.prototype,s),r&&t(e,r),e}}(),_m=ru(),bm=km(_m);function km(t){return t&&t.__esModule?t:{default:t}}function Em(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var Sm=function(){function t(e){return Em(this,t),this.func=e||function(){},this}return t.prototype.process=function(s){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=new bm.default({css:s,error:function(o){throw new Error(o)},options:r});return this.res=n,this.func(n),this},xm(t,[{key:"result",get:function(){return String(this.res)}}]),t}();Tr.default=Sm;su.exports=Tr.default});var Y=y((Ox,ou)=>{"use strict";var Gs=function(t,e){let s=new t.constructor;for(let r in t){if(!t.hasOwnProperty(r))continue;let n=t[r],i=typeof n;r==="parent"&&i==="object"?e&&(s[r]=e):r==="source"?s[r]=n:n instanceof Array?s[r]=n.map(o=>Gs(o,s)):r!=="before"&&r!=="after"&&r!=="between"&&r!=="semicolon"&&(i==="object"&&n!==null&&(n=Gs(n)),s[r]=n)}return s};ou.exports=class{constructor(e){e=e||{},this.raws={before:"",after:""};for(let s in e)this[s]=e[s]}remove(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this}toString(){return[this.raws.before,String(this.value),this.raws.after].join("")}clone(e){e=e||{};let s=Gs(this);for(let r in e)s[r]=e[r];return s}cloneBefore(e){e=e||{};let s=this.clone(e);return this.parent.insertBefore(this,s),s}cloneAfter(e){e=e||{};let s=this.clone(e);return this.parent.insertAfter(this,s),s}replaceWith(){let e=Array.prototype.slice.call(arguments);if(this.parent){for(let s of e)this.parent.insertBefore(this,s);this.remove()}return this}moveTo(e){return this.cleanRaws(this.root()===e.root()),this.remove(),e.append(this),this}moveBefore(e){return this.cleanRaws(this.root()===e.root()),this.remove(),e.parent.insertBefore(e,this),this}moveAfter(e){return this.cleanRaws(this.root()===e.root()),this.remove(),e.parent.insertAfter(e,this),this}next(){let e=this.parent.index(this);return this.parent.nodes[e+1]}prev(){let e=this.parent.index(this);return this.parent.nodes[e-1]}toJSON(){let e={};for(let s in this){if(!this.hasOwnProperty(s)||s==="parent")continue;let r=this[s];r instanceof Array?e[s]=r.map(n=>typeof n=="object"&&n.toJSON?n.toJSON():n):typeof r=="object"&&r.toJSON?e[s]=r.toJSON():e[s]=r}return e}root(){let e=this;for(;e.parent;)e=e.parent;return e}cleanRaws(e){delete this.raws.before,delete this.raws.after,e||delete this.raws.between}positionInside(e){let s=this.toString(),r=this.source.start.column,n=this.source.start.line;for(let i=0;i<e;i++)s[i]===`
`?(r=1,n+=1):r+=1;return{line:n,column:r}}positionBy(e){let s=this.source.start;if(Object(e).index)s=this.positionInside(e.index);else if(Object(e).word){let r=this.toString().indexOf(e.word);r!==-1&&(s=this.positionInside(r))}return s}}});var U=y((Nx,au)=>{"use strict";var Cm=Y(),Me=class extends Cm{constructor(e){super(e),this.nodes||(this.nodes=[])}push(e){return e.parent=this,this.nodes.push(e),this}each(e){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach+=1;let s=this.lastEach,r,n;if(this.indexes[s]=0,!!this.nodes){for(;this.indexes[s]<this.nodes.length&&(r=this.indexes[s],n=e(this.nodes[r],r),n!==!1);)this.indexes[s]+=1;return delete this.indexes[s],n}}walk(e){return this.each((s,r)=>{let n=e(s,r);return n!==!1&&s.walk&&(n=s.walk(e)),n})}walkType(e,s){if(!e||!s)throw new Error("Parameters {type} and {callback} are required.");let r=typeof e=="function";return this.walk((n,i)=>{if(r&&n instanceof e||!r&&n.type===e)return s.call(this,n,i)})}append(e){return e.parent=this,this.nodes.push(e),this}prepend(e){return e.parent=this,this.nodes.unshift(e),this}cleanRaws(e){if(super.cleanRaws(e),this.nodes)for(let s of this.nodes)s.cleanRaws(e)}insertAfter(e,s){let r=this.index(e),n;this.nodes.splice(r+1,0,s);for(let i in this.indexes)n=this.indexes[i],r<=n&&(this.indexes[i]=n+this.nodes.length);return this}insertBefore(e,s){let r=this.index(e),n;this.nodes.splice(r,0,s);for(let i in this.indexes)n=this.indexes[i],r<=n&&(this.indexes[i]=n+this.nodes.length);return this}removeChild(e){e=this.index(e),this.nodes[e].parent=void 0,this.nodes.splice(e,1);let s;for(let r in this.indexes)s=this.indexes[r],s>=e&&(this.indexes[r]=s-1);return this}removeAll(){for(let e of this.nodes)e.parent=void 0;return this.nodes=[],this}every(e){return this.nodes.every(e)}some(e){return this.nodes.some(e)}index(e){return typeof e=="number"?e:this.nodes.indexOf(e)}get first(){if(this.nodes)return this.nodes[0]}get last(){if(this.nodes)return this.nodes[this.nodes.length-1]}toString(){let e=this.nodes.map(String).join("");return this.value&&(e=this.value+e),this.raws.before&&(e=this.raws.before+e),this.raws.after&&(e+=this.raws.after),e}};Me.registerWalker=t=>{let e="walk"+t.name;e.lastIndexOf("s")!==e.length-1&&(e+="s"),!Me.prototype[e]&&(Me.prototype[e]=function(s){return this.walkType(t,s)})};au.exports=Me});var lu=y((Rx,uu)=>{"use strict";var Am=U();uu.exports=class extends Am{constructor(e){super(e),this.type="root"}}});var fu=y((qx,cu)=>{"use strict";var Om=U();cu.exports=class extends Om{constructor(e){super(e),this.type="value",this.unbalanced=0}}});var du=y((Lx,hu)=>{"use strict";var pu=U(),Cr=class extends pu{constructor(e){super(e),this.type="atword"}toString(){let e=this.quoted?this.raws.quote:"";return[this.raws.before,"@",String.prototype.toString.call(this.value),this.raws.after].join("")}};pu.registerWalker(Cr);hu.exports=Cr});var yu=y((Dx,mu)=>{"use strict";var Nm=U(),Pm=Y(),Ar=class extends Pm{constructor(e){super(e),this.type="colon"}};Nm.registerWalker(Ar);mu.exports=Ar});var wu=y((Mx,gu)=>{"use strict";var Rm=U(),Im=Y(),Or=class extends Im{constructor(e){super(e),this.type="comma"}};Rm.registerWalker(Or);gu.exports=Or});var xu=y((Bx,vu)=>{"use strict";var qm=U(),Lm=Y(),Nr=class extends Lm{constructor(e){super(e),this.type="comment",this.inline=Object(e).inline||!1}toString(){return[this.raws.before,this.inline?"//":"/*",String(this.value),this.inline?"":"*/",this.raws.after].join("")}};qm.registerWalker(Nr);vu.exports=Nr});var ku=y((Ux,bu)=>{"use strict";var _u=U(),Pr=class extends _u{constructor(e){super(e),this.type="func",this.unbalanced=-1}};_u.registerWalker(Pr);bu.exports=Pr});var Su=y((Fx,Eu)=>{"use strict";var Dm=U(),Mm=Y(),Rr=class extends Mm{constructor(e){super(e),this.type="number",this.unit=Object(e).unit||""}toString(){return[this.raws.before,String(this.value),this.unit,this.raws.after].join("")}};Dm.registerWalker(Rr);Eu.exports=Rr});var Cu=y(($x,Tu)=>{"use strict";var Bm=U(),Um=Y(),Ir=class extends Um{constructor(e){super(e),this.type="operator"}};Bm.registerWalker(Ir);Tu.exports=Ir});var Ou=y((Wx,Au)=>{"use strict";var Fm=U(),$m=Y(),qr=class extends $m{constructor(e){super(e),this.type="paren",this.parenType=""}};Fm.registerWalker(qr);Au.exports=qr});var Pu=y((zx,Nu)=>{"use strict";var Wm=U(),zm=Y(),Lr=class extends zm{constructor(e){super(e),this.type="string"}toString(){let e=this.quoted?this.raws.quote:"";return[this.raws.before,e,this.value+"",e,this.raws.after].join("")}};Wm.registerWalker(Lr);Nu.exports=Lr});var Iu=y((Yx,Ru)=>{"use strict";var Ym=U(),Vm=Y(),Dr=class extends Vm{constructor(e){super(e),this.type="word"}};Ym.registerWalker(Dr);Ru.exports=Dr});var Lu=y((Vx,qu)=>{"use strict";var Gm=U(),Hm=Y(),Mr=class extends Hm{constructor(e){super(e),this.type="unicode-range"}};Gm.registerWalker(Mr);qu.exports=Mr});var Mu=y((Gx,Du)=>{"use strict";var Hs=class extends Error{constructor(e){super(e),this.name=this.constructor.name,this.message=e||"An error ocurred while tokzenizing.",typeof Error.captureStackTrace=="function"?Error.captureStackTrace(this,this.constructor):this.stack=new Error(e).stack}};Du.exports=Hs});var Fu=y((Hx,Uu)=>{"use strict";var Br=/[ \n\t\r\{\(\)'"\\;,/]/g,Km=/[ \n\t\r\(\)\{\}\*:;@!&'"\+\|~>,\[\]\\]|\/(?=\*)/g,Be=/[ \n\t\r\(\)\{\}\*:;@!&'"\-\+\|~>,\[\]\\]|\//g,Qm=/^[a-z0-9]/i,jm=/^[a-f0-9?\-]/i,Bu=Mu();Uu.exports=function(e,s){s=s||{};let r=[],n=e.valueOf(),i=n.length,o=-1,a=1,u=0,c=0,f=null,p,l,g,x,h,d,m,_,w,v,R,F;function K(T){let C=`Unclosed ${T} at line: ${a}, column: ${u-o}, token: ${u}`;throw new Bu(C)}function $(){let T=`Syntax error at line: ${a}, column: ${u-o}, token: ${u}`;throw new Bu(T)}for(;u<i;){switch(p=n.charCodeAt(u),p===10&&(o=u,a+=1),p){case 10:case 32:case 9:case 13:case 12:l=u;do l+=1,p=n.charCodeAt(l),p===10&&(o=l,a+=1);while(p===32||p===10||p===9||p===13||p===12);r.push(["space",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1;break;case 58:l=u+1,r.push(["colon",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1;break;case 44:l=u+1,r.push(["comma",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1;break;case 123:r.push(["{","{",a,u-o,a,l-o,u]);break;case 125:r.push(["}","}",a,u-o,a,l-o,u]);break;case 40:c++,f=!f&&c===1&&r.length>0&&r[r.length-1][0]==="word"&&r[r.length-1][1]==="url",r.push(["(","(",a,u-o,a,l-o,u]);break;case 41:c--,f=f&&c>0,r.push([")",")",a,u-o,a,l-o,u]);break;case 39:case 34:g=p===39?"'":'"',l=u;do for(v=!1,l=n.indexOf(g,l+1),l===-1&&K("quote",g),R=l;n.charCodeAt(R-1)===92;)R-=1,v=!v;while(v);r.push(["string",n.slice(u,l+1),a,u-o,a,l-o,u]),u=l;break;case 64:Br.lastIndex=u+1,Br.test(n),Br.lastIndex===0?l=n.length-1:l=Br.lastIndex-2,r.push(["atword",n.slice(u,l+1),a,u-o,a,l-o,u]),u=l;break;case 92:l=u,p=n.charCodeAt(l+1),m&&p!==47&&p!==32&&p!==10&&p!==9&&p!==13&&p!==12&&(l+=1),r.push(["word",n.slice(u,l+1),a,u-o,a,l-o,u]),u=l;break;case 43:case 45:case 42:l=u+1,F=n.slice(u+1,l+1);let T=n.slice(u-1,u);if(p===45&&F.charCodeAt(0)===45){l++,r.push(["word",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1;break}r.push(["operator",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1;break;default:if(p===47&&(n.charCodeAt(u+1)===42||s.loose&&!f&&n.charCodeAt(u+1)===47)){if(n.charCodeAt(u+1)===42)l=n.indexOf("*/",u+2)+1,l===0&&K("comment","*/");else{let A=n.indexOf(`
`,u+2);l=A!==-1?A-1:i}d=n.slice(u,l+1),x=d.split(`
`),h=x.length-1,h>0?(_=a+h,w=l-x[h].length):(_=a,w=o),r.push(["comment",d,a,u-o,_,l-w,u]),o=w,a=_,u=l}else if(p===35&&!Qm.test(n.slice(u+1,u+2)))l=u+1,r.push(["#",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1;else if((p===117||p===85)&&n.charCodeAt(u+1)===43){l=u+2;do l+=1,p=n.charCodeAt(l);while(l<i&&jm.test(n.slice(l,l+1)));r.push(["unicoderange",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1}else if(p===47)l=u+1,r.push(["operator",n.slice(u,l),a,u-o,a,l-o,u]),u=l-1;else{let C=Km;if(p>=48&&p<=57&&(C=Be),C.lastIndex=u+1,C.test(n),C.lastIndex===0?l=n.length-1:l=C.lastIndex-2,C===Be||p===46){let A=n.charCodeAt(l),ve=n.charCodeAt(l+1),Zs=n.charCodeAt(l+2);(A===101||A===69)&&(ve===45||ve===43)&&Zs>=48&&Zs<=57&&(Be.lastIndex=l+2,Be.test(n),Be.lastIndex===0?l=n.length-1:l=Be.lastIndex-2)}r.push(["word",n.slice(u,l+1),a,u-o,a,l-o,u]),u=l}break}u++}return r}});var Wu=y((Kx,$u)=>{"use strict";var Ks=class extends Error{constructor(e){super(e),this.name=this.constructor.name,this.message=e||"An error ocurred while parsing.",typeof Error.captureStackTrace=="function"?Error.captureStackTrace(this,this.constructor):this.stack=new Error(e).stack}};$u.exports=Ks});var Gu=y((jx,Vu)=>{"use strict";var Jm=lu(),Xm=fu(),Zm=du(),ey=yu(),ty=wu(),ry=xu(),sy=ku(),ny=Su(),iy=Cu(),zu=Ou(),oy=Pu(),Yu=Iu(),ay=Lu(),uy=Fu(),ly=Ds(),cy=Ms(),fy=Bs(),py=Wu();function hy(t){return t.sort((e,s)=>e-s)}Vu.exports=class{constructor(e,s){let r={loose:!1};this.cache=[],this.input=e,this.options=Object.assign({},r,s),this.position=0,this.unbalanced=0,this.root=new Jm;let n=new Xm;this.root.append(n),this.current=n,this.tokens=uy(e,this.options)}parse(){return this.loop()}colon(){let e=this.currToken;this.newNode(new ey({value:e[1],source:{start:{line:e[2],column:e[3]},end:{line:e[4],column:e[5]}},sourceIndex:e[6]})),this.position++}comma(){let e=this.currToken;this.newNode(new ty({value:e[1],source:{start:{line:e[2],column:e[3]},end:{line:e[4],column:e[5]}},sourceIndex:e[6]})),this.position++}comment(){let e=!1,s=this.currToken[1].replace(/\/\*|\*\//g,""),r;this.options.loose&&s.startsWith("//")&&(s=s.substring(2),e=!0),r=new ry({value:s,inline:e,source:{start:{line:this.currToken[2],column:this.currToken[3]},end:{line:this.currToken[4],column:this.currToken[5]}},sourceIndex:this.currToken[6]}),this.newNode(r),this.position++}error(e,s){throw new py(e+` at line: ${s[2]}, column ${s[3]}`)}loop(){for(;this.position<this.tokens.length;)this.parseTokens();return!this.current.last&&this.spaces?this.current.raws.before+=this.spaces:this.spaces&&(this.current.last.raws.after+=this.spaces),this.spaces="",this.root}operator(){let e=this.currToken[1],s;if(e==="+"||e==="-"){if(this.options.loose||this.position>0&&(this.current.type==="func"&&this.current.value==="calc"?this.prevToken[0]!=="space"&&this.prevToken[0]!=="("?this.error("Syntax Error",this.currToken):this.nextToken[0]!=="space"&&this.nextToken[0]!=="word"?this.error("Syntax Error",this.currToken):this.nextToken[0]==="word"&&this.current.last.type!=="operator"&&this.current.last.value!=="("&&this.error("Syntax Error",this.currToken):(this.nextToken[0]==="space"||this.nextToken[0]==="operator"||this.prevToken[0]==="operator")&&this.error("Syntax Error",this.currToken)),this.options.loose){if((!this.current.nodes.length||this.current.last&&this.current.last.type==="operator")&&this.nextToken[0]==="word")return this.word()}else if(this.nextToken[0]==="word")return this.word()}return s=new iy({value:this.currToken[1],source:{start:{line:this.currToken[2],column:this.currToken[3]},end:{line:this.currToken[2],column:this.currToken[3]}},sourceIndex:this.currToken[4]}),this.position++,this.newNode(s)}parseTokens(){switch(this.currToken[0]){case"space":this.space();break;case"colon":this.colon();break;case"comma":this.comma();break;case"comment":this.comment();break;case"(":this.parenOpen();break;case")":this.parenClose();break;case"atword":case"word":this.word();break;case"operator":this.operator();break;case"string":this.string();break;case"unicoderange":this.unicodeRange();break;default:this.word();break}}parenOpen(){let e=1,s=this.position+1,r=this.currToken,n;for(;s<this.tokens.length&&e;){let i=this.tokens[s];i[0]==="("&&e++,i[0]===")"&&e--,s++}if(e&&this.error("Expected closing parenthesis",r),n=this.current.last,n&&n.type==="func"&&n.unbalanced<0&&(n.unbalanced=0,this.current=n),this.current.unbalanced++,this.newNode(new zu({value:r[1],source:{start:{line:r[2],column:r[3]},end:{line:r[4],column:r[5]}},sourceIndex:r[6]})),this.position++,this.current.type==="func"&&this.current.unbalanced&&this.current.value==="url"&&this.currToken[0]!=="string"&&this.currToken[0]!==")"&&!this.options.loose){let i=this.nextToken,o=this.currToken[1],a={line:this.currToken[2],column:this.currToken[3]};for(;i&&i[0]!==")"&&this.current.unbalanced;)this.position++,o+=this.currToken[1],i=this.nextToken;this.position!==this.tokens.length-1&&(this.position++,this.newNode(new Yu({value:o,source:{start:a,end:{line:this.currToken[4],column:this.currToken[5]}},sourceIndex:this.currToken[6]})))}}parenClose(){let e=this.currToken;this.newNode(new zu({value:e[1],source:{start:{line:e[2],column:e[3]},end:{line:e[4],column:e[5]}},sourceIndex:e[6]})),this.position++,!(this.position>=this.tokens.length-1&&!this.current.unbalanced)&&(this.current.unbalanced--,this.current.unbalanced<0&&this.error("Expected opening parenthesis",e),!this.current.unbalanced&&this.cache.length&&(this.current=this.cache.pop()))}space(){let e=this.currToken;this.position===this.tokens.length-1||this.nextToken[0]===","||this.nextToken[0]===")"?(this.current.last.raws.after+=e[1],this.position++):(this.spaces=e[1],this.position++)}unicodeRange(){let e=this.currToken;this.newNode(new ay({value:e[1],source:{start:{line:e[2],column:e[3]},end:{line:e[4],column:e[5]}},sourceIndex:e[6]})),this.position++}splitWord(){let e=this.nextToken,s=this.currToken[1],r=/^[\+\-]?((\d+(\.\d*)?)|(\.\d+))([eE][\+\-]?\d+)?/,n=/^(?!\#([a-z0-9]+))[\#\{\}]/gi,i,o;if(!n.test(s))for(;e&&e[0]==="word";){this.position++;let a=this.currToken[1];s+=a,e=this.nextToken}i=cy(s,"@"),o=hy(fy(ly([[0],i]))),o.forEach((a,u)=>{let c=o[u+1]||s.length,f=s.slice(a,c),p;if(~i.indexOf(a))p=new Zm({value:f.slice(1),source:{start:{line:this.currToken[2],column:this.currToken[3]+a},end:{line:this.currToken[4],column:this.currToken[3]+(c-1)}},sourceIndex:this.currToken[6]+o[u]});else if(r.test(this.currToken[1])){let l=f.replace(r,"");p=new ny({value:f.replace(l,""),source:{start:{line:this.currToken[2],column:this.currToken[3]+a},end:{line:this.currToken[4],column:this.currToken[3]+(c-1)}},sourceIndex:this.currToken[6]+o[u],unit:l})}else p=new(e&&e[0]==="("?sy:Yu)({value:f,source:{start:{line:this.currToken[2],column:this.currToken[3]+a},end:{line:this.currToken[4],column:this.currToken[3]+(c-1)}},sourceIndex:this.currToken[6]+o[u]}),p.type==="word"?(p.isHex=/^#(.+)/.test(f),p.isColor=/^#([0-9a-f]{3}|[0-9a-f]{4}|[0-9a-f]{6}|[0-9a-f]{8})$/i.test(f)):this.cache.push(this.current);this.newNode(p)}),this.position++}string(){let e=this.currToken,s=this.currToken[1],r=/^(\"|\')/,n=r.test(s),i="",o;n&&(i=s.match(r)[0],s=s.slice(1,s.length-1)),o=new oy({value:s,source:{start:{line:e[2],column:e[3]},end:{line:e[4],column:e[5]}},sourceIndex:e[6],quoted:n}),o.raws.quote=i,this.newNode(o),this.position++}word(){return this.splitWord()}newNode(e){return this.spaces&&(e.raws.before+=this.spaces,this.spaces=""),this.current.append(e)}get currToken(){return this.tokens[this.position]}get nextToken(){return this.tokens[this.position+1]}get prevToken(){return this.tokens[this.position-1]}}});var Xs={};en(Xs,{languages:()=>hi,options:()=>mi,parsers:()=>Js,printers:()=>Oy});var dl=(t,e,s,r)=>{if(!(t&&e==null))return e.replaceAll?e.replaceAll(s,r):s.global?e.replace(s,r):e.split(s).join(r)},E=dl;var Ue="string",Fe="array",$e="cursor",xe="indent",_e="align",We="trim",be="group",ee="fill",le="if-break",ze="indent-if-break",Ye="line-suffix",Ve="line-suffix-boundary",Q="line",Ge="label",ke="break-parent",bt=new Set([$e,xe,_e,We,be,ee,le,ze,Ye,Ve,Q,Ge,ke]);function ml(t){if(typeof t=="string")return Ue;if(Array.isArray(t))return Fe;if(!t)return;let{type:e}=t;if(bt.has(e))return e}var He=ml;var yl=t=>new Intl.ListFormat("en-US",{type:"disjunction"}).format(t);function gl(t){let e=t===null?"null":typeof t;if(e!=="string"&&e!=="object")return`Unexpected doc '${e}', 
Expected it to be 'string' or 'object'.`;if(He(t))throw new Error("doc is valid.");let s=Object.prototype.toString.call(t);if(s!=="[object Object]")return`Unexpected doc '${s}'.`;let r=yl([...bt].map(n=>`'${n}'`));return`Unexpected doc.type '${t.type}'.
Expected it to be ${r}.`}var Wr=class extends Error{name="InvalidDocError";constructor(e){super(gl(e)),this.doc=e}},zr=Wr;var tn=()=>{},ce=tn,kt=tn;function q(t){return ce(t),{type:xe,contents:t}}function rn(t,e){return ce(e),{type:_e,contents:e,n:t}}function L(t,e={}){return ce(t),kt(e.expandedStates,!0),{type:be,id:e.id,contents:t,break:!!e.shouldBreak,expandedStates:e.expandedStates}}function sn(t){return rn({type:"root"},t)}function fe(t){return rn(-1,t)}function Ke(t){return kt(t),{type:ee,parts:t}}function Et(t,e="",s={}){return ce(t),e!==""&&ce(e),{type:le,breakContents:t,flatContents:e,groupId:s.groupId}}var Qe={type:ke};var wl={type:Q,hard:!0};var O={type:Q},M={type:Q,soft:!0},k=[wl,Qe];function V(t,e){ce(t),kt(e);let s=[];for(let r=0;r<e.length;r++)r!==0&&s.push(t),s.push(e[r]);return s}var vl=(t,e,s)=>{if(!(t&&e==null))return Array.isArray(e)||typeof e=="string"?e[s<0?e.length+s:s]:e.at(s)},G=vl;var nn=t=>{if(Array.isArray(t))return t;if(t.type!==ee)throw new Error(`Expect doc to be 'array' or '${ee}'.`);return t.parts};function xl(t,e){if(typeof t=="string")return e(t);let s=new Map;return r(t);function r(i){if(s.has(i))return s.get(i);let o=n(i);return s.set(i,o),o}function n(i){switch(He(i)){case Fe:return e(i.map(r));case ee:return e({...i,parts:i.parts.map(r)});case le:return e({...i,breakContents:r(i.breakContents),flatContents:r(i.flatContents)});case be:{let{expandedStates:o,contents:a}=i;return o?(o=o.map(r),a=o[0]):a=r(a),e({...i,contents:a,expandedStates:o})}case _e:case xe:case ze:case Ge:case Ye:return e({...i,contents:r(i.contents)});case Ue:case $e:case We:case Ve:case Q:case ke:return e(i);default:throw new zr(i)}}}function _l(t){return t.type===Q&&!t.hard?t.soft?"":" ":t.type===le?t.flatContents:t}function on(t){return xl(t,_l)}function bl(t){return Array.isArray(t)&&t.length>0}var te=bl;var St="'",an='"';function kl(t,e){let s=e===!0||e===St?St:an,r=s===St?an:St,n=0,i=0;for(let o of t)o===s?n++:o===r&&i++;return n>i?r:s}var un=kl;function El(t,e,s){let r=e==='"'?"'":'"',i=E(!1,t,/\\(.)|(["'])/gs,(o,a,u)=>a===r?a:u===e?"\\"+u:u||(s&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(a)?a:"\\"+a));return e+i+e}var ln=El;function Sl(t,e){let s=t.slice(1,-1),r=e.parser==="json"||e.parser==="jsonc"||e.parser==="json5"&&e.quoteProps==="preserve"&&!e.singleQuote?'"':e.__isInHtmlAttribute?"'":un(s,e.singleQuote);return ln(s,r,!(e.parser==="css"||e.parser==="less"||e.parser==="scss"||e.__embeddedInHtml))}var Tt=Sl;var Yr=class extends Error{name="UnexpectedNodeError";constructor(e,s,r="type"){super(`Unexpected ${s} node ${r}: ${JSON.stringify(e[r])}.`),this.node=e}},cn=Yr;function Tl(t){return(t==null?void 0:t.type)==="front-matter"}var Ee=Tl;var Cl=new Set(["raw","raws","sourceIndex","source","before","after","trailingComma","spaces"]);function fn(t,e,s){if(Ee(t)&&t.lang==="yaml"&&delete e.value,t.type==="css-comment"&&s.type==="css-root"&&s.nodes.length>0&&((s.nodes[0]===t||Ee(s.nodes[0])&&s.nodes[1]===t)&&(delete e.text,/^\*\s*@(?:format|prettier)\s*$/.test(t.text))||s.type==="css-root"&&G(!1,s.nodes,-1)===t))return null;if(t.type==="value-root"&&delete e.text,(t.type==="media-query"||t.type==="media-query-list"||t.type==="media-feature-expression")&&delete e.value,t.type==="css-rule"&&delete e.params,t.type==="selector-combinator"&&(e.value=E(!1,e.value,/\s+/g," ")),t.type==="media-feature"&&(e.value=E(!1,e.value," ","")),(t.type==="value-word"&&(t.isColor&&t.isHex||["initial","inherit","unset","revert"].includes(e.value.toLowerCase()))||t.type==="media-feature"||t.type==="selector-root-invalid"||t.type==="selector-pseudo")&&(e.value=e.value.toLowerCase()),t.type==="css-decl"&&(e.prop=e.prop.toLowerCase()),(t.type==="css-atrule"||t.type==="css-import")&&(e.name=e.name.toLowerCase()),t.type==="value-number"&&(e.unit=e.unit.toLowerCase()),t.type==="value-unknown"&&(e.value=E(!1,e.value,/;$/g,"")),(t.type==="media-feature"||t.type==="media-keyword"||t.type==="media-type"||t.type==="media-unknown"||t.type==="media-url"||t.type==="media-value"||t.type==="selector-attribute"||t.type==="selector-string"||t.type==="selector-class"||t.type==="selector-combinator"||t.type==="value-string")&&e.value&&(e.value=Al(e.value)),t.type==="selector-attribute"&&(e.attribute=e.attribute.trim(),e.namespace&&typeof e.namespace=="string"&&(e.namespace=e.namespace.trim(),e.namespace.length===0&&(e.namespace=!0)),e.value&&(e.value=E(!1,e.value.trim(),/^["']|["']$/g,""),delete e.quoted)),(t.type==="media-value"||t.type==="media-type"||t.type==="value-number"||t.type==="selector-root-invalid"||t.type==="selector-class"||t.type==="selector-combinator"||t.type==="selector-tag")&&e.value&&(e.value=E(!1,e.value,/([\d+.e-]+)([a-z]*)/gi,(r,n,i)=>{let o=Number(n);return Number.isNaN(o)?r:o+i.toLowerCase()})),t.type==="selector-tag"){let r=t.value.toLowerCase();["from","to"].includes(r)&&(e.value=r)}if(t.type==="css-atrule"&&t.name.toLowerCase()==="supports"&&delete e.value,t.type==="selector-unknown"&&delete e.value,t.type==="value-comma_group"){let r=t.groups.findIndex(n=>n.type==="value-number"&&n.unit==="...");r!==-1&&(e.groups[r].unit="",e.groups.splice(r+1,0,{type:"value-word",value:"...",isColor:!1,isHex:!1}))}if(t.type==="value-comma_group"&&t.groups.some(r=>r.type==="value-atword"&&r.value.endsWith("[")||r.type==="value-word"&&r.value.startsWith("]")))return{type:"value-atword",value:t.groups.map(r=>r.value).join(""),group:{open:null,close:null,groups:[],type:"value-paren_group"}}}fn.ignoredProperties=Cl;function Al(t){return E(!1,E(!1,t,"'",'"'),/\\([^\da-f])/gi,"$1")}var pn=fn;async function Ol(t,e){if(t.lang==="yaml"){let s=t.value.trim(),r=s?await e(s,{parser:"yaml"}):"";return sn([t.startDelimiter,k,r,r?k:"",t.endDelimiter])}}var hn=Ol;function dn(t){let{node:e}=t;if(e.type==="front-matter")return async s=>{let r=await hn(e,s);return r?[r,k]:void 0}}dn.getVisitorKeys=t=>t.type==="css-root"?["frontMatter"]:[];var mn=dn;var je=null;function Je(t){if(je!==null&&typeof je.property){let e=je;return je=Je.prototype=null,e}return je=Je.prototype=t??Object.create(null),new Je}var Nl=10;for(let t=0;t<=Nl;t++)Je();function Vr(t){return Je(t)}function Pl(t,e="type"){Vr(t);function s(r){let n=r[e],i=t[n];if(!Array.isArray(i))throw Object.assign(new Error(`Missing visitor keys for '${n}'.`),{node:r});return i}return s}var yn=Pl;var Rl={"front-matter":[],"css-root":["frontMatter","nodes"],"css-comment":[],"css-rule":["selector","nodes"],"css-decl":["value","selector","nodes"],"css-atrule":["selector","params","value","nodes"],"media-query-list":["nodes"],"media-query":["nodes"],"media-type":[],"media-feature-expression":["nodes"],"media-feature":[],"media-colon":[],"media-value":[],"media-keyword":[],"media-url":[],"media-unknown":[],"selector-root":["nodes"],"selector-selector":["nodes"],"selector-comment":[],"selector-string":[],"selector-tag":[],"selector-id":[],"selector-class":[],"selector-attribute":[],"selector-combinator":["nodes"],"selector-universal":[],"selector-pseudo":["nodes"],"selector-nesting":[],"selector-unknown":[],"value-value":["group"],"value-root":["group"],"value-comment":[],"value-comma_group":["groups"],"value-paren_group":["open","groups","close"],"value-func":["group"],"value-paren":[],"value-number":[],"value-operator":[],"value-word":[],"value-colon":[],"value-comma":[],"value-string":[],"value-atword":[],"value-unicode-range":[],"value-unknown":[]},gn=Rl;var Il=yn(gn),wn=Il;function ql(t,e){let s=0;for(let r=0;r<t.line-1;++r)s=e.indexOf(`
`,s)+1;return s+t.column}var Gr=ql;function Ct(t){return(e,s,r)=>{let n=!!(r!=null&&r.backwards);if(s===!1)return!1;let{length:i}=e,o=s;for(;o>=0&&o<i;){let a=e.charAt(o);if(t instanceof RegExp){if(!t.test(a))return o}else if(!t.includes(a))return o;n?o--:o++}return o===-1||o===i?o:!1}}var Ng=Ct(/\s/),At=Ct(" 	"),vn=Ct(",; 	"),Ot=Ct(/[^\n\r]/);function xn(t,e){var s,r,n;if(typeof((r=(s=t.source)==null?void 0:s.start)==null?void 0:r.offset)=="number")return t.source.start.offset;if(typeof t.sourceIndex=="number")return t.sourceIndex;if((n=t.source)!=null&&n.start)return Gr(t.source.start,e);throw Object.assign(new Error("Can not locate node."),{node:t})}function Hr(t,e){var s,r;if(t.type==="css-comment"&&t.inline)return Ot(e,t.source.startOffset);if(typeof((r=(s=t.source)==null?void 0:s.end)==null?void 0:r.offset)=="number")return t.source.end.offset;if(t.source){if(t.source.end)return Gr(t.source.end,e);if(te(t.nodes))return Hr(G(!1,t.nodes,-1),e)}return null}function Kr(t,e){t.source&&(t.source.startOffset=xn(t,e),t.source.endOffset=Hr(t,e));for(let s in t){let r=t[s];s==="source"||!r||typeof r!="object"||(r.type==="value-root"||r.type==="value-unknown"?_n(r,Ll(t),r.text||r.value):Kr(r,e))}}function _n(t,e,s){t.source&&(t.source.startOffset=xn(t,s)+e,t.source.endOffset=Hr(t,s)+e);for(let r in t){let n=t[r];r==="source"||!n||typeof n!="object"||_n(n,e,s)}}function Ll(t){var s;let e=t.source.startOffset;return typeof t.prop=="string"&&(e+=t.prop.length),t.type==="css-atrule"&&typeof t.name=="string"&&(e+=1+t.name.length+t.raws.afterName.match(/^\s*:?\s*/)[0].length),t.type!=="css-atrule"&&typeof((s=t.raws)==null?void 0:s.between)=="string"&&(e+=t.raws.between.length),e}function bn(t){let e="initial",s="initial",r,n=!1,i=[];for(let o=0;o<t.length;o++){let a=t[o];switch(e){case"initial":if(a==="'"){e="single-quotes";continue}if(a==='"'){e="double-quotes";continue}if((a==="u"||a==="U")&&t.slice(o,o+4).toLowerCase()==="url("){e="url",o+=3;continue}if(a==="*"&&t[o-1]==="/"){e="comment-block";continue}if(a==="/"&&t[o-1]==="/"){e="comment-inline",r=o-1;continue}continue;case"single-quotes":if(a==="'"&&t[o-1]!=="\\"&&(e=s,s="initial"),a===`
`||a==="\r")return t;continue;case"double-quotes":if(a==='"'&&t[o-1]!=="\\"&&(e=s,s="initial"),a===`
`||a==="\r")return t;continue;case"url":if(a===")"&&(e="initial"),a===`
`||a==="\r")return t;if(a==="'"){e="single-quotes",s="url";continue}if(a==='"'){e="double-quotes",s="url";continue}continue;case"comment-block":a==="/"&&t[o-1]==="*"&&(e="initial");continue;case"comment-inline":(a==='"'||a==="'"||a==="*")&&(n=!0),(a===`
`||a==="\r")&&(n&&i.push([r,o]),e="initial",n=!1);continue}}for(let[o,a]of i)t=t.slice(0,o)+E(!1,t.slice(o,a),/["'*]/g," ")+t.slice(a);return t}function N(t){var e;return(e=t.source)==null?void 0:e.startOffset}function P(t){var e;return(e=t.source)==null?void 0:e.endOffset}var re=ue(On(),1);function Vl(t){if(!t.startsWith("#!"))return"";let e=t.indexOf(`
`);return e===-1?t:t.slice(0,e)}var Nn=Vl;function Pn(t){let e=Nn(t);e&&(t=t.slice(e.length+1));let s=(0,re.extract)(t),{pragmas:r,comments:n}=(0,re.parseWithComments)(s);return{shebang:e,text:t,pragmas:r,comments:n}}function Rn(t){let{pragmas:e}=Pn(t);return Object.prototype.hasOwnProperty.call(e,"prettier")||Object.prototype.hasOwnProperty.call(e,"format")}function In(t){let{shebang:e,text:s,pragmas:r,comments:n}=Pn(t),i=(0,re.strip)(s),o=(0,re.print)({pragmas:{format:"",...r},comments:n.trimStart()});return(e?`${e}
`:"")+o+(i.startsWith(`
`)?`
`:`

`)+i}var Gl=new RegExp("^(?<startDelimiter>-{3}|\\+{3})(?<language>[^\\n]*)\\n(?:|(?<value>.*?)\\n)(?<endDelimiter>\\k<startDelimiter>|\\.{3})[^\\S\\n]*(?:\\n|$)","s");function Hl(t){let e=t.match(Gl);if(!e)return{content:t};let{startDelimiter:s,language:r,value:n="",endDelimiter:i}=e.groups,o=r.trim()||"yaml";if(s==="+++"&&(o="toml"),o!=="yaml"&&s!==i)return{content:t};let[a]=e;return{frontMatter:{type:"front-matter",lang:o,value:n,startDelimiter:s,endDelimiter:i,raw:a.replace(/\n$/,"")},content:E(!1,a,/[^\n]/g," ")+t.slice(a.length)}}var Xe=Hl;function qn(t){return Rn(Xe(t).content)}function Ln(t){let{frontMatter:e,content:s}=Xe(t);return(e?e.raw+`

`:"")+In(s)}var Kl=new Set(["red","green","blue","alpha","a","rgb","hue","h","saturation","s","lightness","l","whiteness","w","blackness","b","tint","shade","blend","blenda","contrast","hsl","hsla","hwb","hwba"]);function Dn(t){var e,s;return(s=(e=t.findAncestor(r=>r.type==="css-decl"))==null?void 0:e.prop)==null?void 0:s.toLowerCase()}var Ql=new Set(["initial","inherit","unset","revert"]);function Mn(t){return Ql.has(t.toLowerCase())}function Bn(t,e){var r;let s=t.findAncestor(n=>n.type==="css-atrule");return((r=s==null?void 0:s.name)==null?void 0:r.toLowerCase().endsWith("keyframes"))&&["from","to"].includes(e.toLowerCase())}function se(t){return t.includes("$")||t.includes("@")||t.includes("#")||t.startsWith("%")||t.startsWith("--")||t.startsWith(":--")||t.includes("(")&&t.includes(")")?t:t.toLowerCase()}function Se(t,e){var r;let s=t.findAncestor(n=>n.type==="value-func");return((r=s==null?void 0:s.value)==null?void 0:r.toLowerCase())===e}function Un(t){var r;let e=t.findAncestor(n=>n.type==="css-rule"),s=(r=e==null?void 0:e.raws)==null?void 0:r.selector;return s&&(s.startsWith(":import")||s.startsWith(":export"))}function Te(t,e){let s=Array.isArray(e)?e:[e],r=t.findAncestor(n=>n.type==="css-atrule");return r&&s.includes(r.name.toLowerCase())}function Fn(t){var s;let{node:e}=t;return e.groups[0].value==="url"&&e.groups.length===2&&((s=t.findAncestor(r=>r.type==="css-atrule"))==null?void 0:s.name)==="import"}function $n(t){return t.type==="value-func"&&t.value.toLowerCase()==="url"}function Wn(t){return t.type==="value-func"&&t.value.toLowerCase()==="var"}function Nt(t,e){var r;let s=(r=t.parent)==null?void 0:r.nodes;return s&&s.indexOf(e)===s.length-1}function zn(t){let{selector:e}=t;return e?typeof e=="string"&&/^@.+:.*$/.test(e)||e.value&&/^@.+:.*$/.test(e.value):!1}function Yn(t){return t.type==="value-word"&&["from","through","end"].includes(t.value)}function Vn(t){return t.type==="value-word"&&["and","or","not"].includes(t.value)}function Gn(t){return t.type==="value-word"&&t.value==="in"}function Pt(t){return t.type==="value-operator"&&t.value==="*"}function Ze(t){return t.type==="value-operator"&&t.value==="/"}function j(t){return t.type==="value-operator"&&t.value==="+"}function he(t){return t.type==="value-operator"&&t.value==="-"}function jl(t){return t.type==="value-operator"&&t.value==="%"}function Rt(t){return Pt(t)||Ze(t)||j(t)||he(t)||jl(t)}function Hn(t){return t.type==="value-word"&&["==","!="].includes(t.value)}function Kn(t){return t.type==="value-word"&&["<",">","<=",">="].includes(t.value)}function et(t,e){return e.parser==="scss"&&t.type==="css-atrule"&&["if","else","for","each","while"].includes(t.name)}function jr(t){var e;return((e=t.raws)==null?void 0:e.params)&&/^\(\s*\)$/.test(t.raws.params)}function It(t){return t.name.startsWith("prettier-placeholder")}function Qn(t){return t.prop.startsWith("@prettier-placeholder")}function jn(t,e){return t.value==="$$"&&t.type==="value-func"&&(e==null?void 0:e.type)==="value-word"&&!e.raws.before}function Jn(t){var e,s;return((e=t.value)==null?void 0:e.type)==="value-root"&&((s=t.value.group)==null?void 0:s.type)==="value-value"&&t.prop.toLowerCase()==="composes"}function Xn(t){var e,s,r;return((r=(s=(e=t.value)==null?void 0:e.group)==null?void 0:s.group)==null?void 0:r.type)==="value-paren_group"&&t.value.group.group.open!==null&&t.value.group.group.close!==null}function de(t){var e;return((e=t.raws)==null?void 0:e.before)===""}function qt(t){var e,s;return t.type==="value-comma_group"&&((s=(e=t.groups)==null?void 0:e[1])==null?void 0:s.type)==="value-colon"}function Qr(t){var e;return t.type==="value-paren_group"&&((e=t.groups)==null?void 0:e[0])&&qt(t.groups[0])}function Jr(t,e){var i;if(e.parser!=="scss")return!1;let{node:s}=t;if(s.groups.length===0)return!1;let r=t.grandparent;if(!Qr(s)&&!(r&&Qr(r)))return!1;let n=t.findAncestor(o=>o.type==="css-decl");return!!((i=n==null?void 0:n.prop)!=null&&i.startsWith("$")||Qr(r)||r.type==="value-func")}function Xr(t){return t.type==="value-comment"&&t.inline}function Lt(t){return t.type==="value-word"&&t.value==="#"}function Zr(t){return t.type==="value-word"&&t.value==="{"}function Dt(t){return t.type==="value-word"&&t.value==="}"}function tt(t){return["value-word","value-atword"].includes(t.type)}function Mt(t){return(t==null?void 0:t.type)==="value-colon"}function Zn(t,e){if(!qt(e))return!1;let{groups:s}=e,r=s.indexOf(t);return r===-1?!1:Mt(s[r+1])}function ei(t){return t.value&&["not","and","or"].includes(t.value.toLowerCase())}function ti(t){return t.type!=="value-func"?!1:Kl.has(t.value.toLowerCase())}function Ce(t){return/\/\//.test(t.split(/[\n\r]/).pop())}function rt(t){return(t==null?void 0:t.type)==="value-atword"&&t.value.startsWith("prettier-placeholder-")}function ri(t,e){var s,r;if(((s=t.open)==null?void 0:s.value)!=="("||((r=t.close)==null?void 0:r.value)!==")"||t.groups.some(n=>n.type!=="value-comma_group"))return!1;if(e.type==="value-comma_group"){let n=e.groups.indexOf(t)-1,i=e.groups[n];if((i==null?void 0:i.type)==="value-word"&&i.value==="with")return!0}return!1}function st(t){var e,s;return t.type==="value-paren_group"&&((e=t.open)==null?void 0:e.value)==="("&&((s=t.close)==null?void 0:s.value)===")"}function Jl(t,e,s){var d;let{node:r}=t,n=t.parent,i=t.grandparent,o=Dn(t),a=o&&n.type==="value-value"&&(o==="grid"||o.startsWith("grid-template")),u=t.findAncestor(m=>m.type==="css-atrule"),c=u&&et(u,e),f=r.groups.some(m=>Xr(m)),p=t.map(s,"groups"),l=[],g=Se(t,"url"),x=!1,h=!1;for(let m=0;m<r.groups.length;++m){l.push(p[m]);let _=r.groups[m-1],w=r.groups[m],v=r.groups[m+1],R=r.groups[m+2];if(g){(v&&j(v)||j(w))&&l.push(" ");continue}if(Te(t,"forward")&&w.type==="value-word"&&w.value&&_!==void 0&&_.type==="value-word"&&_.value==="as"&&v.type==="value-operator"&&v.value==="*"||!v||w.type==="value-word"&&w.value.endsWith("-")&&rt(v))continue;if(w.type==="value-string"&&w.quoted){let A=w.value.lastIndexOf("#{"),ve=w.value.lastIndexOf("}");A!==-1&&ve!==-1?x=A>ve:A!==-1?x=!0:ve!==-1&&(x=!1)}if(x||Mt(w)||Mt(v)||w.type==="value-atword"&&(w.value===""||w.value.endsWith("["))||v.type==="value-word"&&v.value.startsWith("]")||w.value==="~"||w.type!=="value-string"&&w.value&&w.value.includes("\\")&&v&&v.type!=="value-comment"||_!=null&&_.value&&_.value.indexOf("\\")===_.value.length-1&&w.type==="value-operator"&&w.value==="/"||w.value==="\\"||jn(w,v)||Lt(w)||Zr(w)||Dt(v)||Zr(v)&&de(v)||Dt(w)&&de(v)||w.value==="--"&&Lt(v))continue;let F=Rt(w),K=Rt(v);if((F&&Lt(v)||K&&Dt(w))&&de(v)||!_&&Ze(w)||Se(t,"calc")&&(j(w)||j(v)||he(w)||he(v))&&de(v))continue;let $=(j(w)||he(w))&&m===0&&(v.type==="value-number"||v.isHex)&&i&&ti(i)&&!de(v),T=(R==null?void 0:R.type)==="value-func"||R&&tt(R)||w.type==="value-func"||tt(w),C=v.type==="value-func"||tt(v)||(_==null?void 0:_.type)==="value-func"||_&&tt(_);if(e.parser==="scss"&&F&&w.value==="-"&&v.type==="value-func"&&P(w)!==N(v)){l.push(" ");continue}if(!(!(Pt(v)||Pt(w))&&!Se(t,"calc")&&!$&&(Ze(v)&&!T||Ze(w)&&!C||j(v)&&!T||j(w)&&!C||he(v)||he(w))&&(de(v)||F&&(!_||_&&Rt(_))))&&!((e.parser==="scss"||e.parser==="less")&&F&&w.value==="-"&&st(v)&&P(w)===N(v.open)&&v.open.value==="(")){if(Xr(w)){if(n.type==="value-paren_group"){l.push(fe(k));continue}l.push(k);continue}if(c&&(Hn(v)||Kn(v)||Vn(v)||Gn(w)||Yn(w))){l.push(" ");continue}if(u&&u.name.toLowerCase()==="namespace"){l.push(" ");continue}if(a){w.source&&v.source&&w.source.start.line!==v.source.start.line?(l.push(k),h=!0):l.push(" ");continue}if(K){l.push(" ");continue}if((v==null?void 0:v.value)!=="..."&&!(rt(w)&&rt(v)&&P(w)===N(v))){if(rt(w)&&st(v)&&P(w)===N(v.open)){l.push(M);continue}if(w.value==="with"&&st(v)){l.push(" ");continue}(d=w.value)!=null&&d.endsWith("#")&&v.value==="{"&&st(v.group)||l.push(O)}}}return f&&l.push(Qe),h&&l.unshift(k),c?L(q(l)):Fn(t)?L(Ke(l)):L(q(Ke(l)))}var si=Jl;function Xl(t){return t.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(?=\d)/,"$1$2").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")}var ni=Xl;var es=new Map([["em","em"],["rem","rem"],["ex","ex"],["rex","rex"],["cap","cap"],["rcap","rcap"],["ch","ch"],["rch","rch"],["ic","ic"],["ric","ric"],["lh","lh"],["rlh","rlh"],["vw","vw"],["svw","svw"],["lvw","lvw"],["dvw","dvw"],["vh","vh"],["svh","svh"],["lvh","lvh"],["dvh","dvh"],["vi","vi"],["svi","svi"],["lvi","lvi"],["dvi","dvi"],["vb","vb"],["svb","svb"],["lvb","lvb"],["dvb","dvb"],["vmin","vmin"],["svmin","svmin"],["lvmin","lvmin"],["dvmin","dvmin"],["vmax","vmax"],["svmax","svmax"],["lvmax","lvmax"],["dvmax","dvmax"],["cm","cm"],["mm","mm"],["q","Q"],["in","in"],["pt","pt"],["pc","pc"],["px","px"],["deg","deg"],["grad","grad"],["rad","rad"],["turn","turn"],["s","s"],["ms","ms"],["hz","Hz"],["khz","kHz"],["dpi","dpi"],["dpcm","dpcm"],["dppx","dppx"],["x","x"],["cqw","cqw"],["cqh","cqh"],["cqi","cqi"],["cqb","cqb"],["cqmin","cqmin"],["cqmax","cqmax"]]);function ii(t){let e=t.toLowerCase();return es.has(e)?es.get(e):t}var oi=/(["'])(?:(?!\1)[^\\]|\\.)*\1/gs,Zl=/(?:\d*\.\d+|\d+\.?)(?:e[+-]?\d+)?/gi,ec=/[a-z]+/gi,tc=/[$@]?[_a-z\u0080-\uFFFF][\w\u0080-\uFFFF-]*/gi,rc=new RegExp(oi.source+`|(${tc.source})?(${Zl.source})(${ec.source})?`,"gi");function W(t,e){return E(!1,t,oi,s=>Tt(s,e))}function ai(t,e){let s=e.singleQuote?"'":'"';return t.includes('"')||t.includes("'")?t:s+t+s}function me(t){return E(!1,t,rc,(e,s,r,n,i)=>!r&&n?ts(n)+se(i||""):e)}function ts(t){return ni(t).replace(/\.0(?=$|e)/,"")}function ui(t){return t.trailingComma==="es5"||t.trailingComma==="all"}function sc(t,e,s){let r=!!(s!=null&&s.backwards);if(e===!1)return!1;let n=t.charAt(e);if(r){if(t.charAt(e-1)==="\r"&&n===`
`)return e-2;if(n===`
`||n==="\r"||n==="\u2028"||n==="\u2029")return e-1}else{if(n==="\r"&&t.charAt(e+1)===`
`)return e+2;if(n===`
`||n==="\r"||n==="\u2028"||n==="\u2029")return e+1}return e}var Bt=sc;function nc(t,e,s={}){let r=At(t,s.backwards?e-1:e,s),n=Bt(t,r,s);return r!==n}var Ut=nc;function ic(t,e){if(e===!1)return!1;if(t.charAt(e)==="/"&&t.charAt(e+1)==="*"){for(let s=e+2;s<t.length;++s)if(t.charAt(s)==="*"&&t.charAt(s+1)==="/")return s+2}return e}var li=ic;function oc(t,e){return e===!1?!1:t.charAt(e)==="/"&&t.charAt(e+1)==="/"?Ot(t,e):e}var ci=oc;function ac(t,e){let s=null,r=e;for(;r!==s;)s=r,r=vn(t,r),r=li(t,r),r=At(t,r);return r=ci(t,r),r=Bt(t,r),r!==!1&&Ut(t,r)}var Ft=ac;function uc({node:t,parent:e},s){return!!(t.source&&s.originalText.slice(N(t),N(e.close)).trimEnd().endsWith(","))}function lc(t,e){return Wn(t.grandparent)&&uc(t,e)?",":t.node.type!=="value-comment"&&!(t.node.type==="value-comma_group"&&t.node.groups.every(s=>s.type==="value-comment"))&&ui(e)&&t.callParent(()=>Jr(t,e))?Et(","):""}function fi(t,e,s){let{node:r,parent:n}=t,i=t.map(({node:g})=>typeof g=="string"?g:s(),"groups");if(n&&$n(n)&&(r.groups.length===1||r.groups.length>0&&r.groups[0].type==="value-comma_group"&&r.groups[0].groups.length>0&&r.groups[0].groups[0].type==="value-word"&&r.groups[0].groups[0].value.startsWith("data:")))return[r.open?s("open"):"",V(",",i),r.close?s("close"):""];if(!r.open){let g=rs(t),x=V([",",g?k:O],i);return q(g?[k,x]:L(Ke(x)))}let o=t.map(({node:g,isLast:x,index:h})=>{var _;let d=i[h];if(qt(g)&&g.type==="value-comma_group"&&g.groups&&g.groups[0].type!=="value-paren_group"&&((_=g.groups[2])==null?void 0:_.type)==="value-paren_group"){let w=nn(d.contents.contents);w[1]=L(w[1]),d=L(fe(d))}let m=[d,x?lc(t,e):","];if(!x&&g.type==="value-comma_group"&&te(g.groups)){let w=G(!1,g.groups,-1);!w.source&&w.close&&(w=w.close),w.source&&Ft(e.originalText,P(w))&&m.push(k)}return m},"groups"),a=Zn(r,n),u=ri(r,n),c=Jr(t,e),f=u||c&&!a,p=u||a,l=L([r.open?s("open"):"",q([M,V(O,o)]),M,r.close?s("close"):""],{shouldBreak:f});return p?fe(l):l}function rs(t){return t.match(e=>e.type==="value-paren_group"&&!e.open&&e.groups.some(s=>s.type==="value-comma_group"),(e,s)=>s==="group"&&e.type==="value-value",(e,s)=>s==="group"&&e.type==="value-root",(e,s)=>s==="value"&&(e.type==="css-decl"&&!e.prop.startsWith("--")||e.type==="css-atrule"&&e.variable))}function cc(t,e,s){let r=[];return t.each(()=>{let{node:n,previous:i}=t;if((i==null?void 0:i.type)==="css-comment"&&i.text.trim()==="prettier-ignore"?r.push(e.originalText.slice(N(n),P(n))):r.push(s()),t.isLast)return;let{next:o}=t;o.type==="css-comment"&&!Ut(e.originalText,N(o),{backwards:!0})&&!Ee(n)||o.type==="css-atrule"&&o.name==="else"&&n.type!=="css-comment"?r.push(" "):(r.push(e.__isHTMLStyleAttribute?O:k),Ft(e.originalText,P(n))&&!Ee(n)&&r.push(k))},"nodes"),r}var Ae=cc;function fc(t,e,s){var n,i,o,a,u,c;let{node:r}=t;switch(r.type){case"front-matter":return[r.raw,k];case"css-root":{let f=Ae(t,e,s),p=r.raws.after.trim();return p.startsWith(";")&&(p=p.slice(1).trim()),[r.frontMatter?[s("frontMatter"),k]:"",f,p?` ${p}`:"",r.nodes.length>0?k:""]}case"css-comment":{let f=r.inline||r.raws.inline,p=e.originalText.slice(N(r),P(r));return f?p.trimEnd():p}case"css-rule":return[s("selector"),r.important?" !important":"",r.nodes?[((n=r.selector)==null?void 0:n.type)==="selector-unknown"&&Ce(r.selector.value)?O:r.selector?" ":"","{",r.nodes.length>0?q([k,Ae(t,e,s)]):"",k,"}",zn(r)?";":""]:";"];case"css-decl":{let f=t.parent,{between:p}=r.raws,l=p.trim(),g=l===":",x=typeof r.value=="string"&&/^ *$/.test(r.value),h=typeof r.value=="string"?r.value:s("value");return h=Jn(r)?on(h):h,!g&&Ce(l)&&!((o=(i=r.value)==null?void 0:i.group)!=null&&o.group&&t.call(()=>rs(t),"value","group","group"))&&(h=q([k,fe(h)])),[E(!1,r.raws.before,/[\s;]/g,""),f.type==="css-atrule"&&f.variable||Un(t)?r.prop:se(r.prop),l.startsWith("//")?" ":"",l,r.extend||x?"":" ",e.parser==="less"&&r.extend&&r.selector?["extend(",s("selector"),")"]:"",h,r.raws.important?r.raws.important.replace(/\s*!\s*important/i," !important"):r.important?" !important":"",r.raws.scssDefault?r.raws.scssDefault.replace(/\s*!default/i," !default"):r.scssDefault?" !default":"",r.raws.scssGlobal?r.raws.scssGlobal.replace(/\s*!global/i," !global"):r.scssGlobal?" !global":"",r.nodes?[" {",q([M,Ae(t,e,s)]),M,"}"]:Qn(r)&&!f.raws.semicolon&&e.originalText[P(r)-1]!==";"?"":e.__isHTMLStyleAttribute&&Nt(t,r)?Et(";"):";"]}case"css-atrule":{let f=t.parent,p=It(r)&&!f.raws.semicolon&&e.originalText[P(r)-1]!==";";if(e.parser==="less"){if(r.mixin)return[s("selector"),r.important?" !important":"",p?"":";"];if(r.function)return[r.name,typeof r.params=="string"?r.params:s("params"),p?"":";"];if(r.variable)return["@",r.name,": ",r.value?s("value"):"",r.raws.between.trim()?r.raws.between.trim()+" ":"",r.nodes?["{",q([r.nodes.length>0?M:"",Ae(t,e,s)]),M,"}"]:"",p?"":";"]}let l=r.name==="import"&&((a=r.params)==null?void 0:a.type)==="value-unknown"&&r.params.value.endsWith(";");return["@",jr(r)||r.name.endsWith(":")||It(r)?r.name:se(r.name),r.params?[jr(r)?"":It(r)?r.raws.afterName===""?"":r.name.endsWith(":")?" ":/^\s*\n\s*\n/.test(r.raws.afterName)?[k,k]:/^\s*\n/.test(r.raws.afterName)?k:" ":" ",typeof r.params=="string"?r.params:s("params")]:"",r.selector?q([" ",s("selector")]):"",r.value?L([" ",s("value"),et(r,e)?Xn(r)?" ":O:""]):r.name==="else"?" ":"",r.nodes?[et(r,e)?"":r.selector&&!r.selector.nodes&&typeof r.selector.value=="string"&&Ce(r.selector.value)||!r.selector&&typeof r.params=="string"&&Ce(r.params)?O:" ","{",q([r.nodes.length>0?M:"",Ae(t,e,s)]),M,"}"]:p||l?"":";"]}case"media-query-list":{let f=[];return t.each(({node:p})=>{p.type==="media-query"&&p.value===""||f.push(s())},"nodes"),L(q(V(O,f)))}case"media-query":return[V(" ",t.map(s,"nodes")),Nt(t,r)?"":","];case"media-type":return me(W(r.value,e));case"media-feature-expression":return r.nodes?["(",...t.map(s,"nodes"),")"]:r.value;case"media-feature":return se(W(E(!1,r.value,/ +/g," "),e));case"media-colon":return[r.value," "];case"media-value":return me(W(r.value,e));case"media-keyword":return W(r.value,e);case"media-url":return W(E(!1,E(!1,r.value,/^url\(\s+/gi,"url("),/\s+\)$/g,")"),e);case"media-unknown":return r.value;case"selector-root":return L([Te(t,"custom-selector")?[t.findAncestor(f=>f.type==="css-atrule").customSelector,O]:"",V([",",Te(t,["extend","custom-selector","nest"])?O:k],t.map(s,"nodes"))]);case"selector-selector":return L(q(t.map(s,"nodes")));case"selector-comment":return r.value;case"selector-string":return W(r.value,e);case"selector-tag":return[r.namespace?[r.namespace===!0?"":r.namespace.trim(),"|"]:"",((u=t.previous)==null?void 0:u.type)==="selector-nesting"?r.value:me(Bn(t,r.value)?r.value.toLowerCase():r.value)];case"selector-id":return["#",r.value];case"selector-class":return[".",me(W(r.value,e))];case"selector-attribute":return["[",r.namespace?[r.namespace===!0?"":r.namespace.trim(),"|"]:"",r.attribute.trim(),r.operator??"",r.value?ai(W(r.value.trim(),e),e):"",r.insensitive?" i":"","]"];case"selector-combinator":{if(r.value==="+"||r.value===">"||r.value==="~"||r.value===">>>"){let l=t.parent;return[l.type==="selector-selector"&&l.nodes[0]===r?"":O,r.value,Nt(t,r)?"":" "]}let f=r.value.trim().startsWith("(")?O:"",p=me(W(r.value.trim(),e))||O;return[f,p]}case"selector-universal":return[r.namespace?[r.namespace===!0?"":r.namespace.trim(),"|"]:"",r.value];case"selector-pseudo":return[se(r.value),te(r.nodes)?L(["(",q([M,V([",",O],t.map(s,"nodes"))]),M,")"]):""];case"selector-nesting":return r.value;case"selector-unknown":{let f=t.findAncestor(g=>g.type==="css-rule");if(f!=null&&f.isSCSSNesterProperty)return me(W(se(r.value),e));let p=t.parent;if((c=p.raws)!=null&&c.selector){let g=N(p),x=g+p.raws.selector.length;return e.originalText.slice(g,x).trim()}let l=t.grandparent;if(p.type==="value-paren_group"&&(l==null?void 0:l.type)==="value-func"&&l.value==="selector"){let g=P(p.open)+1,x=N(p.close),h=e.originalText.slice(g,x).trim();return Ce(h)?[Qe,h]:h}return r.value}case"value-value":case"value-root":return s("group");case"value-comment":return e.originalText.slice(N(r),P(r));case"value-comma_group":return si(t,e,s);case"value-paren_group":return fi(t,e,s);case"value-func":return[r.value,Te(t,"supports")&&ei(r)?" ":"",s("group")];case"value-paren":return r.value;case"value-number":return[ts(r.value),ii(r.unit)];case"value-operator":return r.value;case"value-word":return r.isColor&&r.isHex||Mn(r.value)?r.value.toLowerCase():r.value;case"value-colon":{let{previous:f}=t;return[r.value,typeof(f==null?void 0:f.value)=="string"&&f.value.endsWith("\\")||Se(t,"url")?"":O]}case"value-string":return Tt(r.raws.quote+r.value+r.raws.quote,e);case"value-atword":return["@",r.value];case"value-unicode-range":return r.value;case"value-unknown":return r.value;case"value-comma":default:throw new cn(r,"PostCSS")}}var pc={print:fc,embed:mn,insertPragma:Ln,massageAstNode:pn,getVisitorKeys:wn},pi=pc;var hi=[{linguistLanguageId:50,name:"CSS",type:"markup",tmScope:"source.css",aceMode:"css",codemirrorMode:"css",codemirrorMimeType:"text/css",color:"#563d7c",extensions:[".css",".wxss"],parsers:["css"],vscodeLanguageIds:["css"]},{linguistLanguageId:262764437,name:"PostCSS",type:"markup",color:"#dc3a0c",tmScope:"source.postcss",group:"CSS",extensions:[".pcss",".postcss"],aceMode:"text",parsers:["css"],vscodeLanguageIds:["postcss"]},{linguistLanguageId:198,name:"Less",type:"markup",color:"#1d365d",aliases:["less-css"],extensions:[".less"],tmScope:"source.css.less",aceMode:"less",codemirrorMode:"css",codemirrorMimeType:"text/css",parsers:["less"],vscodeLanguageIds:["less"]},{linguistLanguageId:329,name:"SCSS",type:"markup",color:"#c6538c",tmScope:"source.css.scss",aceMode:"scss",codemirrorMode:"css",codemirrorMimeType:"text/x-scss",extensions:[".scss"],parsers:["scss"],vscodeLanguageIds:["scss"]}];var di={bracketSpacing:{category:"Common",type:"boolean",default:!0,description:"Print spaces between brackets.",oppositeDescription:"Do not print spaces between brackets."},singleQuote:{category:"Common",type:"boolean",default:!1,description:"Use single quotes instead of double quotes."},proseWrap:{category:"Common",type:"choice",default:"preserve",description:"How to wrap prose.",choices:[{value:"always",description:"Wrap prose if it exceeds the print width."},{value:"never",description:"Do not wrap prose."},{value:"preserve",description:"Wrap prose as-is."}]},bracketSameLine:{category:"Common",type:"boolean",default:!1,description:"Put > of opening tags on the last line instead of on a new line."},singleAttributePerLine:{category:"Common",type:"boolean",default:!1,description:"Enforce single attribute per line in HTML, Vue and JSX."}};var hc={singleQuote:di.singleQuote},mi=hc;var Js={};en(Js,{css:()=>Ty,less:()=>Cy,scss:()=>Ay});var tl=ue(ht(),1),rl=ue(bo(),1),sl=ue(ra(),1);function Zf(t,e){let s=new SyntaxError(t+" ("+e.loc.start.line+":"+e.loc.start.column+")");return Object.assign(s,e)}var sa=Zf;var ca=ue(la(),1);function J(t,e,s){if(t&&typeof t=="object"){delete t.parent;for(let r in t)J(t[r],e,s),r==="type"&&typeof t[r]=="string"&&!t[r].startsWith(e)&&(!s||!s.test(t[r]))&&(t[r]=e+t[r])}return t}function Ls(t){if(t&&typeof t=="object"){delete t.parent;for(let e in t)Ls(t[e]);!Array.isArray(t)&&t.value&&!t.type&&(t.type="unknown")}return t}var fp=ca.default.default;function pp(t){let e;try{e=fp(t)}catch{return{type:"selector-unknown",value:t}}return J(Ls(e),"media-")}var fa=pp;var iu=ue(nu(),1);function Tm(t){if(/\/\/|\/\*/.test(t))return{type:"selector-unknown",value:t.trim()};let e;try{new iu.default(s=>{e=s}).process(t)}catch{return{type:"selector-unknown",value:t}}return J(e,"selector-")}var Z=Tm;var Ju=ue(Gu(),1);var dy=t=>{for(;t.parent;)t=t.parent;return t},Ur=dy;function my(t){return Ur(t).text.slice(t.group.open.sourceIndex+1,t.group.close.sourceIndex).trim()}var Hu=my;function yy(t){if(te(t)){for(let e=t.length-1;e>0;e--)if(t[e].type==="word"&&t[e].value==="{"&&t[e-1].type==="word"&&t[e-1].value.endsWith("#"))return!0}return!1}var Ku=yy;function gy(t){return t.some(e=>e.type==="string"||e.type==="func"&&!e.value.endsWith("\\"))}var Qu=gy;function wy(t,e){return!!(e.parser==="scss"&&(t==null?void 0:t.type)==="word"&&t.value.startsWith("$"))}var ju=wy;function vy(t,e){var u;let{nodes:s}=t,r={open:null,close:null,groups:[],type:"paren_group"},n=[r],i=r,o={groups:[],type:"comma_group"},a=[o];for(let c=0;c<s.length;++c){let f=s[c];if(e.parser==="scss"&&f.type==="number"&&f.unit===".."&&f.value.endsWith(".")&&(f.value=f.value.slice(0,-1),f.unit="..."),f.type==="func"&&f.value==="selector"&&(f.group.groups=[Z(Ur(t).text.slice(f.group.open.sourceIndex+1,f.group.close.sourceIndex))]),f.type==="func"&&f.value==="url"){let p=((u=f.group)==null?void 0:u.groups)??[],l=[];for(let g=0;g<p.length;g++){let x=p[g];x.type==="comma_group"?l=[...l,...x.groups]:l.push(x)}(Ku(l)||!Qu(l)&&!ju(l[0],e))&&(f.group.groups=[Hu(f)])}if(f.type==="paren"&&f.value==="(")r={open:f,close:null,groups:[],type:"paren_group"},n.push(r),o={groups:[],type:"comma_group"},a.push(o);else if(f.type==="paren"&&f.value===")"){if(o.groups.length>0&&r.groups.push(o),r.close=f,a.length===1)throw new Error("Unbalanced parenthesis");a.pop(),o=G(!1,a,-1),o.groups.push(r),n.pop(),r=G(!1,n,-1)}else f.type==="comma"?(r.groups.push(o),o={groups:[],type:"comma_group"},a[a.length-1]=o):o.groups.push(f)}return o.groups.length>0&&r.groups.push(o),i}function Fr(t){return t.type==="paren_group"&&!t.open&&!t.close&&t.groups.length===1||t.type==="comma_group"&&t.groups.length===1?Fr(t.groups[0]):t.type==="paren_group"||t.type==="comma_group"?{...t,groups:t.groups.map(Fr)}:t}function Xu(t,e){if(t&&typeof t=="object")for(let s in t)s!=="parent"&&(Xu(t[s],e),s==="nodes"&&(t.group=Fr(vy(t,e)),delete t[s]));return t}function xy(t,e){if(e.parser==="less"&&t.startsWith("~`"))return{type:"value-unknown",value:t};let s=null;try{s=new Ju.default(t,{loose:!0}).parse()}catch{return{type:"value-unknown",value:t}}s.text=t;let r=Xu(s,e);return J(r,"value-",/^selector-/)}var ae=xy;var _y=new Set(["import","use","forward"]);function by(t){return _y.has(t)}var Zu=by;function ky(t,e){return e.parser!=="scss"||!t.selector?!1:t.selector.replace(/\/\*.*?\*\//,"").replace(/\/\/.*\n/,"").trim().endsWith(":")}var el=ky;var Ey=/(\s*)(!default).*$/,Sy=/(\s*)(!global).*$/;function nl(t,e){var s,r;if(t&&typeof t=="object"){delete t.parent;for(let a in t)nl(t[a],e);if(!t.type)return t;if(t.raws??(t.raws={}),t.type==="css-decl"&&typeof t.prop=="string"&&t.prop.startsWith("--")&&typeof t.value=="string"&&t.value.startsWith("{")){let a;if(t.value.trimEnd().endsWith("}")){let u=e.originalText.slice(0,t.source.start.offset),c="a".repeat(t.prop.length)+e.originalText.slice(t.source.start.offset+t.prop.length,t.source.end.offset),f=E(!1,u,/[^\n]/g," ")+c,p;e.parser==="scss"?p=al:e.parser==="less"?p=ol:p=il;let l;try{l=p(f,{...e})}catch{}((s=l==null?void 0:l.nodes)==null?void 0:s.length)===1&&l.nodes[0].type==="css-rule"&&(a=l.nodes[0].nodes)}return a?t.value={type:"css-rule",nodes:a}:t.value={type:"value-unknown",value:t.raws.value.raw},t}let n="";typeof t.selector=="string"&&(n=t.raws.selector?t.raws.selector.scss??t.raws.selector.raw:t.selector,t.raws.between&&t.raws.between.trim().length>0&&(n+=t.raws.between),t.raws.selector=n);let i="";typeof t.value=="string"&&(i=t.raws.value?t.raws.value.scss??t.raws.value.raw:t.value,i=i.trim(),t.raws.value=i);let o="";if(typeof t.params=="string"&&(o=t.raws.params?t.raws.params.scss??t.raws.params.raw:t.params,t.raws.afterName&&t.raws.afterName.trim().length>0&&(o=t.raws.afterName+o),t.raws.between&&t.raws.between.trim().length>0&&(o=o+t.raws.between),o=o.trim(),t.raws.params=o),n.trim().length>0)return n.startsWith("@")&&n.endsWith(":")?t:t.mixin?(t.selector=ae(n,e),t):(el(t,e)&&(t.isSCSSNesterProperty=!0),t.selector=Z(n),t);if(i.length>0){let a=i.match(Ey);a&&(i=i.slice(0,a.index),t.scssDefault=!0,a[0].trim()!=="!default"&&(t.raws.scssDefault=a[0]));let u=i.match(Sy);if(u&&(i=i.slice(0,u.index),t.scssGlobal=!0,u[0].trim()!=="!global"&&(t.raws.scssGlobal=u[0])),i.startsWith("progid:"))return{type:"value-unknown",value:i};t.value=ae(i,e)}if(e.parser==="less"&&t.type==="css-decl"&&i.startsWith("extend(")&&(t.extend||(t.extend=t.raws.between===":"),t.extend&&!t.selector&&(delete t.value,t.selector=Z(i.slice(7,-1)))),t.type==="css-atrule"){if(e.parser==="less"){if(t.mixin){let a=t.raws.identifier+t.name+t.raws.afterName+t.raws.params;return t.selector=Z(a),delete t.params,t}if(t.function)return t}if(e.parser==="css"&&t.name==="custom-selector"){let a=t.params.match(/:--\S+\s+/)[0].trim();return t.customSelector=a,t.selector=Z(t.params.slice(a.length).trim()),delete t.params,t}if(e.parser==="less"){if(t.name.includes(":")&&!t.params){t.variable=!0;let a=t.name.split(":");t.name=a[0],t.value=ae(a.slice(1).join(":"),e)}if(!["page","nest","keyframes"].includes(t.name)&&((r=t.params)==null?void 0:r[0])===":"){t.variable=!0;let a=t.params.slice(1);a&&(t.value=ae(a,e)),t.raws.afterName+=":"}if(t.variable)return delete t.params,t.value||delete t.value,t}}if(t.type==="css-atrule"&&o.length>0){let{name:a}=t,u=t.name.toLowerCase();return a==="warn"||a==="error"?(t.params={type:"media-unknown",value:o},t):a==="extend"||a==="nest"?(t.selector=Z(o),delete t.params,t):a==="at-root"?(/^\(\s*(?:without|with)\s*:.+\)$/s.test(o)?t.params=ae(o,e):(t.selector=Z(o),delete t.params),t):Zu(u)?(t.import=!0,delete t.filename,t.params=ae(o,e),t):["namespace","supports","if","else","for","each","while","debug","mixin","include","function","return","define-mixin","add-mixin"].includes(a)?(o=o.replace(/(\$\S+?)(\s+)?\.{3}/,"$1...$2"),o=o.replace(/^(?!if)(\S+)(\s+)\(/,"$1($2"),t.value=ae(o,e),delete t.params,t):["media","custom-media"].includes(u)?o.includes("#{")?{type:"media-unknown",value:o}:(t.params=fa(o),t):(t.params=o,t)}}return t}function Qs(t,e,s){let r=Xe(e),{frontMatter:n}=r;e=r.content;let i;try{i=t(e,{map:!1})}catch(o){let{name:a,reason:u,line:c,column:f}=o;throw typeof c!="number"?o:sa(`${a}: ${u}`,{loc:{start:{line:c,column:f}},cause:o})}return s.originalText=e,i=nl(J(i,"css-"),s),Kr(i,e),n&&(n.source={startOffset:0,endOffset:n.raw.length},i.frontMatter=n),i}function il(t,e={}){return Qs(tl.default.default,t,e)}function ol(t,e={}){return Qs(s=>rl.default.parse(bn(s)),t,e)}function al(t,e={}){return Qs(sl.default,t,e)}var js={astFormat:"postcss",hasPragma:qn,locStart:N,locEnd:P},Ty={...js,parse:il},Cy={...js,parse:ol},Ay={...js,parse:al};var Oy={postcss:pi};var P_=Xs;export{P_ as default,hi as languages,mi as options,Js as parsers,Oy as printers};
