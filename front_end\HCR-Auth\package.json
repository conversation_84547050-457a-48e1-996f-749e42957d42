{"name": "bone-conduction-auth", "version": "0.0.0", "private": true, "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron:dev": "concurrently \"npm run dev\" \"electron .\"", "electron:build": "npm run build && electron-builder --win --x64", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.7.2", "element-plus": "^2.7.2", "less": "^4.2.0", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^9.0.0", "concurrently": "^9.1.2", "electron": "^36.5.0", "electron-builder": "^26.0.12", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "prettier": "^3.2.5", "vite": "^5.2.8"}, "build": {"appId": "com.example.bone-conduction-auth", "productName": "骨振识息", "npmRebuild": true, "directories": {"output": "dist_electron"}, "files": ["dist/**/*", "electron/**/*", "pic/**/*"], "win": {"target": "nsis"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}