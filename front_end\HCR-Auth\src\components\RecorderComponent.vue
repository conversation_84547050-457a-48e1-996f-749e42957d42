<template>
  <div class="ctn">
    <div type="primary" class="el-button el-button--primary button-ctn">
      <div v-if="!buttonWord" class="button-sub left" @click="startRecording('left')">身份</div>
      <div v-if="!buttonWord" class="button-sub" @click="startRecording('right')">认证</div>
      <div v-else @click="startRecording()">{{ buttonWord }}</div>
    </div>
    <el-form label-width="80px" inline size="small">
      <el-form-item label="麦克风">
        <el-select
          v-model="selectedDeviceId"
          placeholder="请选择骨振识息麦克风"
          size="small"
          style="width: 160px"
          @change="recorderInit()"
        >
          <el-option
            v-for="item in audioDevices"
            :key="item.deviceId"
            :label="item.label == '麦克风阵列 (适用于数字麦克风的英特尔® 智音技术)' ? '骨振识息外部麦克风' : item.label"
            :value="item.deviceId"
          />
        </el-select> </el-form-item
      ><el-form-item label="扬声器">
        <el-select placeholder="在系统中设置输出设备" disabled size="small" style="width: 160px">
        </el-select>
      </el-form-item>
    </el-form>

    <audio ref="recordedAudio"></audio>
    <audio ref="playAudio"></audio>
    <div style="padding-top: 5px">
      <!-- 波形绘制区域
      <div style="border: 1px solid #ccc; display: inline-block; vertical-align: bottom">
        <div style="height: 100px; width: 300px" ref="recWave"></div>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import verifyWav from '../assets/verify.wav'
import registerWav from '../assets/register.wav'
import '../recorder/recorder.min.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  type: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  message: {
    type: String,
    default: '请输入用户ID'
  },
  buttonWord: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['start', 'finish', 'start-recording'])

const recordedAudio = ref(null)
const playAudio = ref(null)
// const recWave = ref(null)

const isStartRecording = ref(false)

const recorder = ref(null)
// const isRecorderOpen = ref(false)
const audioDevices = ref([])
const selectedDeviceId = ref('')

const getAudioDevices = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    const devices = await navigator.mediaDevices.enumerateDevices()
    audioDevices.value = devices.filter((device) => device.kind === 'audioinput')
    console.log('🚀 ~ getAudioDevices ~ audioDevices.value:', audioDevices.value)

    stream.getTracks().forEach((track) => track.stop()) // 关闭获取权限的流
  } catch (error) {
    console.error('Error accessing audio devices:', error)
  }
}

onMounted(async () => {
  await getAudioDevices()
  if (props.type === 'verify') {
    playAudio.value.src = verifyWav
  } else {
    playAudio.value.src = registerWav
  }
  playAudio.value.addEventListener('ended', () => {
    console.log('音频播放完成')
    // stopRecording()
    // 在这里添加你的代码
  })
  playAudio.value.addEventListener('pause', async () => {
    console.log('音频已暂停')
    playAudio.value.currentTime = 0
    await sleep(500)
    stopRecording()
    // 在这里添加你的代码
  })
})

const recorderInit = async () => {
  if (recorder.value) {
    recorder.value = null
  }
  // console.log('isRecorderOpen设置false')

  const sourceNode = await navigator.mediaDevices
    .getUserMedia({
      audio: {
        deviceId: selectedDeviceId.value ? { exact: selectedDeviceId.value } : undefined,
        noiseSuppression: false, // 禁用降噪
        echoCancellation: false, // 禁用回声消除
        autoGainControl: false // 启用自动增益控制
      }
    })
    .then((stream) => {
      const context = new AudioContext()
      return context.createMediaStreamSource(stream)
    })

  // isRecorderOpen.value = false
  // eslint-disable-next-line no-undef
  recorder.value = new Recorder({
    sourceNode: sourceNode,
    wavBitDepth: 32,
    monitorGain: 0,
    recordingGain: 1,
    mediaTrackConstraints: true,
    numberOfChannels: 2,
    encoderPath: '/waveWorker.min.js'
  })
  console.log('🚀 ~ recorderInit ~ recorder.value:', recorder.value)

  recorder.value.ondataavailable = function (typedArray) {
    var dataBlob = new Blob([typedArray], { type: 'audio/wav' })
    recBlob = dataBlob
    //简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给recordedAudio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
    var localUrl = window.URL.createObjectURL(recBlob)
    console.log('录音成功', recBlob, localUrl)
    console.log('qweqwe', clickType.value)
    emits('finish', recBlob, clickType.value)
    isStartRecording.value = false
    // 下载 blob 文件
    const downloadBlob = (blob, filename) => {
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url) // 释放 URL 对象
      document.body.removeChild(a) // 移除 a 元素
    }
    // downloadBlob(dataBlob, 'recording.wav')
  }
  console.log('🚀 ~ recorderInit ~ clickType.value:', clickType.value)
  console.log('🚀 ~ recorderInit ~ clickType.value:', clickType.value)
}

onUnmounted(() => {
  destroyRecorder()
})

const destroyRecorder = () => {
  if (recorder.value) {
    // recorder.value.close()
    recorder.value = null
    isStartRecording.value = false
    console.log('录音对象已销毁')
  }
}

// const awaitRecorderStart = async () => {
//   return new Promise((resolve, reject) => {
//     let intervalId
//     const timeoutId = setTimeout(() => {
//       clearInterval(intervalId)
//       reject(new Error('Timeout: isRecorderOpen did not become true within 5 seconds'))
//     }, 5000)

//     intervalId = setInterval(() => {
//       if (isRecorderOpen.value) {
//         clearTimeout(timeoutId)
//         clearInterval(intervalId)
//         console.log('录音是true')
//         resolve()
//       }
//     }, 100) // 每 100 毫秒检查一次
//   })
// }

const clickType = ref(undefined)

const startRecording = async () => {
  ElMessageBox.alert('未检测到设备', '提示', {
    confirmButtonText: '确定',
    type: 'warning'
  })
}

const sleep = (time) => {
  const timeout = time ?? 1000
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, timeout)
  })
}

let recBlob = null

const stopRecording = async () => {
  if (!recorder.value) {
    console.error('未打开录音')
    return
  }
  recorder.value.stop()

  // old
  // recorder.value.stop(
  //   async (blob, duration) => {
  //     //blob就是我们要的录音文件对象，可以上传，或者本地播放
  //     recBlob = blob
  //     //简单利用URL生成本地文件地址，此地址只能本地使用，比如赋值给recordedAudio.src进行播放，赋值给a.href然后a.click()进行下载（a需提供download="xxx.mp3"属性）
  //     var localUrl = window.URL.createObjectURL(blob)
  //     console.log('录音成功', recBlob, localUrl, '时长:' + duration + 'ms')
  //     emits('finish', recBlob)

  //     // 下载 blob 文件
  //     const downloadBlob = (blob, filename) => {
  //       const url = window.URL.createObjectURL(blob)
  //       const a = document.createElement('a')
  //       a.style.display = 'none'
  //       a.href = url
  //       a.download = filename
  //       document.body.appendChild(a)
  //       a.click()
  //       window.URL.revokeObjectURL(url) // 释放 URL 对象
  //       document.body.removeChild(a) // 移除 a 元素
  //     }

  //     // 调用下载函数，设置文件名为 "recording.mp3"
  //     // downloadBlob(blob, 'recording.mp3')

  //     // this.upload(recBlob) //把blob文件上传到服务器

  //     destroyRecorder()
  //     await recorderInit()
  //   },
  //   async (err) => {
  //     console.error('结束录音出错：' + err)

  //     destroyRecorder()
  //     await recorderInit()
  //   }
  // )
}
</script>

<style scoped>
.ctn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 10px;
}

.button-ctn {
  min-width: 89px;
  height: 32px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: nowrap;
  padding: 0 !important;
}
.button-sub {
  width: 45px;
  height: 32px;
  display: flex;
  align-items: center;
}
.left {
  justify-content: flex-end;
}
</style>
