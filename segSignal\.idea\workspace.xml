<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="5c8c0842-c2a2-44d1-a527-9ce8e4a2b28c" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectId" id="2kM6b478UZWryggKnQRxsBfEzTN" />
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5c8c0842-c2a2-44d1-a527-9ce8e4a2b28c" name="Default Changelist" comment="" />
      <created>1723081166619</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1723081166619</updated>
      <workItem from="1723081168152" duration="610000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
</project>