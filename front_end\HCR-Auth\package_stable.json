{"name": "bone-conduction-auth", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.6.0", "element-plus": "^2.4.0", "less": "^4.2.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "vite": "^4.5.0", "eslint": "^8.50.0", "prettier": "^3.0.0"}}