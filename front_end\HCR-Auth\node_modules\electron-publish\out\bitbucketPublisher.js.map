{"version": 3, "file": "bitbucketPublisher.js", "sourceRoot": "", "sources": ["../src/bitbucketPublisher.ts"], "names": [], "mappings": ";;;AAAA,+CAAkG;AAClG,+DAA4E;AAE5E,sCAAqC;AACrC,uCAAmC;AAGnC,mDAA+C;AAE/C,MAAa,kBAAmB,SAAQ,6BAAa;IAQnD,YAAY,OAAuB,EAAE,IAAsB;QACzD,KAAK,CAAC,OAAO,CAAC,CAAA;QARP,iBAAY,GAAG,WAAW,CAAA;QAC1B,aAAQ,GAAG,mBAAmB,CAAA;QASrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,IAAI,CAAA;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI,CAAA;QAExE,IAAI,IAAA,8BAAe,EAAC,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,wCAAyB,CAAC,kHAAkH,CAAC,CAAA;QACzJ,CAAC;QAED,IAAI,IAAA,8BAAe,EAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,kBAAG,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAA;QACpG,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QACrF,IAAI,CAAC,QAAQ,GAAG,qBAAqB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,YAAY,CAAA;IACpF,CAAC;IAES,QAAQ,CAChB,QAAgB,EAChB,KAAW,EACX,WAAmB,EACnB,iBAAmF,EACnF,IAAY;QAEZ,OAAO,mCAAY,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;YAChD,MAAM,WAAW,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,CAAA;YACxC,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAA;YAC3B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAA;YAC3C,MAAM,MAAM,GAAmB;gBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,QAAQ;gBACnB,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,SAAS;aACxC,CAAA;YACD,MAAM,2BAAY,CAAC,YAAY,CAAC,IAAA,8CAAuB,EAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;YACxI,OAAO,QAAQ,CAAA;QACjB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,MAAM,GAAG,GAAmB;YAC1B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ,EAAE;YACpC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,SAAS;SACxC,CAAA;QACD,MAAM,2BAAY,CAAC,OAAO,CAAC,IAAA,8CAAuB,EAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;IAC/G,CAAC;IAED,QAAQ;QACN,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAA;QAC1C,OAAO,qBAAqB,KAAK,WAAW,IAAI,cAAc,OAAO,GAAG,CAAA;IAC1E,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,QAAgB,EAAE,KAAa;QACvD,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QACvF,OAAO,SAAS,iBAAiB,EAAE,CAAA;IACrC,CAAC;CACF;AAnED,gDAmEC", "sourcesContent": ["import { Arch, httpExecutor, InvalidConfigurationError, isEmptyOrSpaces, log } from \"builder-util\"\nimport { configureRequestOptions, HttpExecutor } from \"builder-util-runtime\"\nimport { BitbucketOptions } from \"builder-util-runtime/out/publishOptions\"\nimport * as FormData from \"form-data\"\nimport { readFile } from \"fs-extra\"\nimport { ClientRequest, RequestOptions } from \"http\"\nimport { PublishContext } from \"./\"\nimport { HttpPublisher } from \"./httpPublisher\"\n\nexport class BitbucketPublisher extends HttpPublisher {\n  readonly providerName = \"bitbucket\"\n  readonly hostname = \"api.bitbucket.org\"\n\n  private readonly info: BitbucketOptions\n  private readonly auth: string\n  private readonly basePath: string\n\n  constructor(context: PublishContext, info: BitbucketOptions) {\n    super(context)\n\n    const token = info.token || process.env.BITBUCKET_TOKEN || null\n    const username = info.username || process.env.BITBUCKET_USERNAME || null\n\n    if (isEmptyOrSpaces(token)) {\n      throw new InvalidConfigurationError(`Bitbucket token is not set using env \"BITBUCKET_TOKEN\" (see https://www.electron.build/publish#BitbucketOptions)`)\n    }\n\n    if (isEmptyOrSpaces(username)) {\n      log.warn('No Bitbucket username provided via \"BITBUCKET_USERNAME\". Defaulting to use repo owner.')\n    }\n\n    this.info = info\n    this.auth = BitbucketPublisher.convertAppPassword(username ?? this.info.owner, token)\n    this.basePath = `/2.0/repositories/${this.info.owner}/${this.info.slug}/downloads`\n  }\n\n  protected doUpload(\n    fileName: string,\n    _arch: Arch,\n    _dataLength: number,\n    _requestProcessor: (request: ClientRequest, reject: (error: Error) => void) => void,\n    file: string\n  ): Promise<any> {\n    return HttpExecutor.retryOnServerError(async () => {\n      const fileContent = await readFile(file)\n      const form = new FormData()\n      form.append(\"files\", fileContent, fileName)\n      const upload: RequestOptions = {\n        hostname: this.hostname,\n        path: this.basePath,\n        headers: form.getHeaders(),\n        timeout: this.info.timeout || undefined,\n      }\n      await httpExecutor.doApiRequest(configureRequestOptions(upload, this.auth, \"POST\"), this.context.cancellationToken, it => form.pipe(it))\n      return fileName\n    })\n  }\n\n  async deleteRelease(filename: string): Promise<void> {\n    const req: RequestOptions = {\n      hostname: this.hostname,\n      path: `${this.basePath}/${filename}`,\n      timeout: this.info.timeout || undefined,\n    }\n    await httpExecutor.request(configureRequestOptions(req, this.auth, \"DELETE\"), this.context.cancellationToken)\n  }\n\n  toString() {\n    const { owner, slug, channel } = this.info\n    return `Bitbucket (owner: ${owner}, slug: ${slug}, channel: ${channel})`\n  }\n\n  static convertAppPassword(username: string, token: string) {\n    const base64encodedData = Buffer.from(`${username}:${token.trim()}`).toString(\"base64\")\n    return `Basic ${base64encodedData}`\n  }\n}\n"]}