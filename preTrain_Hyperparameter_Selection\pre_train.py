# -*- coding:utf-8 -*-
from datasets import SiameseMNIST
from get_dataSet import MyDataset, MySubset
import random
import torch

from losses import ContrastiveLoss
from networks import SiameseNet, EmbeddingNet

from torch.optim import lr_scheduler
import torch.optim as optim

from trainer import fit

if __name__ == '__main__':

    dataset = MyDataset('/root/project/Paper2/dataset')

    # 划分训练集和测试集
    train_indices = []
    test_indices = []
    for i in range(46, 61):
        indices = [j for j, label in enumerate(dataset.labels) if label == i]
        test_indices.extend(random.sample(indices, 50))  # 随机选择50个样本作为测试集
        train_indices.extend([index for index in indices if index not in test_indices])  # 其余样本作为训练集

    # 创建训练集和测试集
    train_dataset = MySubset(dataset, train_indices, train=True)
    test_dataset = MySubset(dataset, test_indices, train=False)

    # 开始训练
    # 设置待调优的参数
    # dropRate = 0.2  # 0.2 0.25 0.3 0.35 0.4 0.45 0.5 0.55 0.6
    # lr = 0.1 # 0.1 0.05 0.01 0.005 0.001 0.0005 0.0001 0.00005 0.00001
    # batch_size = 16 # 16 32 64 128 256

    # 加载模型数据
    siamese_train_dataset = SiameseMNIST(train_dataset)  # Returns pairs of images and target same/different
    siamese_test_dataset = SiameseMNIST(test_dataset)
    cuda = torch.cuda.is_available()
    kwargs = {'num_workers': 1, 'pin_memory': True} if cuda else {}

    batchSize_list = [8, 16, 32, 64, 128]
    lr_list = [0.1, 0.01, 0.001, 0.0001]
    dropRate_list = [0.2, 0.3, 0.4, 0.5, 0.6]
    for i in range(len(batchSize_list)):
        for j in range(len(lr_list)):
            for k in range(len(dropRate_list)):
                batchSize = batchSize_list[i]
                lr = lr_list[j]
                dropRate = dropRate_list[k]
                siamese_train_loader = torch.utils.data.DataLoader(siamese_train_dataset, batch_size=batchSize, shuffle=False, **kwargs)
                siamese_test_loader = torch.utils.data.DataLoader(siamese_test_dataset, batch_size=batchSize, shuffle=False, **kwargs)

                # 加载模型
                margin = 1.
                embedding_net = EmbeddingNet(dropRate)
                model = SiameseNet(embedding_net)
                if cuda:
                    model.cuda()
                #设置损失函数 优化器
                loss_fn = ContrastiveLoss(margin)
                # loss_fn = TripletLoss(margin)
                optimizer = optim.Adam(model.parameters(), lr=lr)
                scheduler = lr_scheduler.StepLR(optimizer, step_size=20, gamma=1, last_epoch=-1) #每经过step_size轮次，学习率乘以gamma
                n_epochs = 45 #原值是20
                log_interval = 100

                message = '\nbatch_size: {}, lr: {}, dropRate: {}. '.format(batchSize, lr, dropRate)
                print(message)
                fit(siamese_train_loader, siamese_test_loader, model, loss_fn, optimizer, scheduler, n_epochs, cuda, log_interval)




