# -*- coding: utf-8 -*-
"""
配置文件
"""

import os
from pathlib import Path

class Config:
    # 基础配置
    BASE_DIR = Path(__file__).parent.parent
    
    # 服务器配置
    HOST = '127.0.0.1'
    PORT = 5000
    DEBUG = True
    
    # 文件存储配置
    UPLOAD_FOLDER = BASE_DIR / 'data' / 'uploads'
    MODEL_FOLDER = BASE_DIR / 'data' / 'models'
    LOG_FOLDER = BASE_DIR / 'logs'
    TEMP_FOLDER = BASE_DIR / 'data' / 'temp'
    
    # 文件大小限制 (16MB)
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'wav', 'txt'}
    
    # 数据处理配置
    FRAME_RATE = 467
    THRESHOLD_FREQUENCY = 20
    CYCLE_NUM = 55
    
    # 模型配置
    EMBEDDING_DIM = 32
    DROPOUT_RATE = 0.2
    CONTRASTIVE_MARGIN = 2.0
    
    # 孪生网络配置
    SIAMESE_THRESHOLD = 0.5  # 相似度阈值
    
    @classmethod
    def get_flask_config(cls):
        """获取Flask配置"""
        return {
            'MAX_CONTENT_LENGTH': cls.MAX_CONTENT_LENGTH,
            'UPLOAD_FOLDER': str(cls.UPLOAD_FOLDER),
            'SECRET_KEY': 'hcr-auth-secret-key-2024'
        }
    
    @classmethod
    def ensure_directories(cls):
        """确保所有必要的目录存在"""
        directories = [
            cls.UPLOAD_FOLDER,
            cls.MODEL_FOLDER,
            cls.LOG_FOLDER,
            cls.TEMP_FOLDER
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
