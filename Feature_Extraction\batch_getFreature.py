# -*- coding:utf-8 -*-

import scipy.io.wavfile as wav
from scipy.signal import resample

from get_Feature import get_Feature
import os

if __name__ == '__main__':

    input_file = "50_800_W.wav"
    # frameRate = 467
    # frameRate = 426
    frameRate = 467
    # 读取原始音频文件
    original_sampling_rate, audio_data = wav.read(input_file)
    # 计算采样率的比例
    sampling_rate_ratio = frameRate / original_sampling_rate
    # 计算目标音频数据的长度
    target_length = int(len(audio_data) * sampling_rate_ratio)
    # 执行音频重采样
    resampled_audio = resample(audio_data, target_length)

    Userlist = [1,4,5,17,20]
    for UserNum in Userlist:

        source_filename = "/Earphone_IMU/DataSetPreprocess/head_movement/segment/static/" + str(UserNum)
        target_filename = "/Earphone_IMU/DataSetPreprocess/head_movement/feature/static/" + str(UserNum)

        print("UserNum:", UserNum)

        for i in range(48):
            Ltxt = source_filename + "/L_" + str(i+1) + ".txt"
            Rtxt = source_filename + "/R_" + str(i+1) + ".txt"
            target_path = target_filename + "/" + str(i+1) + ".txt"

            # 获取目标文件的目录
            target_dir = os.path.dirname(target_path)
            # 如果目录不存在，则创建目录
            if not os.path.exists(target_dir):
                os.makedirs(target_dir)

            #特征计算
            tfL, tfR, corr = get_Feature(Ltxt, Rtxt, resampled_audio, frameRate)
            feature = tfL + tfR + corr

            # print("len(tfL):", len(tfL))
            # print("len(tfR):", len(tfR))
            # print("len(corr):", len(corr))

            # 特征写入
            with open(target_path, 'w') as file:
                for item in feature:
                    file.write(str(item) + '\n')





