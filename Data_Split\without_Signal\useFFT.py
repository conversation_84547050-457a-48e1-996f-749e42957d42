# -*-coding:utf-8 -*-
#coding: UTF-8

import numpy as np
from scipy.fftpack import fft
import matplotlib.pyplot as plt
from scipy.signal import butter, lfilter

from Event_Detection import read_date

def zeroMeanNorm(data):
    mean = np.mean(data)
    std = np.std(data)
    for i in range(len(data)):
        data[i] = (data[i]-mean)/std
    return data

def FFT (Fs,data):
    print("Fs:",Fs)
    L = len (data)                        # 信号长度
    print("L:",L)
    N =int(np.power(2,np.ceil(np.log2(L))))    # 下一个最近二次幂
    print("N:", N)
    FFT_y1 = np.abs(fft(data,N))/L*2      # N点FFT 变化,但除以信号长度
    print("len(FFT_y1):",len(FFT_y1))
    Fre = np.arange(int(N/2))*Fs/N        # 频率坐标
    print("len(Fre):",len(Fre))
    print("Fre[-1]:", Fre[-1])
    print("np.arange(int(N / 2)):", np.arange(int(N / 2)))
    FFT_y1 = FFT_y1[range(int(N/2))]      # 取一半
    return Fre, FFT_y1


if __name__ == '__main__':


    Fs =467      # 采样频率
    # f1 =390       # 信号频率1
    # f2 = 2e3      # 信号频率2
    # t=np.linspace(0,1,Fs)   # 生成 1s 的实践序列
    # noise1 = np.random.random(1000)      # 0-1 之间的随机噪声
    # noise2 = np.random.normal(1,10,1000)
    # #产生的是一个10e3的高斯噪声点数组集合（均值为：1，标准差：10）
    # # y=2*np.sin(2*np.pi*f1*t)+5*np.sin(2*np.pi*f2*t)+noise2
    # y=2*np.sin(2*np.pi*f1*t)+5*np.sin(2*np.pi*f2*t)

    # 读取数据
    dataX, dataY, dataZ = read_date("R_Walk.txt")
    current_axis = dataZ
    # current_axis = zeroMeanNorm(current_axis)

    #高通滤波
    cutoff_freq = 20  # 截止频率
    sampling_rate = 469  # 采样率
    filter_order = 4  # 滤波器阶数
    nyquist_freq = 0.5 * sampling_rate
    cutoff = cutoff_freq / nyquist_freq
    b, a = butter(filter_order, cutoff, btype='highpass')
    # 对每列数据应用高通滤波器
    # current_axis = np.zeros_like(current_axis)
    current_axis = lfilter(b, a, current_axis)

    Fre, FFT_y1 = FFT(Fs,current_axis)
    print(len(Fre))

    plt.plot(Fre,FFT_y1)
    plt.grid()
    plt.show()