@echo off
chcp 65001 >nul
title 前端依赖安装 - 无需管理员权限

echo ================================================================
echo 🎨 HCR-Auth 前端依赖安装 (无需管理员权限)
echo ================================================================
echo.

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查package.json...
if not exist package.json (
    echo ❌ 未找到package.json文件
    echo 请确保在 front_end\HCR-Auth 目录中运行
    pause
    exit /b 1
)
echo ✅ 找到package.json文件
echo.

echo 🔧 配置npm使用用户缓存目录...
npm config set cache "%USERPROFILE%\npm-cache"
npm config set prefix "%USERPROFILE%\npm-global"
echo ✅ npm配置完成
echo.

echo 🧹 清理旧文件...
if exist node_modules (
    echo 删除 node_modules...
    rmdir /s /q node_modules 2>nul
)
if exist package-lock.json (
    echo 删除 package-lock.json...
    del package-lock.json 2>nul
)
echo ✅ 清理完成
echo.

echo 📦 开始安装依赖...
echo 使用国内镜像源，请耐心等待...
echo.

REM 方法1：使用淘宝镜像
echo 🔄 尝试方法1: 淘宝镜像...
npm install --registry=https://registry.npmmirror.com --no-optional --no-audit --no-fund --timeout=300000

if %errorlevel% equ 0 (
    echo ✅ 安装成功！
    goto success
)

echo ❌ 方法1失败，尝试方法2...
echo.

REM 方法2：使用官方源但跳过可选依赖
echo 🔄 尝试方法2: 官方源...
npm install --no-optional --no-audit --no-fund --timeout=600000

if %errorlevel% equ 0 (
    echo ✅ 安装成功！
    goto success
)

echo ❌ 方法2失败，尝试方法3...
echo.

REM 方法3：最小化安装
echo 🔄 尝试方法3: 最小化安装...
npm install --registry=https://registry.npmmirror.com --no-optional --no-audit --no-fund --no-shrinkwrap --no-package-lock --ignore-scripts

if %errorlevel% equ 0 (
    echo ✅ 安装成功！
    goto success
)

echo ❌ 所有方法都失败了
echo.
echo 💡 建议:
echo 1. 以管理员身份运行PowerShell
echo 2. 或者使用手机热点尝试
echo 3. 或者暂时跳过前端，只使用后端
echo.
goto end

:success
echo.
echo 🎉 前端依赖安装成功！
echo.
echo 🚀 现在可以启动前端服务:
echo npm run dev
echo.
echo 📍 前端地址: http://localhost:5173
echo ================================================================
echo.

set /p start="是否立即启动前端服务? (y/n): "
if /i "%start%"=="y" (
    echo.
    echo 🚀 启动前端服务...
    npm run dev
)

:end
pause
