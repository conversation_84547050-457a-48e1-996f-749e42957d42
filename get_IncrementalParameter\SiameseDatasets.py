import numpy as np
from PIL import Image

from torch.utils.data import Dataset
from torch.utils.data.sampler import BatchSampler
import torch

class SiameseMNIST(Dataset):
    """
    Train: For each sample creates randomly a positive or a negative pair
    Test: Creates fixed pairs for testing
    """

    def __init__(self, mnist_dataset):
        self.mnist_dataset = mnist_dataset

        # 属性赋值
        self.train = self.mnist_dataset.train #分别对应True, false
        self.UserID = 46

        # 如果数据集用于训练
        if self.train:
            self.labels = self.mnist_dataset.labels # Tensor 60000*1
            # print("len(self.labels):", len(self.labels))
            self.samples = self.mnist_dataset.samples # Tensor 60000*28*28
            # 将训练标签转换为set类型，由于set中数据不重复，这里实际上得到了所有的标签类别
            self.labels_set = set(self.labels.numpy()) # set, 包含0-9共10个元素
            # 将每个类别对应的样本的下标存储到self.label_to_indices中
            # dict类型, 长度为10，比如key为0，value值对应一个array，长度为5923，对应着60000个样本中label=0的样本下标
            self.label_to_indices = {label: np.where(self.labels.numpy() == label)[0]
                                     for label in self.labels_set}

        else:
            # generate fixed pairs for testing
            self.labels = self.mnist_dataset.labels
            self.samples = self.mnist_dataset.samples
            self.labels_set = set(self.labels.numpy())
            self.label_to_indices = {label: np.where(self.labels.numpy() == label)[0]
                                     for label in self.labels_set}

            random_state = np.random.RandomState(29) #初始化一个随机数生成器

            # 创建一组测试数据对，其中每个数据对由两个来自同一标签的图像组成[给出其样本下标]
            # positive_pairs,list类型，长度5000，内容举例[[0, 2285, 1], [2, 4516, 1]……[9998, 1970, 1]], [样本1下标, 样本2下标, 标签(1表示相同样本)]
            positive_pairs = [[i,
                               random_state.choice(self.label_to_indices[self.labels[i].item()]),
                               1]
                              for i in range(0, len(self.samples), 2)]

            # 创建一组测试数据对，其中每个数据对由两个来自不同标签的图像组成[给出其样本下标]
            # negative_pairs, list类型，长度5000，内容举例[[1, 1908, 0], [3, 3002, 0]……[9999, 2105, 0]]
            negative_pairs = [[i,
                               random_state.choice(self.label_to_indices[ # 在dict中，以该label为key，获得对应array，即10000个样本中相同label的数据下标
                                                       np.random.choice(#在长度为10的set中删除目标label，然后生成list，之后随机取一个值(0-9)
                                                           list(self.labels_set - set([self.labels[i].item()]))
                                                       )
                                                   ]), # 在array的下标中随机取一个值
                               0]
                              for i in range(1, len(self.samples), 2)] #从1开始，步长为2，到10000为止的整数序列

            self.test_pairs = positive_pairs + negative_pairs #两个list连接成一个，长度变为1000
            # print("type(self.test_pairs):", type(self.test_pairs))
            # print("len(self.test_pairs):", len(self.test_pairs))
            # print("self.test_pairs:", self.test_pairs)


    def __getitem__(self, index):
        # 从这里来看，训练时根据当前样本标签，随机选择与其标签相同/不同的样本
        # 测试时, 则是从一个构建好的样本对中选择[测试集共1000个样本，其中5K个作为anchor构建正样本对，另外5K作为anchor构建负样本对],这样保证测试的全面性
        if self.train:
            target = np.random.randint(0, 2) #从0、1中随机选择一个整数
            # 获得对应下标的样本
            sample1, label1 = self.samples[index], self.labels[index].item()
            if target == 1: #需要输出同类样本
                siamese_index = index
                while siamese_index == index:
                    siamese_index = np.random.choice(self.label_to_indices[label1]) #随机选择一个相同类别的样本的下标

            else: #需要输出不同类样本
                siamese_label = np.random.choice(list(self.labels_set - set([label1])))
                siamese_index = np.random.choice(self.label_to_indices[siamese_label]) #随机选择一个不同类别的样本的下标
            sample2 = self.samples[siamese_index] #获得对应下标的样本
        else: # 测试模式
            # test_pairs：[[0, 2285, 1], [2, 4516, 1]……[9997, 7244, 0], [9999, 9390, 0]]
            # 这里获得两个样本以及标记target，1是指相同类样本，0是指不同类样本
            sample1 = self.samples[self.test_pairs[index][0]]
            sample2 = self.samples[self.test_pairs[index][1]]
            target = self.test_pairs[index][2]

        # print("sample21.shape:", sample2.shape)
        return (sample1, sample2), target


    def __len__(self):
        return len(self.mnist_dataset)



class IncrementalDataset(Dataset):
    def __init__(self, train_User, Defult_User, UserID):
        self.train_User = train_User
        self.Defult_User = Defult_User
        self.UserID = UserID
        self.train_User_samples = self.train_User.samples
        self.train_User_labels = self.train_User.labels
        self.Defult_User_samples = self.Defult_User.samples
        self.Defult_User_labels = self.Defult_User.labels

    def __getitem__(self, index):
        target = np.random.randint(0, 2) #从0、1中随机选择一个整数
        # 获得一个合法用户样本
        sample1, label1 = self.train_User_samples[index], self.train_User_labels[index].item()
        if target == 1:  # 需要输出同类样本
            sample2 = self.train_User_samples[torch.randint(0, self.train_User_samples.size(0), (1,))]

        else:  # 需要输出不同类样本
            sample2 = self.Defult_User_samples[torch.randint(0, self.Defult_User_samples.size(0), (1,))]
        # 将sample2的[1,1,480]维度转换为[1,480]
        sample2 = torch.reshape(sample2, (1, -1))
        # print("sample22.shape:", sample2.shape)
        return (sample1, sample2), target


    def __len__(self):
        return len(self.train_User)





class TripletMNIST(Dataset):
    """
    Train: For each sample (anchor) randomly chooses a positive and negative samples
    Test: Creates fixed triplets for testing
    """

    def __init__(self, mnist_dataset):
        self.mnist_dataset = mnist_dataset
        self.train = self.mnist_dataset.train
        self.transform = self.mnist_dataset.transform

        if self.train:
            self.train_labels = self.mnist_dataset.train_labels
            self.train_data = self.mnist_dataset.train_data
            self.labels_set = set(self.train_labels.numpy())
            self.label_to_indices = {label: np.where(self.train_labels.numpy() == label)[0]
                                     for label in self.labels_set}

        else:
            self.test_labels = self.mnist_dataset.test_labels
            self.test_data = self.mnist_dataset.test_data
            # generate fixed triplets for testing
            self.labels_set = set(self.test_labels.numpy())
            self.label_to_indices = {label: np.where(self.test_labels.numpy() == label)[0]
                                     for label in self.labels_set}

            random_state = np.random.RandomState(29)

            triplets = [[i,
                         random_state.choice(self.label_to_indices[self.test_labels[i].item()]),
                         random_state.choice(self.label_to_indices[
                                                 np.random.choice(
                                                     list(self.labels_set - set([self.test_labels[i].item()]))
                                                 )
                                             ])
                         ]
                        for i in range(len(self.test_data))]
            self.test_triplets = triplets

    def __getitem__(self, index):
        if self.train:
            img1, label1 = self.train_data[index], self.train_labels[index].item()
            positive_index = index
            while positive_index == index:
                positive_index = np.random.choice(self.label_to_indices[label1])
            negative_label = np.random.choice(list(self.labels_set - set([label1])))
            negative_index = np.random.choice(self.label_to_indices[negative_label])
            img2 = self.train_data[positive_index]
            img3 = self.train_data[negative_index]
        else:
            img1 = self.test_data[self.test_triplets[index][0]]
            img2 = self.test_data[self.test_triplets[index][1]]
            img3 = self.test_data[self.test_triplets[index][2]]

        img1 = Image.fromarray(img1.numpy(), mode='L')
        img2 = Image.fromarray(img2.numpy(), mode='L')
        img3 = Image.fromarray(img3.numpy(), mode='L')
        if self.transform is not None:
            img1 = self.transform(img1)
            img2 = self.transform(img2)
            img3 = self.transform(img3)
        return (img1, img2, img3), []

    def __len__(self):
        return len(self.mnist_dataset)


class BalancedBatchSampler(BatchSampler):
    """
    BatchSampler - from a MNIST-like dataset, samples n_classes and within these classes samples n_samples.
    Returns batches of size n_classes * n_samples
    """

    def __init__(self, labels, n_classes, n_samples):
        self.labels = labels
        self.labels_set = list(set(self.labels.numpy()))
        self.label_to_indices = {label: np.where(self.labels.numpy() == label)[0]
                                 for label in self.labels_set}
        for l in self.labels_set:
            np.random.shuffle(self.label_to_indices[l])
        self.used_label_indices_count = {label: 0 for label in self.labels_set}
        self.count = 0
        self.n_classes = n_classes
        self.n_samples = n_samples
        self.n_dataset = len(self.labels)
        self.batch_size = self.n_samples * self.n_classes

    def __iter__(self):
        self.count = 0
        while self.count + self.batch_size < self.n_dataset:
            classes = np.random.choice(self.labels_set, self.n_classes, replace=False)
            indices = []
            for class_ in classes:
                indices.extend(self.label_to_indices[class_][
                               self.used_label_indices_count[class_]:self.used_label_indices_count[
                                                                         class_] + self.n_samples])
                self.used_label_indices_count[class_] += self.n_samples
                if self.used_label_indices_count[class_] + self.n_samples > len(self.label_to_indices[class_]):
                    np.random.shuffle(self.label_to_indices[class_])
                    self.used_label_indices_count[class_] = 0
            yield indices
            self.count += self.n_classes * self.n_samples

    def __len__(self):
        return self.n_dataset // self.batch_size
