#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的启动脚本 - 仅启动后端
适用于开发环境
"""

import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
backend_dir = project_root / "backend"
sys.path.insert(0, str(backend_dir))

print("=" * 50)
print("🦴 骨振识息系统 - 后端服务")
print("=" * 50)

# 检查依赖
try:
    import flask
    import torch
    import numpy
    import scipy
    print("✅ 主要依赖检查通过")
except ImportError as e:
    print(f"❌ 缺少依赖: {e}")
    print("请运行: pip install flask torch numpy scipy flask-cors")
    sys.exit(1)

# 启动后端
try:
    os.chdir(backend_dir)
    from app import app
    from utils.config import Config
    
    print(f"🚀 启动后端服务: http://localhost:{Config.PORT}")
    print("💡 前端请手动启动: cd front_end/HCR-Auth && npm run dev")
    print("🛑 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=True
    )
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1)
