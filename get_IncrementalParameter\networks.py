# -*- coding:utf-8 -*-

import torch
import torch.nn as nn

class EmbeddingNet(nn.Module):
    def __init__(self, dropRate = 0.2):
        super(EmbeddingNet, self).__init__()
        self.convnet = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Conv1d(32, 64, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            nn.Dropout(p=dropRate),
            nn.Flatten(),
            nn.Linear(15360, 32))

    def forward(self, x):
        # print("x.shape:", x.shape)
        output = self.convnet(x)
        output = output.view(output.size()[0], -1)
        return output

class SiameseNet(nn.Module):
    def __init__(self, embedding_net):
        super(SiameseNet, self).__init__()
        self.embedding_net = embedding_net

    def forward(self, x1, x2):
        output1 = self.embedding_net(x1)
        output2 = self.embedding_net(x2)
        return output1, output2

    def get_embedding(self, x):
        return self.embedding_net(x)



