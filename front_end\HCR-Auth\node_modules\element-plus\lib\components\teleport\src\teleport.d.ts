import type { ExtractPropTypes, StyleValue } from 'vue';
import type Teleport from './teleport.vue';
export declare const teleportProps: {
    readonly container: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string & {}) | (() => string) | ((new (...args: any[]) => string & {}) | (() => string))[], unknown, unknown, "body", boolean>;
    readonly disabled: import("element-plus/es/utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    readonly style: {
        readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => StyleValue & {}) | (() => StyleValue) | ((new (...args: any[]) => StyleValue & {}) | (() => StyleValue))[], unknown, unknown>>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly zIndex: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "2000", boolean>;
};
export declare type TeleportProps = ExtractPropTypes<typeof teleportProps>;
export declare type TeleportInstance = InstanceType<typeof Teleport>;
