#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接启动Flask服务器
最简单的启动方式
"""

import os
import sys
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent
backend_dir = project_root / "backend"

# 切换到后端目录
os.chdir(backend_dir)

# 添加后端目录到Python路径
sys.path.insert(0, str(backend_dir))

print("=" * 60)
print("🦴 骨振识息系统 - 后端服务")
print("=" * 60)
print(f"📁 工作目录: {os.getcwd()}")
print(f"🐍 Python路径: {sys.executable}")

try:
    # 直接运行app.py，指定UTF-8编码
    with open('app.py', 'r', encoding='utf-8') as f:
        exec(f.read())
except Exception as e:
    print(f"❌ 启动失败: {e}")
    print(f"错误类型: {type(e).__name__}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")
    sys.exit(1)
