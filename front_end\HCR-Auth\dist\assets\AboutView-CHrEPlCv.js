import{_ as w,r as n,c as x,a as l,w as i,b as F,R as V,E as I,u as S,d as p,g as U,e as f,o as b,f as u}from"./index-DrE6AAwV.js";const B={style:{display:"flex","flex-direction":"row",padding:"4px"}},D={__name:"AboutView",setup(E){const _=n(!1),m=n(!1),g=n(!1),t=n(null);let d;const h=async(o,a)=>{if(console.log("🚀 ~ handleFinish ~ qq:",a),!o)return;_.value=!0,I({type:"success",message:"采集成功，分析中。。。",plain:!0});const s=crypto.randomUUID(),r=new File([o],s+".wav",{type:"audio/wave"});try{let e;console.log("fake",e),a=="left"?e="0":a=="right"&&(e="1");const{data:c}=await S("login",r,t.value,void 0,e);console.log("🚀 ~ handleFinish ~ response:",c),d=c.data.id,await y()}catch(e){console.error("🚀 ~ handleFinish ~ error:",e),p.alert("验证失败","验证结果",{type:"error"})}},v=()=>{g.value=!0},y=async()=>{t.value&&(await U("login",t.value,d),m.value=!0,p.alert("验证成功","验证结果",{type:"success"}))};return(o,a)=>{const s=f("el-text"),r=f("el-input");return b(),x("main",null,[l(s,null,{default:i(()=>[u("请正确佩戴骨传导耳机")]),_:1}),l(s,null,{default:i(()=>[u("请输入您的id并点击下方按钮开始对您的身份进行认证")]),_:1}),F("div",B,[l(s,{type:"primary"},{default:i(()=>[u("ID：")]),_:1}),l(r,{modelValue:t.value,"onUpdate:modelValue":a[0]||(a[0]=e=>t.value=e),size:"small",placeholder:"请输入ID"},null,8,["modelValue"])]),l(V,{type:"verify",disabled:!t.value,onFinish:h,onStart:v},null,8,["disabled"])])}}},C=w(D,[["__scopeId","data-v-3a978bc3"]]);export{C as default};
