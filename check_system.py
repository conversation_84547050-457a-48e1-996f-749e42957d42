#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HCR-Auth 系统检查脚本
检查系统完整性和依赖
"""

import os
import sys
import subprocess
from pathlib import Path
import importlib.util

# 项目路径
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "front_end" / "HCR-Auth"

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_node_version():
    """检查Node.js版本"""
    print("📦 检查Node.js版本...")
    
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version}")
            return True
        else:
            print("❌ Node.js未安装或无法访问")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False

def check_python_dependencies():
    """检查Python依赖"""
    print("📚 检查Python依赖...")
    
    requirements_file = BACKEND_DIR / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    missing_packages = []
    
    with open(requirements_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                package_name = line.split('==')[0].split('>=')[0].split('<=')[0]
                
                try:
                    importlib.import_module(package_name.replace('-', '_'))
                    print(f"  ✅ {package_name}")
                except ImportError:
                    print(f"  ❌ {package_name}")
                    missing_packages.append(package_name)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("   运行: pip install -r backend/requirements.txt")
        return False
    
    print("✅ 所有Python依赖已安装")
    return True

def check_node_dependencies():
    """检查Node.js依赖"""
    print("📦 检查Node.js依赖...")
    
    package_json = FRONTEND_DIR / "package.json"
    node_modules = FRONTEND_DIR / "node_modules"
    
    if not package_json.exists():
        print("❌ package.json文件不存在")
        return False
    
    if not node_modules.exists():
        print("❌ node_modules目录不存在")
        print("   运行: cd front_end/HCR-Auth && npm install")
        return False
    
    print("✅ Node.js依赖已安装")
    return True

def check_file_structure():
    """检查文件结构"""
    print("📁 检查文件结构...")
    
    required_files = [
        "backend/app.py",
        "backend/run.py",
        "backend/core/data_processor.py",
        "backend/core/model_inference.py",
        "backend/core/user_manager.py",
        "backend/core/signal_processing.py",
        "backend/utils/config.py",
        "backend/utils/logger.py",
        "front_end/HCR-Auth/src/App.vue",
        "front_end/HCR-Auth/src/views/HomeView.vue",
        "front_end/HCR-Auth/src/views/AboutView.vue",
        "front_end/HCR-Auth/package.json",
        "front_end/HCR-Auth/vite.config.js"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        full_path = PROJECT_ROOT / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {len(missing_files)}个")
        return False
    
    print("✅ 文件结构完整")
    return True

def check_algorithm_files():
    """检查算法文件"""
    print("🧮 检查算法文件...")
    
    algorithm_dirs = [
        "Data_Split",
        "Feature_Extraction",
        "preTrain_Hyperparameter_Selection",
        "get_IncrementalParameter"
    ]
    
    missing_dirs = []
    
    for dir_name in algorithm_dirs:
        dir_path = PROJECT_ROOT / dir_name
        if dir_path.exists() and dir_path.is_dir():
            print(f"  ✅ {dir_name}/")
        else:
            print(f"  ❌ {dir_name}/")
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"⚠️  缺少算法目录: {', '.join(missing_dirs)}")
        print("   系统仍可运行，但可能影响某些功能")
    else:
        print("✅ 算法文件完整")
    
    return len(missing_dirs) == 0

def check_ports():
    """检查端口占用"""
    print("🔌 检查端口占用...")
    
    import socket
    
    ports_to_check = [5000, 5173]
    occupied_ports = []
    
    for port in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"  ⚠️  端口 {port} 已被占用")
            occupied_ports.append(port)
        else:
            print(f"  ✅ 端口 {port} 可用")
    
    if occupied_ports:
        print(f"⚠️  端口占用: {', '.join(map(str, occupied_ports))}")
        print("   请关闭占用端口的程序或修改配置")
        return False
    
    print("✅ 端口检查通过")
    return True

def generate_report(checks):
    """生成检查报告"""
    print("\n" + "=" * 60)
    print("📊 系统检查报告")
    print("=" * 60)
    
    total_checks = len(checks)
    passed_checks = sum(1 for result in checks.values() if result)
    
    for check_name, result in checks.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name:<20} {status}")
    
    print("-" * 60)
    print(f"总计: {passed_checks}/{total_checks} 项检查通过")
    
    if passed_checks == total_checks:
        print("🎉 系统检查完全通过，可以启动系统！")
        print("\n启动命令:")
        print("  python start_system.py")
        print("  或双击 start_system.bat (Windows)")
        return True
    else:
        print("⚠️  系统检查未完全通过，请解决上述问题")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 HCR-Auth 系统检查")
    print("=" * 60)
    print()
    
    # 执行各项检查
    checks = {
        "Python版本": check_python_version(),
        "Node.js版本": check_node_version(),
        "Python依赖": check_python_dependencies(),
        "Node.js依赖": check_node_dependencies(),
        "文件结构": check_file_structure(),
        "算法文件": check_algorithm_files(),
        "端口检查": check_ports()
    }
    
    # 生成报告
    success = generate_report(checks)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
