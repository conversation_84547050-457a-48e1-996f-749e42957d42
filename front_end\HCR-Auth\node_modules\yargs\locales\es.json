{"Commands:": "Comandos:", "Options:": "Opciones:", "Examples:": "Ejemplos:", "boolean": "booleano", "count": "cuenta", "string": "cadena de caracteres", "number": "número", "array": "tabla", "required": "requerido", "default": "defecto", "default:": "defecto:", "choices:": "selección:", "aliases:": "alias:", "generated-value": "valor-generado", "Not enough non-option arguments: got %s, need at least %s": {"one": "Hacen falta argumentos no-opcionales: Número recibido %s, necesita por lo menos %s", "other": "Hacen falta argumentos no-opcionales: Número recibido %s, necesita por lo menos %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "Demasiados argumentos no-opcionales: Número recibido %s, máximo es %s", "other": "Demasiados argumentos no-opcionales: Número recibido %s, máximo es %s"}, "Missing argument value: %s": {"one": "Falta argumento: %s", "other": "Faltan argumentos: %s"}, "Missing required argument: %s": {"one": "Falta argumento requerido: %s", "other": "Faltan argumentos requeridos: %s"}, "Unknown argument: %s": {"one": "Argumento desconocido: %s", "other": "Argumentos desconocidos: %s"}, "Invalid values:": "Valores inválidos:", "Argument: %s, Given: %s, Choices: %s": "Argumento: %s, Recibido: %s, Seleccionados: %s", "Argument check failed: %s": "Verificación de argumento ha fallado: %s", "Implications failed:": "Implicaciones fallidas:", "Not enough arguments following: %s": "No hay suficientes argumentos después de: %s", "Invalid JSON config file: %s": "Archivo de configuración JSON inválido: %s", "Path to JSON config file": "Ruta al archivo de configuración JSON", "Show help": "<PERSON><PERSON><PERSON> a<PERSON>", "Show version number": "Muestra número de versión", "Did you mean %s?": "Quisiste decir %s?"}