{"version": 3, "sources": ["../src/log.js"], "names": ["globalThis", "ROARR", "logFactory", "createLogger", "environmentIsNode", "enabled", "process", "env", "ROARR_LOG", "createMockLogger", "message", "write", "body", "JSON", "stringify"], "mappings": ";;;;;;;AAEA;;AAGA;;AACA;;AACA;;;;AAMA,MAAMA,UAAU,GAAG,0BAAnB;AAEA,MAAMC,KAAK,GAAGD,UAAU,CAACC,KAAX,GAAmB,gDAAgCD,UAAU,CAACC,KAAX,IAAoB,EAApD,CAAjC;;AAEA,IAAIC,UAAU,GAAGC,uBAAjB;;AAEA,IAAIC,mBAAJ,EAAuB;AACrB;AACA,QAAMC,OAAO,GAAG,sBAAQC,OAAO,CAACC,GAAR,CAAYC,SAAZ,IAAyB,EAAjC,CAAhB;;AAEA,MAAI,CAACH,OAAL,EAAc;AACZH,IAAAA,UAAU,GAAGO,2BAAb;AACD;AACF;;eAYcP,UAAU,CAAEQ,OAAD,IAAa;AACrC,MAAIT,KAAK,CAACU,KAAV,EAAiB;AACf;AACA;AACA,UAAMC,IAAI,GAAGC,IAAI,CAACC,SAAL,CAAeJ,OAAf,CAAb;AAEAT,IAAAA,KAAK,CAACU,KAAN,CAAYC,IAAZ;AACD;AACF,CARwB,C", "sourcesContent": ["// @flow\n\nimport {\n  boolean,\n} from 'boolean';\nimport environmentIsNode from 'detect-node';\nimport createGlobalThis from 'globalthis';\nimport {\n  createLogger,\n  createMockLogger,\n  createRoarrInititialGlobalState,\n} from './factories';\n\nconst globalThis = createGlobalThis();\n\nconst ROARR = globalThis.ROARR = createRoarrInititialGlobalState(globalThis.ROARR || {});\n\nlet logFactory = createLogger;\n\nif (environmentIsNode) {\n  // eslint-disable-next-line no-process-env\n  const enabled = boolean(process.env.ROARR_LOG || '');\n\n  if (!enabled) {\n    logFactory = createMockLogger;\n  }\n}\n\nexport type {\n  LoggerType,\n  MessageType,\n  TranslateMessageFunctionType,\n} from './types';\n\nexport {\n  ROARR,\n};\n\nexport default logFactory((message) => {\n  if (ROARR.write) {\n    // Stringify message as soon as it is received to prevent\n    // properties of the context from being modified by reference.\n    const body = JSON.stringify(message);\n\n    ROARR.write(body);\n  }\n});\n"], "file": "log.js"}