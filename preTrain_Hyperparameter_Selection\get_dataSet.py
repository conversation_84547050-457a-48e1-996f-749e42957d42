# -*- coding:utf-8 -*-

from torch.utils.data import Dataset

import os
import random
import torch
from torch.utils.data import Dataset, Subset
from torchvision import transforms

class MyDataset(Dataset):
    def __init__(self, root_dir, train=True, startUser = 46, endUser = 61):
        self.root_dir = root_dir
        self.samples = []
        self.labels = []
        self.train = train
        self.startUser = startUser
        self.endUser = endUser

        # 遍历文件夹，读取数据和标签
        for i in range(startUser, endUser):  # 假设文件夹编号为1到15
            folder_path = os.path.join(root_dir, str(i))
            for j in range(1, 251):  # 假设每个文件夹有250个txt文件
                file_path = os.path.join(folder_path, str(j) + ".txt")
                # 读取文件内容
                with open(file_path, 'r') as file:
                    data = file.read().strip().split('\n')[0:174]  # 假设每个txt文件的数据为一行一行的数字，每行有480个数字
                # 将数据和标签添加到列表中
                self.samples.append(list(map(float, data)))
                self.labels.append(i)  # 每个样本对应的标签为文件夹的编号
        self.samples = torch.tensor(self.samples)
        self.labels = torch.tensor(self.labels)
        self.samples = torch.unsqueeze(self.samples, 1) # 在第1维上添加一个维度，将形状[N, 480]转换为[N, 1,480]


    def __getitem__(self, index):
        sample = self.samples[index]
        label = self.labels[index]
        return torch.tensor(sample), torch.tensor(label)

    def __len__(self):
        return len(self.samples)


class MySubset(Subset):
    def __init__(self, dataset, indices, train=True, transform=None):
        super().__init__(dataset, indices)
        self.train = train
        self.transform = transform
        self.samples = dataset.samples[indices]
        self.labels = dataset.labels[indices]

    def __getitem__(self, index):
        sample = self.samples[index]
        label = self.labels[index]
        if self.transform is not None:
            sample = self.transform(sample)
        # sample = torch.unsqueeze(sample, 0) # 在第0维上添加一个维度，将形状[480]转换为[1,480],第一个维度表示batchsize
        # sample = torch.unsqueeze(sample, 0)  # 在第0维上添加一个维度，将形状[480]转换为[1,1,480],第一个维度表示batchsize
        return torch.tensor(sample), torch.tensor(label)

    def __len__(self):
        return len(self.samples)


if __name__ == '__main__':

    # 创建数据集
    dataset = MyDataset('/root/project/Paper2/dataset')  # 将'/path/to/dataset'替换为你的数据集路径

    # 划分训练集和测试集
    train_indices = []
    test_indices = []
    for i in range(46, 61):
        indices = [j for j, label in enumerate(dataset.labels) if label == i]
        # print("i, indices:", i, indices)
        test_indices.extend(random.sample(indices, 50))  # 随机选择50个样本作为测试集
        # print("i, test_indices:", i, test_indices)
        train_indices.extend([index for index in indices if index not in test_indices])  # 其余样本作为训练集

    # 创建训练集和测试集
    train_dataset = MySubset(dataset, train_indices, train=True)
    test_dataset = MySubset(dataset, test_indices, train=False)

    # print("train_dataset.samples:", train_dataset.samples)
    # print("train_dataset.samples[0]:", train_dataset.samples[127])
    # sampel, label = train_dataset[127]
    # print("sampel:", sampel)

    # print(len(train_dataset))  # 输出训练集样本数，应为15 * 200
    # print(len(test_dataset))  # 输出测试集样本数，应为15 * 50

    # 随机获取一个训练样本和标签
    for i in range(750):
    # sample, label = train_dataset[random.randint(0, len(train_dataset)-1)]
        sample, label = test_dataset[i]
        # print(sample.size())  # 输出样本的形状，应为(480,)
        # print(label)  # 输出标签
