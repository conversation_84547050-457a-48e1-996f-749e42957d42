<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="84037c11-1e5f-4f6e-b46f-4223ab2d7865" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DatabaseView">
    <option name="SHOW_INTERMEDIATE" value="true" />
    <option name="GROUP_DATA_SOURCES" value="true" />
    <option name="GROUP_SCHEMA" value="true" />
    <option name="GROUP_CONTENTS" value="false" />
    <option name="SORT_POSITIONED" value="false" />
    <option name="SHOW_EMPTY_GROUPS" value="false" />
    <option name="AUTO_SCROLL_FROM_SOURCE" value="false" />
    <option name="HIDDEN_KINDS">
      <set />
    </option>
    <expand />
    <select />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="2QYb4PN65UpXJOuuqNmlUtITPpk" />
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="WebServerToolWindowPanel.toolwindow.highlight.mappings" value="true" />
    <property name="WebServerToolWindowPanel.toolwindow.highlight.symlinks" value="true" />
    <property name="WebServerToolWindowPanel.toolwindow.show.date" value="false" />
    <property name="WebServerToolWindowPanel.toolwindow.show.permissions" value="false" />
    <property name="WebServerToolWindowPanel.toolwindow.show.size" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\PyCharm 2019.2.5\workspace\paper2\Data_Split" />
      <recent name="F:\PyCharm 2019.2.5\workspace\paper2\Data_Split\without_Signal" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Python.Event_Detection">
    <configuration name="Event_Detection" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="Data_Split" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Event_Detection.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="batch_EventDetection" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="Data_Split" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/batch_EventDetection.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="calculate_dB" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="Data_Split" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/calculate_dB.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="getRandomData" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="Data_Split" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/getRandomData.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_eventDetection" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <module name="Data_Split" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_eventDetection.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.Event_Detection" />
        <item itemvalue="Python.test_eventDetection" />
        <item itemvalue="Python.batch_EventDetection" />
        <item itemvalue="Python.getRandomData" />
        <item itemvalue="Python.calculate_dB" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="84037c11-1e5f-4f6e-b46f-4223ab2d7865" name="Default Changelist" comment="" />
      <created>1685533269125</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1685533269125</updated>
      <workItem from="1685533280655" duration="36521000" />
      <workItem from="1685673903163" duration="13349000" />
      <workItem from="1687269804882" duration="841000" />
      <workItem from="1688133282769" duration="2111000" />
      <workItem from="1689319741361" duration="344000" />
      <workItem from="1692692521098" duration="5294000" />
      <workItem from="1699449650250" duration="2355000" />
      <workItem from="1701093375422" duration="647000" />
      <workItem from="1701094068233" duration="400000" />
      <workItem from="1701173306121" duration="4135000" />
      <workItem from="1704096880289" duration="3492000" />
      <workItem from="1704250944616" duration="3128000" />
      <workItem from="1705288953648" duration="1330000" />
      <workItem from="1706097593942" duration="6454000" />
      <workItem from="1711071890610" duration="282000" />
      <workItem from="1721350935193" duration="4644000" />
      <workItem from="1721355852517" duration="2680000" />
      <workItem from="1722392109026" duration="4595000" />
      <workItem from="1723370085646" duration="2507000" />
      <workItem from="1723554692929" duration="2545000" />
      <workItem from="1723724814547" duration="2042000" />
      <workItem from="1739286489436" duration="168000" />
      <workItem from="1741696837636" duration="1588000" />
      <workItem from="1743082909735" duration="359000" />
      <workItem from="1747127359615" duration="348000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/Data_Split$batch_EventDetection.coverage" NAME="batch_EventDetection Coverage Results" MODIFIED="1721356868732" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Data_Split$useFFT.coverage" NAME="useFFT Coverage Results" MODIFIED="1685586151821" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/without_Signal" />
    <SUITE FILE_PATH="coverage/Data_Split$getRandomData.coverage" NAME="getRandomData Coverage Results" MODIFIED="1706234390431" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Data_Split$Event_Detection.coverage" NAME="Event_Detection Coverage Results" MODIFIED="1743082928203" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Data_Split$test_eventDetection.coverage" NAME="test_eventDetection Coverage Results" MODIFIED="1741697183565" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Data_Split$wav_resample.coverage" NAME="wav_resample Coverage Results" MODIFIED="1685536499626" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Data_Split$calculate_dB.coverage" NAME="calculate_dB Coverage Results" MODIFIED="1701226722481" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>