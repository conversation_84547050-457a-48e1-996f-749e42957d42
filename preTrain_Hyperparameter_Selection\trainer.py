import torch
import numpy as np


def fit(train_loader, val_loader, model, loss_fn, optimizer, scheduler, n_epochs, cuda, log_interval, metrics=[],
        start_epoch=0):
    """
    Loaders, model, loss function and metrics should work together for a given task,
    i.e. The model should be able to process data output of loaders,
    loss function should process target output of loaders and outputs from the model

    Examples: Classification: batch loader, classification model, NLL loss, accuracy metric
    Siamese network: Siamese loader, siamese model, contrastive loss
    Online triplet learning: batch loader, embedding model, online triplet loss
    """

    for epoch in range(0, start_epoch):
        scheduler.step()

    TrainLoss_list = []
    ValidLoss_list = []
    for epoch in range(start_epoch, n_epochs):
        scheduler.step() #按照scheduler的设定更新学习率

        # Train stage，返回总平均的loss和Acc
        train_loss, metrics = train_epoch(train_loader, model, loss_fn, optimizer, cuda, log_interval, metrics)
        # message = 'Epoch: {}/{}. Train set: Average loss: {:.4f}'.format(epoch + 1, n_epochs, train_loss)
        # for metric in metrics:
        #     message += '\t{}: {}'.format(metric.name(), metric.value())

        val_loss, metrics = test_epoch(val_loader, model, loss_fn, cuda, metrics)
        val_loss /= len(val_loader) # loss取得平均值
    #     # message += '\nEpoch: {}/{}. Validation set: Average loss: {:.4f}'.format(epoch + 1, n_epochs,
    #     #                                                                        val_loss)
    #     # for metric in metrics:
    #     #     message += '\t{}: {}'.format(metric.name(), metric.value())
        TrainLoss_list.append(train_loss)
        ValidLoss_list.append(val_loss)
        # print(message)

    # 打印总体训练的最低Loss
    min_trainLoss = min(TrainLoss_list)
    min_trainIndex = TrainLoss_list.index(min_trainLoss)
    min_validLoss = min(ValidLoss_list)
    min_validIndex = ValidLoss_list.index(min_validLoss)
    message1 = '\ntrainLoss: {}-{}. validLoss：{}-{}.'.format(min_trainIndex, min_trainLoss, min_validIndex, min_validLoss)
    print(message1)
    # message1 = '\ntrainLoss: {}-{}.'.format(min_trainIndex, min_trainLoss)
    # print(message1)



def train_epoch(train_loader, model, loss_fn, optimizer, cuda, log_interval, metrics):

    #重置评估指标
    for metric in metrics:
        metric.reset()

    #将模型设置为训练模式，此时模型参数会被更新，一些特殊层(dropout、Batch Normalization )会被启用
    model.train()
    losses = []
    total_loss = 0

    # data 为长度256的tensor
    for batch_idx, (data, target) in enumerate(train_loader):

        # print("len(data):", len(data))
        # print("data[1].shape:", data[1].shape)

        # 如果标签数据为空，则将target赋值none
        target = target if len(target) > 0 else None
        # 数据转换，如果data不是元组或列表类型，则将其转换为元组类型
        if not type(data) in (tuple, list):
            data = (data,)
        # 如果使用cuda加速，则将数据和标签转换到GPU上，返回转换后的数据和标签
        if cuda:
            data = tuple(d.cuda() for d in data)
            if target is not None:
                target = target.cuda()

        # 将模型中的梯度参数置为零
        optimizer.zero_grad()
        # 前向传播计算输出
        outputs = model(*data)  #tensor类型 256*10; 对于孪生网络，输出为turple类型，里面包含两个128*2的tensor
        # print("outputs:", outputs)
        # print("type(outputs):", type(outputs))
        # 数据转换，如果output不是元组或列表类型，则将其转换为元组(turple)类型，长度为1，outputs[0]是256*10的tensor
        if type(outputs) not in (tuple, list):
            outputs = (outputs,)
        # 将forward输出转换为loss输入
        loss_inputs = outputs
        if target is not None:
            # 对于孪生网络，target是128*1的tensor，里面标记了当前的两个样本是相同类别(1)还是不同类别(0)
            target = (target,) #turple类型，长度为1
            # 将两个长度为1的turple拼接形成长度为2的turple，可以通过loss_inputs[0]和loss_inputs[1]分别得到两个tensor
            loss_inputs += target
            # print("target:", target)


        # 默认对一个batch所有样本求loss并计算均值，得到1*1的tensor
        loss_outputs = loss_fn(*loss_inputs)
        # 这里是为了防止输出为turple形式，这里loss仍然是均值
        loss = loss_outputs[0] if type(loss_outputs) in (tuple, list) else loss_outputs
        # print("loss:", loss)
        # 记录不同batch的平均loss
        losses.append(loss.item()) # item()函数用于返回tensor单个标量的值
        # 不同batch的平均loss求和
        total_loss += loss.item()
        loss.backward()
        #根据梯度信息更新模型参数的值
        optimizer.step()

        for metric in metrics:
            metric(outputs, target, loss_outputs)


        # # 当前batch_idx是log_interval的倍数
        # if batch_idx % log_interval == 0:
        #     message = 'Train: [{}/{} ({:.0f}%)]\tLoss: {:.6f}'.format(
        #         batch_idx * len(data[0]), len(train_loader.dataset),
        #         100. * batch_idx / len(train_loader), np.mean(losses))
        #     # batch_idx * len(data[0]), len(train_loader.dataset){定值=6w}表征的是sample数目
        #     # 100. * batch_idx / len(train_loader){定值=6w/256}是使用batch数目计算的(乘100是因为要表示百分数)
        #     for metric in metrics:
        #         message += '\t{}: {}'.format(metric.name(), metric.value())
        #     print(message)

            losses = []

    # 求总的平均loss
    total_loss /= (batch_idx + 1)
    return total_loss, metrics



def test_epoch(val_loader, model, loss_fn, cuda, metrics):

    # 构建计算图时，不会为梯度属性分配存储空间，降低了内存开销,此外loss.backward()会被禁用
    with torch.no_grad():
        for metric in metrics:
            metric.reset()
        # 将模式设置为评估模式，此时 Batch Normalization不再更新
        # Dropout也不再随机失活，从而获得更加稳测试结果
        model.eval()
        val_loss = 0
        for batch_idx, (data, target) in enumerate(val_loader):
            target = target if len(target) > 0 else None
            if not type(data) in (tuple, list): # data转为turple类型
                data = (data,)
            if cuda: #数据放在cuda里面
                data = tuple(d.cuda() for d in data)
                if target is not None:
                    target = target.cuda()

            # 前向传播计算结果，得到256*10的tensor[对于孪生网络是256*2的tensor*2]
            outputs = model(*data)

            if type(outputs) not in (tuple, list):
                outputs = (outputs,) # 输出转为turple
            loss_inputs = outputs
            if target is not None:
                target = (target,)
                loss_inputs += target # 输出和标签两个turple合并

            loss_outputs = loss_fn(*loss_inputs) # 计算loss
            loss = loss_outputs[0] if type(loss_outputs) in (tuple, list) else loss_outputs # 防止loss是turple类型
            val_loss += loss.item() # loss求和

            for metric in metrics:
                metric(outputs, target, loss_outputs) # 计算指标
                # print(""outputs)

    return val_loss, metrics
