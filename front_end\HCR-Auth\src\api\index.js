import { request } from './request'

const registerUser = () => {
  return request.get(`/create/`)
}

const uploadFile = (file, userId, type) => {
  const form = new FormData()
  form.append('file', file)
  return request.post(`/upload/${userId}/${type}/`, form, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const startRegister = (userId) => {
  return request.get(`/register/${userId}/`)
}

const getUserRegisterStatus = (userId) => {
  return request.get(`/status/${userId}/`)
}

const loginUser = (file, userId, fake) => {
  const form = new FormData()
  form.append('file', file)

  return request.post(`/login/${userId}/?${fake ? 'fake=' + fake : ''}`, form, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const getUserLoginStatus = (userId, status_id) => {
  return request.get(`/login/${userId}/status/${status_id}/`)
}

export default {
  registerUser,
  getUserRegisterStatus,
  loginUser,
  getUserLoginStatus,
  uploadFile,
  startRegister
}
