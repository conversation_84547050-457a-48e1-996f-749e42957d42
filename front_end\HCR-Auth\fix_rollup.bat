@echo off
chcp 65001 >nul
title 修复Rollup依赖问题

echo ================================================================
echo 🔧 修复Rollup依赖问题
echo ================================================================
echo.

echo 📁 当前目录: %CD%
echo.

echo 🧹 清理现有安装...
if exist node_modules (
    echo 删除 node_modules...
    rmdir /s /q node_modules
)
if exist package-lock.json (
    echo 删除 package-lock.json...
    del package-lock.json
)
echo ✅ 清理完成
echo.

echo 🔧 配置npm...
npm config set registry https://registry.npmmirror.com
echo ✅ 使用国内镜像源
echo.

echo 📦 重新安装所有依赖（包含可选依赖）...
echo 这次会安装Rollup的原生模块...
echo.

npm install

if %errorlevel% equ 0 (
    echo.
    echo ✅ 依赖安装成功！
    echo.
    echo 🔍 验证Rollup模块...
    if exist "node_modules\@rollup\rollup-win32-x64-msvc" (
        echo ✅ Rollup原生模块已安装
    ) else (
        echo ⚠️  Rollup原生模块可能缺失，尝试手动安装...
        npm install @rollup/rollup-win32-x64-msvc
    )
    echo.
    echo 🚀 尝试启动开发服务器...
    npm run dev
) else (
    echo.
    echo ❌ 安装失败，尝试备用方案...
    echo.
    echo 🔄 尝试使用官方源...
    npm config set registry https://registry.npmjs.org/
    npm install
    
    if %errorlevel% equ 0 (
        echo ✅ 使用官方源安装成功！
        npm run dev
    ) else (
        echo ❌ 所有方法都失败了
        echo.
        echo 💡 建议:
        echo 1. 检查网络连接
        echo 2. 尝试使用手机热点
        echo 3. 或者使用yarn: yarn install && yarn dev
        echo 4. 或者暂时只使用后端API测试
    )
)

echo.
pause
