<template>
  <main>
    <!-- <el-text size="large" type="primary">欢迎使用</el-text>
    <el-divider></el-divider>

    <h2>验证</h2>

    <el-divider></el-divider> -->
    <el-text>请正确佩戴骨传导耳机</el-text>
    <el-text>请输入您的id并点击下方按钮开始对您的身份进行认证</el-text>
    <div style="display: flex; flex-direction: row; padding: 4px">
      <el-text type="primary">ID：</el-text
      ><el-input v-model="userId" size="small" placeholder="请输入ID" />
    </div>

    <RecorderComponent
      type="verify"
      :disabled="!userId"
      @finish="handleFinish"
      @start="handleStart"
    ></RecorderComponent>
  </main>
</template>

<script setup>
import { ref } from 'vue'
import RecorderComponent from './../components/RecorderComponent.vue'
import { ElMessageBox } from 'element-plus'
import { ElMessage } from 'element-plus'
import { uploadFile, getStatus } from '@/util/utils'

const isFinished = ref(false)
const isRegisterSuccess = ref(false)
const isStart = ref(false)
const userId = ref(null)
let loginId

const handleFinish = async (blob, clickType) => {
  console.log('🚀 ~ handleFinish ~ qq:', clickType)
  if (!blob) {
    return
  }
  isFinished.value = true
  ElMessage({
    type: 'success',
    message: '采集成功，分析中。。。',
    plain: true
  })
  const UUID = crypto.randomUUID()
  const file = new File([blob], UUID + '.wav', {
    type: 'audio/wave'
  })
  try {
    // let currentCount = localStorage.getItem('current')
    // const key = localStorage.getItem('key')
    // let fake = undefined
    // const switcher = localStorage.getItem('switch')
    // if (key && currentCount && switcher == 1) {
    //   if (currentCount > key.length) {
    //     fake = key[0]
    //     currentCount = 1
    //   }
    //   fake = key[currentCount - 1]
    //   localStorage.setItem('current', Number(currentCount) + 1)
    // }
    let fake = undefined
    console.log('fake', fake)
    if (clickType == 'left') {
      fake = '0'
    } else if (clickType == 'right') {
      fake = '1'
    }
    const { data } = await uploadFile('login', file, userId.value, undefined, fake)
    console.log('🚀 ~ handleFinish ~ response:', data)
    loginId = data.data.id
    await updateRegisterStatus()
  } catch (error) {
    console.error('🚀 ~ handleFinish ~ error:', error)

    ElMessageBox.alert('验证失败', '验证结果', {
      type: 'error'
    })
  }
}

const handleStart = () => {
  isStart.value = true
}

const updateRegisterStatus = async () => {
  if (!userId.value) {
    return
  }
  await getStatus('login', userId.value, loginId)
  isRegisterSuccess.value = true
  ElMessageBox.alert('验证成功', '验证结果', {
    type: 'success'
  })
}
</script>

<style scoped>
main {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
