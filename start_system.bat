@echo off
chcp 65001 >nul
echo ================================================================
echo 🦴 骨振识息系统 (HCR-Auth)
echo 🚀 系统启动器 (Windows)
echo ================================================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请安装Python 3.8+
    pause
    exit /b 1
)

echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Node.js，请安装Node.js 16+
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

echo 🚀 启动系统...
python start_system.py

pause
