{"version": 3, "file": "node-gyp.js", "sourceRoot": "", "sources": ["../../../src/module-type/node-gyp/node-gyp.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,8DAAqC;AACrC,gDAAwB;AACxB,oDAA4B;AAE5B,+CAAmD;AACnD,uDAA8D;AAC9D,0BAAkC;AAClC,iDAAqC;AAErC,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,MAAa,OAAQ,SAAQ,gBAAY;IACvC,KAAK,CAAC,SAAS,CAAC,YAAsB;QACpC,MAAM,IAAI,GAAG;YACX,MAAM;YACN,UAAU;YACV,SAAS;YACT,GAAG,YAAY;YACf,oBAAoB;YACpB,YAAY,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;YAC5C,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;YAC/B,cAAc,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxC,qBAAqB;SACtB,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAEhD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACxB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACtB;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;SAC3D;QAED,wEAAwE;QACxE,oEAAoE;QACpE,oCAAoC;QACpC,wDAAwD;QACxD,IAAI,CAAC,gBAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,oBAAoB,CAAC,IAAI,gBAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,GAAG,EAAE,EAAE;YAChI,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SACrC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,QAAQ,EAAE,EAAE,CAA2B,CAAC;QAC9F,IAAI,gBAAgB,GAAuB,SAAS,CAAC;QACrD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;YACvC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAC9F;QACD,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE;YAC5F,IAAI,SAAS,KAAK,eAAe,EAAE;gBACjC,OAAO;aACR;YAED,IAAI,KAAK,GAAG,WAAW,CAAC;YAExB,IAAI,SAAS,KAAK,aAAa,EAAE;gBAC/B,KAAK,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;aAC9C;YAED,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;iBAC/D,OAAO,CAAC,YAAY,EAAE,aAAa,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;iBACrG,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;iBAC9C,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;iBACtC,OAAO,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAW,CAAC;iBACtE,OAAO,CAAC,QAAQ,EAAE,MAAM,qBAAU,CAAC,MAAM,EAAE,IAAI,SAAS,CAAC,CAAC;YAC7D,IAAI,gBAAgB,KAAK,SAAS,EAAE;gBAClC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;aAC5E;YACD,KAAK,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC/D,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,EAAE,YAAY,CAAC,CAAC;aACxD;YAED,OAAO,KAAK,SAAS,IAAI,KAAK,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC;QAEJ,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAa,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,aAAa;;QACjB,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAC;SAC1F;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjC,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;YACvE,OAAO,CAAC,KAAK,CAAC,2GAA2G,CAAC,CAAC;YAC3H,8DAA8D;YAC9D,0HAA0H;SAC3H;QAED,MAAM,GAAG,GAAG;YACV,GAAG,OAAO,CAAC,GAAG;SACf,CAAC;QACF,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACnC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,IAAA,uCAAuB,EAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC9H,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC7B,gBAAgB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;SACrC;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC3D,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAE3D,MAAM,WAAW,GAAG,IAAA,oBAAI,EAAC,cAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE;YAC7D,GAAG;YACH,GAAG,EAAE,IAAI,CAAC,UAAU;YACpB,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;QACH,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,MAAA,WAAW,CAAC,MAAM,0CAAE,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YACvC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,MAAA,WAAW,CAAC,MAAM,0CAAE,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YACvC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QACH,WAAW,CAAC,IAAI,CAAC;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,WAAW;YACX,gBAAgB;YAChB,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,CAAC,4BAAgB,CAAC,CAAC,CAAC,cAAI,CAAC,OAAO,CAAC,4BAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC;SACxH,CAAC,CAAC;QAEH,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1C,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC9B,IAAI,IAAI,KAAK,CAAC;oBAAE,OAAO,OAAO,EAAE,CAAC;gBACjC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACvD,MAAM,CAAC,IAAI,KAAK,CAAC,+BAA+B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA9HD,0BA8HC"}