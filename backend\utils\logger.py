# -*- coding: utf-8 -*-
"""
日志配置
"""

import logging
import os
from datetime import datetime
from .config import Config

def setup_logger():
    """设置日志配置"""
    # 确保日志目录存在
    os.makedirs(Config.LOG_FOLDER, exist_ok=True)
    
    # 创建logger
    logger = logging.getLogger('hcr_auth')
    logger.setLevel(logging.INFO)
    
    # 避免重复添加handler
    if logger.handlers:
        return logger
    
    # 创建文件handler
    log_file = Config.LOG_FOLDER / f"hcr_auth_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加handler到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
