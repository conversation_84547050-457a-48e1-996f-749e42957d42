AUTHORS.txt
LICENSE.txt
MANIFEST.in
NEWS.rst
README.rst
pyproject.toml
setup.cfg
setup.py
docs/pip_sphinxext.py
docs/html/conf.py
docs/html/cookbook.rst
docs/html/index.rst
docs/html/installing.rst
docs/html/logic.rst
docs/html/news.rst
docs/html/quickstart.rst
docs/html/usage.rst
docs/html/user_guide.rst
docs/html/development/configuration.rst
docs/html/development/contributing.rst
docs/html/development/getting-started.rst
docs/html/development/index.rst
docs/html/development/release-process.rst
docs/html/development/vendoring-policy.rst
docs/html/reference/index.rst
docs/html/reference/pip.rst
docs/html/reference/pip_check.rst
docs/html/reference/pip_config.rst
docs/html/reference/pip_download.rst
docs/html/reference/pip_freeze.rst
docs/html/reference/pip_hash.rst
docs/html/reference/pip_install.rst
docs/html/reference/pip_list.rst
docs/html/reference/pip_search.rst
docs/html/reference/pip_show.rst
docs/html/reference/pip_uninstall.rst
docs/html/reference/pip_wheel.rst
docs/man/index.rst
docs/man/commands/check.rst
docs/man/commands/config.rst
docs/man/commands/download.rst
docs/man/commands/freeze.rst
docs/man/commands/hash.rst
docs/man/commands/help.rst
docs/man/commands/install.rst
docs/man/commands/list.rst
docs/man/commands/search.rst
docs/man/commands/show.rst
docs/man/commands/uninstall.rst
docs/man/commands/wheel.rst
src/pip/__init__.py
src/pip/__main__.py
src/pip.egg-info/PKG-INFO
src/pip.egg-info/SOURCES.txt
src/pip.egg-info/dependency_links.txt
src/pip.egg-info/entry_points.txt
src/pip.egg-info/not-zip-safe
src/pip.egg-info/top_level.txt
src/pip/_internal/__init__.py
src/pip/_internal/build_env.py
src/pip/_internal/cache.py
src/pip/_internal/configuration.py
src/pip/_internal/download.py
src/pip/_internal/exceptions.py
src/pip/_internal/index.py
src/pip/_internal/locations.py
src/pip/_internal/pep425tags.py
src/pip/_internal/pyproject.py
src/pip/_internal/resolve.py
src/pip/_internal/wheel.py
src/pip/_internal/cli/__init__.py
src/pip/_internal/cli/autocompletion.py
src/pip/_internal/cli/base_command.py
src/pip/_internal/cli/cmdoptions.py
src/pip/_internal/cli/main_parser.py
src/pip/_internal/cli/parser.py
src/pip/_internal/cli/status_codes.py
src/pip/_internal/commands/__init__.py
src/pip/_internal/commands/check.py
src/pip/_internal/commands/completion.py
src/pip/_internal/commands/configuration.py
src/pip/_internal/commands/download.py
src/pip/_internal/commands/freeze.py
src/pip/_internal/commands/hash.py
src/pip/_internal/commands/help.py
src/pip/_internal/commands/install.py
src/pip/_internal/commands/list.py
src/pip/_internal/commands/search.py
src/pip/_internal/commands/show.py
src/pip/_internal/commands/uninstall.py
src/pip/_internal/commands/wheel.py
src/pip/_internal/models/__init__.py
src/pip/_internal/models/candidate.py
src/pip/_internal/models/format_control.py
src/pip/_internal/models/index.py
src/pip/_internal/models/link.py
src/pip/_internal/operations/__init__.py
src/pip/_internal/operations/check.py
src/pip/_internal/operations/freeze.py
src/pip/_internal/operations/prepare.py
src/pip/_internal/req/__init__.py
src/pip/_internal/req/constructors.py
src/pip/_internal/req/req_file.py
src/pip/_internal/req/req_install.py
src/pip/_internal/req/req_set.py
src/pip/_internal/req/req_tracker.py
src/pip/_internal/req/req_uninstall.py
src/pip/_internal/utils/__init__.py
src/pip/_internal/utils/appdirs.py
src/pip/_internal/utils/compat.py
src/pip/_internal/utils/deprecation.py
src/pip/_internal/utils/encoding.py
src/pip/_internal/utils/filesystem.py
src/pip/_internal/utils/glibc.py
src/pip/_internal/utils/hashes.py
src/pip/_internal/utils/logging.py
src/pip/_internal/utils/misc.py
src/pip/_internal/utils/models.py
src/pip/_internal/utils/outdated.py
src/pip/_internal/utils/packaging.py
src/pip/_internal/utils/setuptools_build.py
src/pip/_internal/utils/temp_dir.py
src/pip/_internal/utils/typing.py
src/pip/_internal/utils/ui.py
src/pip/_internal/vcs/__init__.py
src/pip/_internal/vcs/bazaar.py
src/pip/_internal/vcs/git.py
src/pip/_internal/vcs/mercurial.py
src/pip/_internal/vcs/subversion.py
src/pip/_vendor/README.rst
src/pip/_vendor/__init__.py
src/pip/_vendor/appdirs.LICENSE.txt
src/pip/_vendor/appdirs.py
src/pip/_vendor/distro.LICENSE
src/pip/_vendor/distro.py
src/pip/_vendor/ipaddress.LICENSE
src/pip/_vendor/ipaddress.py
src/pip/_vendor/pyparsing.LICENSE
src/pip/_vendor/pyparsing.py
src/pip/_vendor/retrying.LICENSE
src/pip/_vendor/retrying.py
src/pip/_vendor/six.LICENSE
src/pip/_vendor/six.py
src/pip/_vendor/vendor.txt
src/pip/_vendor/cachecontrol/LICENSE.txt
src/pip/_vendor/cachecontrol/__init__.py
src/pip/_vendor/cachecontrol/_cmd.py
src/pip/_vendor/cachecontrol/adapter.py
src/pip/_vendor/cachecontrol/cache.py
src/pip/_vendor/cachecontrol/compat.py
src/pip/_vendor/cachecontrol/controller.py
src/pip/_vendor/cachecontrol/filewrapper.py
src/pip/_vendor/cachecontrol/heuristics.py
src/pip/_vendor/cachecontrol/serialize.py
src/pip/_vendor/cachecontrol/wrapper.py
src/pip/_vendor/cachecontrol/caches/__init__.py
src/pip/_vendor/cachecontrol/caches/file_cache.py
src/pip/_vendor/cachecontrol/caches/redis_cache.py
src/pip/_vendor/certifi/LICENSE
src/pip/_vendor/certifi/__init__.py
src/pip/_vendor/certifi/__main__.py
src/pip/_vendor/certifi/cacert.pem
src/pip/_vendor/certifi/core.py
src/pip/_vendor/chardet/LICENSE
src/pip/_vendor/chardet/__init__.py
src/pip/_vendor/chardet/big5freq.py
src/pip/_vendor/chardet/big5prober.py
src/pip/_vendor/chardet/chardistribution.py
src/pip/_vendor/chardet/charsetgroupprober.py
src/pip/_vendor/chardet/charsetprober.py
src/pip/_vendor/chardet/codingstatemachine.py
src/pip/_vendor/chardet/compat.py
src/pip/_vendor/chardet/cp949prober.py
src/pip/_vendor/chardet/enums.py
src/pip/_vendor/chardet/escprober.py
src/pip/_vendor/chardet/escsm.py
src/pip/_vendor/chardet/eucjpprober.py
src/pip/_vendor/chardet/euckrfreq.py
src/pip/_vendor/chardet/euckrprober.py
src/pip/_vendor/chardet/euctwfreq.py
src/pip/_vendor/chardet/euctwprober.py
src/pip/_vendor/chardet/gb2312freq.py
src/pip/_vendor/chardet/gb2312prober.py
src/pip/_vendor/chardet/hebrewprober.py
src/pip/_vendor/chardet/jisfreq.py
src/pip/_vendor/chardet/jpcntx.py
src/pip/_vendor/chardet/langbulgarianmodel.py
src/pip/_vendor/chardet/langcyrillicmodel.py
src/pip/_vendor/chardet/langgreekmodel.py
src/pip/_vendor/chardet/langhebrewmodel.py
src/pip/_vendor/chardet/langhungarianmodel.py
src/pip/_vendor/chardet/langthaimodel.py
src/pip/_vendor/chardet/langturkishmodel.py
src/pip/_vendor/chardet/latin1prober.py
src/pip/_vendor/chardet/mbcharsetprober.py
src/pip/_vendor/chardet/mbcsgroupprober.py
src/pip/_vendor/chardet/mbcssm.py
src/pip/_vendor/chardet/sbcharsetprober.py
src/pip/_vendor/chardet/sbcsgroupprober.py
src/pip/_vendor/chardet/sjisprober.py
src/pip/_vendor/chardet/universaldetector.py
src/pip/_vendor/chardet/utf8prober.py
src/pip/_vendor/chardet/version.py
src/pip/_vendor/chardet/cli/__init__.py
src/pip/_vendor/chardet/cli/chardetect.py
src/pip/_vendor/colorama/LICENSE.txt
src/pip/_vendor/colorama/__init__.py
src/pip/_vendor/colorama/ansi.py
src/pip/_vendor/colorama/ansitowin32.py
src/pip/_vendor/colorama/initialise.py
src/pip/_vendor/colorama/win32.py
src/pip/_vendor/colorama/winterm.py
src/pip/_vendor/distlib/LICENSE.txt
src/pip/_vendor/distlib/__init__.py
src/pip/_vendor/distlib/compat.py
src/pip/_vendor/distlib/database.py
src/pip/_vendor/distlib/index.py
src/pip/_vendor/distlib/locators.py
src/pip/_vendor/distlib/manifest.py
src/pip/_vendor/distlib/markers.py
src/pip/_vendor/distlib/metadata.py
src/pip/_vendor/distlib/resources.py
src/pip/_vendor/distlib/scripts.py
src/pip/_vendor/distlib/t32.exe
src/pip/_vendor/distlib/t64.exe
src/pip/_vendor/distlib/util.py
src/pip/_vendor/distlib/version.py
src/pip/_vendor/distlib/w32.exe
src/pip/_vendor/distlib/w64.exe
src/pip/_vendor/distlib/wheel.py
src/pip/_vendor/distlib/_backport/__init__.py
src/pip/_vendor/distlib/_backport/misc.py
src/pip/_vendor/distlib/_backport/shutil.py
src/pip/_vendor/distlib/_backport/sysconfig.cfg
src/pip/_vendor/distlib/_backport/sysconfig.py
src/pip/_vendor/distlib/_backport/tarfile.py
src/pip/_vendor/html5lib/LICENSE
src/pip/_vendor/html5lib/__init__.py
src/pip/_vendor/html5lib/_ihatexml.py
src/pip/_vendor/html5lib/_inputstream.py
src/pip/_vendor/html5lib/_tokenizer.py
src/pip/_vendor/html5lib/_utils.py
src/pip/_vendor/html5lib/constants.py
src/pip/_vendor/html5lib/html5parser.py
src/pip/_vendor/html5lib/serializer.py
src/pip/_vendor/html5lib/_trie/__init__.py
src/pip/_vendor/html5lib/_trie/_base.py
src/pip/_vendor/html5lib/_trie/datrie.py
src/pip/_vendor/html5lib/_trie/py.py
src/pip/_vendor/html5lib/filters/__init__.py
src/pip/_vendor/html5lib/filters/alphabeticalattributes.py
src/pip/_vendor/html5lib/filters/base.py
src/pip/_vendor/html5lib/filters/inject_meta_charset.py
src/pip/_vendor/html5lib/filters/lint.py
src/pip/_vendor/html5lib/filters/optionaltags.py
src/pip/_vendor/html5lib/filters/sanitizer.py
src/pip/_vendor/html5lib/filters/whitespace.py
src/pip/_vendor/html5lib/treeadapters/__init__.py
src/pip/_vendor/html5lib/treeadapters/genshi.py
src/pip/_vendor/html5lib/treeadapters/sax.py
src/pip/_vendor/html5lib/treebuilders/__init__.py
src/pip/_vendor/html5lib/treebuilders/base.py
src/pip/_vendor/html5lib/treebuilders/dom.py
src/pip/_vendor/html5lib/treebuilders/etree.py
src/pip/_vendor/html5lib/treebuilders/etree_lxml.py
src/pip/_vendor/html5lib/treewalkers/__init__.py
src/pip/_vendor/html5lib/treewalkers/base.py
src/pip/_vendor/html5lib/treewalkers/dom.py
src/pip/_vendor/html5lib/treewalkers/etree.py
src/pip/_vendor/html5lib/treewalkers/etree_lxml.py
src/pip/_vendor/html5lib/treewalkers/genshi.py
src/pip/_vendor/idna/LICENSE.rst
src/pip/_vendor/idna/__init__.py
src/pip/_vendor/idna/codec.py
src/pip/_vendor/idna/compat.py
src/pip/_vendor/idna/core.py
src/pip/_vendor/idna/idnadata.py
src/pip/_vendor/idna/intranges.py
src/pip/_vendor/idna/package_data.py
src/pip/_vendor/idna/uts46data.py
src/pip/_vendor/lockfile/LICENSE
src/pip/_vendor/lockfile/__init__.py
src/pip/_vendor/lockfile/linklockfile.py
src/pip/_vendor/lockfile/mkdirlockfile.py
src/pip/_vendor/lockfile/pidlockfile.py
src/pip/_vendor/lockfile/sqlitelockfile.py
src/pip/_vendor/lockfile/symlinklockfile.py
src/pip/_vendor/msgpack/COPYING
src/pip/_vendor/msgpack/__init__.py
src/pip/_vendor/msgpack/_version.py
src/pip/_vendor/msgpack/exceptions.py
src/pip/_vendor/msgpack/fallback.py
src/pip/_vendor/packaging/LICENSE
src/pip/_vendor/packaging/LICENSE.APACHE
src/pip/_vendor/packaging/LICENSE.BSD
src/pip/_vendor/packaging/__about__.py
src/pip/_vendor/packaging/__init__.py
src/pip/_vendor/packaging/_compat.py
src/pip/_vendor/packaging/_structures.py
src/pip/_vendor/packaging/markers.py
src/pip/_vendor/packaging/requirements.py
src/pip/_vendor/packaging/specifiers.py
src/pip/_vendor/packaging/utils.py
src/pip/_vendor/packaging/version.py
src/pip/_vendor/pep517/LICENSE
src/pip/_vendor/pep517/__init__.py
src/pip/_vendor/pep517/_in_process.py
src/pip/_vendor/pep517/build.py
src/pip/_vendor/pep517/check.py
src/pip/_vendor/pep517/colorlog.py
src/pip/_vendor/pep517/compat.py
src/pip/_vendor/pep517/envbuild.py
src/pip/_vendor/pep517/wrappers.py
src/pip/_vendor/pkg_resources/LICENSE
src/pip/_vendor/pkg_resources/__init__.py
src/pip/_vendor/pkg_resources/py31compat.py
src/pip/_vendor/progress/LICENSE
src/pip/_vendor/progress/__init__.py
src/pip/_vendor/progress/bar.py
src/pip/_vendor/progress/counter.py
src/pip/_vendor/progress/helpers.py
src/pip/_vendor/progress/spinner.py
src/pip/_vendor/pytoml/LICENSE
src/pip/_vendor/pytoml/__init__.py
src/pip/_vendor/pytoml/core.py
src/pip/_vendor/pytoml/parser.py
src/pip/_vendor/pytoml/test.py
src/pip/_vendor/pytoml/utils.py
src/pip/_vendor/pytoml/writer.py
src/pip/_vendor/requests/LICENSE
src/pip/_vendor/requests/__init__.py
src/pip/_vendor/requests/__version__.py
src/pip/_vendor/requests/_internal_utils.py
src/pip/_vendor/requests/adapters.py
src/pip/_vendor/requests/api.py
src/pip/_vendor/requests/auth.py
src/pip/_vendor/requests/certs.py
src/pip/_vendor/requests/compat.py
src/pip/_vendor/requests/cookies.py
src/pip/_vendor/requests/exceptions.py
src/pip/_vendor/requests/help.py
src/pip/_vendor/requests/hooks.py
src/pip/_vendor/requests/models.py
src/pip/_vendor/requests/packages.py
src/pip/_vendor/requests/sessions.py
src/pip/_vendor/requests/status_codes.py
src/pip/_vendor/requests/structures.py
src/pip/_vendor/requests/utils.py
src/pip/_vendor/urllib3/LICENSE.txt
src/pip/_vendor/urllib3/__init__.py
src/pip/_vendor/urllib3/_collections.py
src/pip/_vendor/urllib3/connection.py
src/pip/_vendor/urllib3/connectionpool.py
src/pip/_vendor/urllib3/exceptions.py
src/pip/_vendor/urllib3/fields.py
src/pip/_vendor/urllib3/filepost.py
src/pip/_vendor/urllib3/poolmanager.py
src/pip/_vendor/urllib3/request.py
src/pip/_vendor/urllib3/response.py
src/pip/_vendor/urllib3/contrib/__init__.py
src/pip/_vendor/urllib3/contrib/_appengine_environ.py
src/pip/_vendor/urllib3/contrib/appengine.py
src/pip/_vendor/urllib3/contrib/ntlmpool.py
src/pip/_vendor/urllib3/contrib/pyopenssl.py
src/pip/_vendor/urllib3/contrib/securetransport.py
src/pip/_vendor/urllib3/contrib/socks.py
src/pip/_vendor/urllib3/contrib/_securetransport/__init__.py
src/pip/_vendor/urllib3/contrib/_securetransport/bindings.py
src/pip/_vendor/urllib3/contrib/_securetransport/low_level.py
src/pip/_vendor/urllib3/packages/__init__.py
src/pip/_vendor/urllib3/packages/six.py
src/pip/_vendor/urllib3/packages/backports/__init__.py
src/pip/_vendor/urllib3/packages/backports/makefile.py
src/pip/_vendor/urllib3/packages/ssl_match_hostname/__init__.py
src/pip/_vendor/urllib3/packages/ssl_match_hostname/_implementation.py
src/pip/_vendor/urllib3/util/__init__.py
src/pip/_vendor/urllib3/util/connection.py
src/pip/_vendor/urllib3/util/queue.py
src/pip/_vendor/urllib3/util/request.py
src/pip/_vendor/urllib3/util/response.py
src/pip/_vendor/urllib3/util/retry.py
src/pip/_vendor/urllib3/util/ssl_.py
src/pip/_vendor/urllib3/util/timeout.py
src/pip/_vendor/urllib3/util/url.py
src/pip/_vendor/urllib3/util/wait.py
src/pip/_vendor/webencodings/LICENSE
src/pip/_vendor/webencodings/__init__.py
src/pip/_vendor/webencodings/labels.py
src/pip/_vendor/webencodings/mklabels.py
src/pip/_vendor/webencodings/tests.py
src/pip/_vendor/webencodings/x_user_defined.py