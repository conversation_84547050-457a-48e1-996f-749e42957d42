#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
在当前环境中启动HCR-Auth系统
适用于任何Python环境（包括虚拟环境）
"""

import sys
import subprocess
import os
from pathlib import Path

def check_and_install_dependencies():
    """检查并安装依赖"""
    print("🔍 检查当前Python环境...")
    print(f"Python路径: {sys.executable}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    # 需要的包
    required_packages = [
        'flask',
        'flask_cors', 
        'torch',
        'numpy',
        'scipy',
        'matplotlib',
        'werkzeug',
        'requests'
    ]
    
    missing_packages = []
    
    print("📦 检查依赖包...")
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📥 安装缺失的包: {', '.join(missing_packages)}")
        
        # 包名映射（pip安装名可能不同）
        pip_names = {
            'flask_cors': 'Flask-CORS',
            'torch': 'torch',
            'numpy': 'numpy', 
            'scipy': 'scipy',
            'matplotlib': 'matplotlib',
            'werkzeug': 'werkzeug',
            'requests': 'requests',
            'flask': 'Flask'
        }
        
        for package in missing_packages:
            pip_name = pip_names.get(package, package)
            print(f"安装 {pip_name}...")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', pip_name])
                print(f"  ✅ {pip_name} 安装成功")
            except subprocess.CalledProcessError:
                print(f"  ❌ {pip_name} 安装失败")
                return False
    
    print("✅ 所有依赖已就绪")
    return True

def start_backend():
    """启动后端服务"""
    print("\n🚀 启动后端服务...")
    
    # 确保在正确的目录
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    if not backend_dir.exists():
        print(f"❌ 后端目录不存在: {backend_dir}")
        return False
    
    # 添加后端目录到Python路径
    sys.path.insert(0, str(backend_dir))
    
    try:
        # 切换到后端目录
        os.chdir(backend_dir)

        # 修复相对导入问题
        if str(backend_dir) not in sys.path:
            sys.path.insert(0, str(backend_dir))

        # 导入并启动Flask应用
        import app
        from utils.config import Config
        
        print(f"📍 后端服务地址: http://localhost:{Config.PORT}")
        print("💡 API测试: 在另一个终端运行 'python backend/test_api.py'")
        print("💡 前端启动: cd front_end/HCR-Auth && npm run dev")
        print("🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        app.app.run(
            host=Config.HOST,
            port=Config.PORT,
            debug=True,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖已正确安装")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🦴 骨振识息系统 (HCR-Auth)")
    print("🚀 当前环境启动器")
    print("=" * 60)
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("❌ 依赖安装失败")
        input("按回车键退出...")
        return 1
    
    # 启动后端服务
    try:
        start_backend()
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        input("按回车键退出...")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
