(function(f){function e(){var i=f();return i.default||i}if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var t=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};t.prettierPlugins=t.prettierPlugins||{},t.prettierPlugins.meriyah=e()}})(function(){"use strict";var Ie=Object.create;var L2=Object.defineProperty;var Ne=Object.getOwnPropertyDescriptor;var Ve=Object.getOwnPropertyNames;var Re=Object.getPrototypeOf,Oe=Object.prototype.hasOwnProperty;var Ue=(u,e)=>()=>(e||u((e={exports:{}}).exports,e),e.exports),Cu=(u,e)=>{for(var n in e)L2(u,n,{get:e[n],enumerable:!0})},Pu=(u,e,n,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Ve(e))!Oe.call(u,i)&&i!==n&&L2(u,i,{get:()=>e[i],enumerable:!(t=Ne(e,i))||t.enumerable});return u};var Me=(u,e,n)=>(n=u!=null?Ie(Re(u)):{},Pu(e||!u||!u.__esModule?L2(n,"default",{value:u,enumerable:!0}):n,u)),Je=u=>Pu(L2({},"__esModule",{value:!0}),u);var ve=Ue(a2=>{"use strict";Object.defineProperty(a2,"__esModule",{value:!0});a2.extract=F0;a2.parse=L0;a2.parseWithComments=Be;a2.print=I0;a2.strip=q0;var w0=/\*\/$/,S0=/^\/\*\*?/,we=/^\s*(\/\*\*?(.|\r?\n)*?\*\/)/,B0=/(^|\s+)\/\/([^\r\n]*)/g,Ce=/^(\r?\n)+/,v0=/(?:^|\r?\n) *(@[^\r\n]*?) *\r?\n *(?![^@\r\n]*\/\/[^]*)([^@\r\n\s][^@\r\n]+?) *\r?\n/g,Pe=/(?:^|\r?\n) *@(\S+) *([^\r\n]*)/g,T0=/(\r?\n|^) *\* ?/g,Se=[];function F0(u){let e=u.match(we);return e?e[0].trimLeft():""}function q0(u){let e=u.match(we);return e&&e[0]?u.substring(e[0].length):u}function L0(u){return Be(u).pragmas}function Be(u){let e=`
`;u=u.replace(S0,"").replace(w0,"").replace(T0,"$1");let n="";for(;n!==u;)n=u,u=u.replace(v0,`${e}$1 $2${e}`);u=u.replace(Ce,"").trimRight();let t=Object.create(null),i=u.replace(Pe,"").replace(Ce,"").trimRight(),o;for(;o=Pe.exec(u);){let l=o[2].replace(B0,"");typeof t[o[1]]=="string"||Array.isArray(t[o[1]])?t[o[1]]=Se.concat(t[o[1]],l):t[o[1]]=l}return{comments:i,pragmas:t}}function I0({comments:u="",pragmas:e={}}){let n=`
`,t="/**",i=" *",o=" */",l=Object.keys(e),f=l.flatMap(a=>Ee(a,e[a])).map(a=>`${i} ${a}${n}`).join("");if(!u){if(l.length===0)return"";if(l.length===1&&!Array.isArray(e[l[0]])){let a=e[l[0]];return`${t} ${Ee(l[0],a)[0]}${o}`}}let c=u.split(n).map(a=>`${i} ${a}`).join(n)+n;return t+n+(u?c:"")+(u&&l.length?i+n:"")+f+o}function Ee(u,e){return Se.concat(e).map(n=>`@${u} ${n}`.trim())}});var z0={};Cu(z0,{parsers:()=>bu});var bu={};Cu(bu,{meriyah:()=>X0});var je={0:"Unexpected token",28:"Unexpected token: '%0'",1:"Octal escape sequences are not allowed in strict mode",2:"Octal escape sequences are not allowed in template strings",3:"Unexpected token `#`",4:"Illegal Unicode escape sequence",5:"Invalid code point %0",6:"Invalid hexadecimal escape sequence",8:"Octal literals are not allowed in strict mode",7:"Decimal integer literals with a leading zero are forbidden in strict mode",9:"Expected number in radix %0",146:"Invalid left-hand side assignment to a destructible right-hand side",10:"Non-number found after exponent indicator",11:"Invalid BigIntLiteral",12:"No identifiers allowed directly after numeric literal",13:"Escapes \\8 or \\9 are not syntactically valid escapes",14:"Unterminated string literal",15:"Unterminated template literal",16:"Multiline comment was not closed properly",17:"The identifier contained dynamic unicode escape that was not closed",18:"Illegal character '%0'",19:"Missing hexadecimal digits",20:"Invalid implicit octal",21:"Invalid line break in string literal",22:"Only unicode escapes are legal in identifier names",23:"Expected '%0'",24:"Invalid left-hand side in assignment",25:"Invalid left-hand side in async arrow",26:'Calls to super must be in the "constructor" method of a class expression or class declaration that has a superclass',27:"Member access on super must be in a method",29:"Await expression not allowed in formal parameter",30:"Yield expression not allowed in formal parameter",93:"Unexpected token: 'escaped keyword'",31:"Unary expressions as the left operand of an exponentiation expression must be disambiguated with parentheses",120:"Async functions can only be declared at the top level or inside a block",32:"Unterminated regular expression",33:"Unexpected regular expression flag",34:"Duplicate regular expression flag '%0'",35:"%0 functions must have exactly %1 argument%2",36:"Setter function argument must not be a rest parameter",37:"%0 declaration must have a name in this context",38:"Function name may not contain any reserved words or be eval or arguments in strict mode",39:"The rest operator is missing an argument",40:"A getter cannot be a generator",41:"A setter cannot be a generator",42:"A computed property name must be followed by a colon or paren",131:"Object literal keys that are strings or numbers must be a method or have a colon",44:"Found `* async x(){}` but this should be `async * x(){}`",43:"Getters and setters can not be generators",45:"'%0' can not be generator method",46:"No line break is allowed after '=>'",47:"The left-hand side of the arrow can only be destructed through assignment",48:"The binding declaration is not destructible",49:"Async arrow can not be followed by new expression",50:"Classes may not have a static property named 'prototype'",51:"Class constructor may not be a %0",52:"Duplicate constructor method in class",53:"Invalid increment/decrement operand",54:"Invalid use of `new` keyword on an increment/decrement expression",55:"`=>` is an invalid assignment target",56:"Rest element may not have a trailing comma",57:"Missing initializer in %0 declaration",58:"'for-%0' loop head declarations can not have an initializer",59:"Invalid left-hand side in for-%0 loop: Must have a single binding",60:"Invalid shorthand property initializer",61:"Property name __proto__ appears more than once in object literal",62:"Let is disallowed as a lexically bound name",63:"Invalid use of '%0' inside new expression",64:"Illegal 'use strict' directive in function with non-simple parameter list",65:'Identifier "let" disallowed as left-hand side expression in strict mode',66:"Illegal continue statement",67:"Illegal break statement",68:"Cannot have `let[...]` as a var name in strict mode",69:"Invalid destructuring assignment target",70:"Rest parameter may not have a default initializer",71:"The rest argument must the be last parameter",72:"Invalid rest argument",74:"In strict mode code, functions can only be declared at top level or inside a block",75:"In non-strict mode code, functions can only be declared at top level, inside a block, or as the body of an if statement",76:"Without web compatibility enabled functions can not be declared at top level, inside a block, or as the body of an if statement",77:"Class declaration can't appear in single-statement context",78:"Invalid left-hand side in for-%0",79:"Invalid assignment in for-%0",80:"for await (... of ...) is only valid in async functions and async generators",81:"The first token after the template expression should be a continuation of the template",83:"`let` declaration not allowed here and `let` cannot be a regular var name in strict mode",82:"`let \n [` is a restricted production at the start of a statement",84:"Catch clause requires exactly one parameter, not more (and no trailing comma)",85:"Catch clause parameter does not support default values",86:"Missing catch or finally after try",87:"More than one default clause in switch statement",88:"Illegal newline after throw",89:"Strict mode code may not include a with statement",90:"Illegal return statement",91:"The left hand side of the for-header binding declaration is not destructible",92:"new.target only allowed within functions",94:"'#' not followed by identifier",100:"Invalid keyword",99:"Can not use 'let' as a class name",98:"'A lexical declaration can't define a 'let' binding",97:"Can not use `let` as variable name in strict mode",95:"'%0' may not be used as an identifier in this context",96:"Await is only valid in async functions",101:"The %0 keyword can only be used with the module goal",102:"Unicode codepoint must not be greater than 0x10FFFF",103:"%0 source must be string",104:"Only a identifier can be used to indicate alias",105:"Only '*' or '{...}' can be imported after default",106:"Trailing decorator may be followed by method",107:"Decorators can't be used with a constructor",109:"HTML comments are only allowed with web compatibility (Annex B)",110:"The identifier 'let' must not be in expression position in strict mode",111:"Cannot assign to `eval` and `arguments` in strict mode",112:"The left-hand side of a for-of loop may not start with 'let'",113:"Block body arrows can not be immediately invoked without a group",114:"Block body arrows can not be immediately accessed without a group",115:"Unexpected strict mode reserved word",116:"Unexpected eval or arguments in strict mode",117:"Decorators must not be followed by a semicolon",118:"Calling delete on expression not allowed in strict mode",119:"Pattern can not have a tail",121:"Can not have a `yield` expression on the left side of a ternary",122:"An arrow function can not have a postfix update operator",123:"Invalid object literal key character after generator star",124:"Private fields can not be deleted",126:"Classes may not have a field called constructor",125:"Classes may not have a private element named constructor",127:"A class field initializer may not contain arguments",128:"Generators can only be declared at the top level or inside a block",129:"Async methods are a restricted production and cannot have a newline following it",130:"Unexpected character after object literal property name",132:"Invalid key token",133:"Label '%0' has already been declared",134:"continue statement must be nested within an iteration statement",135:"Undefined label '%0'",136:"Trailing comma is disallowed inside import(...) arguments",137:"import() requires exactly one argument",138:"Cannot use new with import(...)",139:"... is not allowed in import()",140:"Expected '=>'",141:"Duplicate binding '%0'",142:"Cannot export a duplicate name '%0'",145:"Duplicate %0 for-binding",143:"Exported binding '%0' needs to refer to a top-level declared variable",144:"Unexpected private field",148:"Numeric separators are not allowed at the end of numeric literals",147:"Only one underscore is allowed as numeric separator",149:"JSX value should be either an expression or a quoted JSX text",150:"Expected corresponding JSX closing tag for %0",151:"Adjacent JSX elements must be wrapped in an enclosing tag",152:"JSX attributes must only be assigned a non-empty 'expression'",153:"'%0' has already been declared",154:"'%0' shadowed a catch clause binding",155:"Dot property must be an identifier",156:"Encountered invalid input after spread/rest argument",157:"Catch without try",158:"Finally without try",159:"Expected corresponding closing tag for JSX fragment",160:"Coalescing and logical operators used together in the same expression must be disambiguated with parentheses",161:"Invalid tagged template on optional chain",162:"Invalid optional chain from super property",163:"Invalid optional chain from new expression",164:'Cannot use "import.meta" outside a module',165:"Leading decorators must be attached to a class declaration"},k2=class extends SyntaxError{constructor(e,n,t,i,...o){let l="["+n+":"+t+"]: "+je[i].replace(/%(\d+)/g,(f,c)=>o[c]);super(`${l}`),this.index=e,this.line=n,this.column=t,this.description=l,this.loc={line:n,column:t}}};function d(u,e,...n){throw new k2(u.index,u.line,u.column,e,...n)}function z2(u){throw new k2(u.index,u.line,u.column,u.type,u.params)}function h2(u,e,n,t,...i){throw new k2(u,e,n,t,...i)}function A2(u,e,n,t){throw new k2(u,e,n,t)}var E2=((u,e)=>{let n=new Uint32Array(104448),t=0,i=0;for(;t<3540;){let o=u[t++];if(o<0)i-=o;else{let l=u[t++];o&2&&(l=e[l]),o&1?n.fill(l,i,i+=u[t++]):n[i++]=l}}return n})([-1,2,24,2,25,2,5,-1,0,77595648,3,44,2,3,0,14,2,57,2,58,3,0,3,0,3168796671,0,4294956992,2,1,2,0,2,59,3,0,4,0,4294966523,3,0,4,2,16,2,60,2,0,0,4294836735,0,3221225471,0,4294901942,2,61,0,*********,3,0,2,0,4294951935,3,0,2,0,2683305983,0,2684354047,2,17,2,0,0,4294961151,3,0,2,2,19,2,0,0,*********,2,0,2,131,2,6,2,56,-1,2,37,0,4294443263,2,1,3,0,3,0,4294901711,2,39,0,4089839103,0,2961209759,0,1342439375,0,4294543342,0,3547201023,0,1577204103,0,4194240,0,4294688750,2,2,0,80831,0,4261478351,0,4294549486,2,2,0,2967484831,0,196559,0,3594373100,0,3288319768,0,8469959,2,194,2,3,0,3825204735,0,123747807,0,65487,0,4294828015,0,4092591615,0,1080049119,0,458703,2,3,2,0,0,2163244511,0,4227923919,0,4236247022,2,66,0,4284449919,0,851904,2,4,2,11,0,67076095,-1,2,67,0,1073741743,0,4093591391,-1,0,50331649,0,3265266687,2,32,0,4294844415,0,4278190047,2,18,2,129,-1,3,0,2,2,21,2,0,2,9,2,0,2,14,2,15,3,0,10,2,69,2,0,2,70,2,71,2,72,2,0,2,73,2,0,2,10,0,261632,2,23,3,0,2,2,12,2,4,3,0,18,2,74,2,5,3,0,2,2,75,0,2088959,2,27,2,8,0,909311,3,0,2,0,814743551,2,41,0,67057664,3,0,2,2,40,2,0,2,28,2,0,2,29,2,7,0,268374015,2,26,2,49,2,0,2,76,0,134153215,-1,2,6,2,0,2,7,0,2684354559,0,67044351,0,3221160064,0,1,-1,3,0,2,2,42,0,1046528,3,0,3,2,8,2,0,2,51,0,4294960127,2,9,2,38,2,10,0,4294377472,2,11,3,0,7,0,4227858431,3,0,8,2,12,2,0,2,78,2,9,2,0,2,79,2,80,2,81,-1,2,124,0,1048577,2,82,2,13,-1,2,13,0,131042,2,83,2,84,2,85,2,0,2,33,-83,2,0,2,53,2,7,3,0,4,0,1046559,2,0,2,14,2,0,0,2147516671,2,20,3,86,2,2,0,-16,2,87,0,524222462,2,4,2,0,0,4269801471,2,4,2,0,2,15,2,77,2,16,3,0,2,2,47,2,0,-1,2,17,-16,3,0,206,-2,3,0,655,2,18,3,0,36,2,68,-1,2,17,2,9,3,0,8,2,89,2,121,2,0,0,3220242431,3,0,3,2,19,2,90,2,91,3,0,2,2,92,2,0,2,93,2,94,2,0,0,4351,2,0,2,8,3,0,2,0,67043391,0,3909091327,2,0,2,22,2,8,2,18,3,0,2,0,67076097,2,7,2,0,2,20,0,67059711,0,4236247039,3,0,2,0,939524103,0,8191999,2,97,2,98,2,15,2,21,3,0,3,0,67057663,3,0,349,2,99,2,100,2,6,-264,3,0,11,2,22,3,0,2,2,31,-1,0,3774349439,2,101,2,102,3,0,2,2,19,2,103,3,0,10,2,9,2,17,2,0,2,45,2,0,2,30,2,104,2,23,0,1638399,2,172,2,105,3,0,3,2,18,2,24,2,25,2,5,2,26,2,0,2,7,2,106,-1,2,107,2,108,2,109,-1,3,0,3,2,11,-2,2,0,2,27,-3,2,150,-4,2,18,2,0,2,35,0,1,2,0,2,62,2,28,2,11,2,9,2,0,2,110,-1,3,0,4,2,9,2,21,2,111,2,6,2,0,2,112,2,0,2,48,-4,3,0,9,2,20,2,29,2,30,-4,2,113,2,114,2,29,2,20,2,7,-2,2,115,2,29,2,31,-2,2,0,2,116,-2,0,4277137519,0,2269118463,-1,3,18,2,-1,2,32,2,36,2,0,3,29,2,2,34,2,19,-3,3,0,2,2,33,-1,2,0,2,34,2,0,2,34,2,0,2,46,-10,2,0,0,203775,-2,2,18,2,43,2,35,-2,2,17,2,117,2,20,3,0,2,2,36,0,2147549120,2,0,2,11,2,17,2,135,2,0,2,37,2,52,0,5242879,3,0,2,0,402644511,-1,2,120,0,1090519039,-2,2,122,2,38,2,0,0,67045375,2,39,0,4226678271,0,3766565279,0,2039759,-4,3,0,2,0,3288270847,0,3,3,0,2,0,67043519,-5,2,0,0,4282384383,0,1056964609,-1,3,0,2,0,67043345,-1,2,0,2,40,2,41,-1,2,10,2,42,-6,2,0,2,11,-3,3,0,2,0,2147484671,2,125,0,4190109695,2,50,-2,2,126,0,4244635647,0,27,2,0,2,7,2,43,2,0,2,63,-1,2,0,2,40,-8,2,54,2,44,0,67043329,2,127,2,45,0,8388351,-2,2,128,0,3028287487,2,46,2,130,0,33259519,2,41,-9,2,20,-5,2,64,-2,3,0,28,2,31,-3,3,0,3,2,47,3,0,6,2,48,-85,3,0,33,2,47,-126,3,0,18,2,36,-269,3,0,17,2,40,2,7,2,41,-2,2,17,2,49,2,0,2,20,2,50,2,132,2,23,-21,3,0,2,-4,3,0,2,0,4294936575,2,0,0,4294934783,-2,0,196635,3,0,191,2,51,3,0,38,2,29,-1,2,33,-279,3,0,8,2,7,-1,2,133,2,52,3,0,11,2,6,-72,3,0,3,2,134,0,1677656575,-166,0,4161266656,0,4071,0,15360,-4,0,28,-13,3,0,2,2,37,2,0,2,136,2,137,2,55,2,0,2,138,2,139,2,140,3,0,10,2,141,2,142,2,15,3,37,2,3,53,2,3,54,2,0,4294954999,2,0,-16,2,0,2,88,2,0,0,2105343,0,4160749584,0,65534,-42,0,4194303871,0,2011,-6,2,0,0,1073684479,0,17407,-11,2,0,2,31,-40,3,0,6,0,8323103,-1,3,0,2,2,42,-37,2,55,2,144,2,145,2,146,2,147,2,148,-105,2,24,-32,3,0,1334,2,9,-1,3,0,129,2,27,3,0,6,2,9,3,0,180,2,149,3,0,233,0,1,-96,3,0,16,2,9,-47,3,0,154,2,56,-22381,3,0,7,2,23,-6130,3,5,2,-1,0,69207040,3,44,2,3,0,14,2,57,2,58,-3,0,3168731136,0,4294956864,2,1,2,0,2,59,3,0,4,0,4294966275,3,0,4,2,16,2,60,2,0,2,33,-1,2,17,2,61,-1,2,0,2,56,0,4294885376,3,0,2,0,3145727,0,2617294944,0,4294770688,2,23,2,62,3,0,2,0,131135,2,95,0,70256639,0,71303167,0,272,2,40,2,56,-1,2,37,2,30,-1,2,96,2,63,0,4278255616,0,4294836227,0,4294549473,0,600178175,0,2952806400,0,268632067,0,4294543328,0,57540095,0,1577058304,0,1835008,0,4294688736,2,65,2,64,0,33554435,2,123,2,65,2,151,0,131075,0,3594373096,0,67094296,2,64,-1,0,4294828e3,0,603979263,2,160,0,3,0,4294828001,0,602930687,2,183,0,393219,0,4294828016,0,671088639,0,2154840064,0,4227858435,0,4236247008,2,66,2,36,-1,2,4,0,917503,2,36,-1,2,67,0,537788335,0,4026531935,-1,0,1,-1,2,32,2,68,0,7936,-3,2,0,0,2147485695,0,1010761728,0,4292984930,0,16387,2,0,2,14,2,15,3,0,10,2,69,2,0,2,70,2,71,2,72,2,0,2,73,2,0,2,11,-1,2,23,3,0,2,2,12,2,4,3,0,18,2,74,2,5,3,0,2,2,75,0,253951,3,19,2,0,122879,2,0,2,8,0,276824064,-2,3,0,2,2,40,2,0,0,4294903295,2,0,2,29,2,7,-1,2,17,2,49,2,0,2,76,2,41,-1,2,20,2,0,2,27,-2,0,128,-2,2,77,2,8,0,4064,-1,2,119,0,4227907585,2,0,2,118,2,0,2,48,2,173,2,9,2,38,2,10,-1,0,74440192,3,0,6,-2,3,0,8,2,12,2,0,2,78,2,9,2,0,2,79,2,80,2,81,-3,2,82,2,13,-3,2,83,2,84,2,85,2,0,2,33,-83,2,0,2,53,2,7,3,0,4,0,817183,2,0,2,14,2,0,0,33023,2,20,3,86,2,-17,2,87,0,524157950,2,4,2,0,2,88,2,4,2,0,2,15,2,77,2,16,3,0,2,2,47,2,0,-1,2,17,-16,3,0,206,-2,3,0,655,2,18,3,0,36,2,68,-1,2,17,2,9,3,0,8,2,89,0,3072,2,0,0,2147516415,2,9,3,0,2,2,23,2,90,2,91,3,0,2,2,92,2,0,2,93,2,94,0,4294965179,0,7,2,0,2,8,2,91,2,8,-1,0,1761345536,2,95,0,4294901823,2,36,2,18,2,96,2,34,2,166,0,2080440287,2,0,2,33,2,143,0,3296722943,2,0,0,1046675455,0,939524101,0,1837055,2,97,2,98,2,15,2,21,3,0,3,0,7,3,0,349,2,99,2,100,2,6,-264,3,0,11,2,22,3,0,2,2,31,-1,0,2700607615,2,101,2,102,3,0,2,2,19,2,103,3,0,10,2,9,2,17,2,0,2,45,2,0,2,30,2,104,-3,2,105,3,0,3,2,18,-1,3,5,2,2,26,2,0,2,7,2,106,-1,2,107,2,108,2,109,-1,3,0,3,2,11,-2,2,0,2,27,-8,2,18,2,0,2,35,-1,2,0,2,62,2,28,2,29,2,9,2,0,2,110,-1,3,0,4,2,9,2,17,2,111,2,6,2,0,2,112,2,0,2,48,-4,3,0,9,2,20,2,29,2,30,-4,2,113,2,114,2,29,2,20,2,7,-2,2,115,2,29,2,31,-2,2,0,2,116,-2,0,4277075969,2,29,-1,3,18,2,-1,2,32,2,117,2,0,3,29,2,2,34,2,19,-3,3,0,2,2,33,-1,2,0,2,34,2,0,2,34,2,0,2,48,-10,2,0,0,197631,-2,2,18,2,43,2,118,-2,2,17,2,117,2,20,2,119,2,51,-2,2,119,2,23,2,17,2,33,2,119,2,36,0,4294901904,0,4718591,2,119,2,34,0,335544350,-1,2,120,2,121,-2,2,122,2,38,2,7,-1,2,123,2,65,0,3758161920,0,3,-4,2,0,2,27,0,2147485568,0,3,2,0,2,23,0,176,-5,2,0,2,47,2,186,-1,2,0,2,23,2,197,-1,2,0,0,16779263,-2,2,11,-7,2,0,2,121,-3,3,0,2,2,124,2,125,0,2147549183,0,2,-2,2,126,2,35,0,10,0,4294965249,0,67633151,0,4026597376,2,0,0,536871935,-1,2,0,2,40,-8,2,54,2,47,0,1,2,127,2,23,-3,2,128,2,35,2,129,2,130,0,16778239,-10,2,34,-5,2,64,-2,3,0,28,2,31,-3,3,0,3,2,47,3,0,6,2,48,-85,3,0,33,2,47,-126,3,0,18,2,36,-269,3,0,17,2,40,2,7,-3,2,17,2,131,2,0,2,23,2,48,2,132,2,23,-21,3,0,2,-4,3,0,2,0,67583,-1,2,103,-2,0,11,3,0,191,2,51,3,0,38,2,29,-1,2,33,-279,3,0,8,2,7,-1,2,133,2,52,3,0,11,2,6,-72,3,0,3,2,134,2,135,-187,3,0,2,2,37,2,0,2,136,2,137,2,55,2,0,2,138,2,139,2,140,3,0,10,2,141,2,142,2,15,3,37,2,3,53,2,3,54,2,2,143,-73,2,0,0,1065361407,0,16384,-11,2,0,2,121,-40,3,0,6,2,117,-1,3,0,2,0,2063,-37,2,55,2,144,2,145,2,146,2,147,2,148,-138,3,0,1334,2,9,-1,3,0,129,2,27,3,0,6,2,9,3,0,180,2,149,3,0,233,0,1,-96,3,0,16,2,9,-47,3,0,154,2,56,-28517,2,0,0,1,-1,2,124,2,0,0,8193,-21,2,193,0,10255,0,4,-11,2,64,2,171,-1,0,71680,-1,2,161,0,4292900864,0,805306431,-5,2,150,-1,2,157,-1,0,6144,-2,2,127,-1,2,154,-1,0,2147532800,2,151,2,165,2,0,2,164,0,524032,0,4,-4,2,190,0,205128192,0,1333757536,0,2147483696,0,423953,0,747766272,0,2717763192,0,4286578751,0,278545,2,152,0,4294886464,0,33292336,0,417809,2,152,0,1327482464,0,4278190128,0,700594195,0,1006647527,0,4286497336,0,4160749631,2,153,0,469762560,0,4171219488,0,8323120,2,153,0,202375680,0,3214918176,0,4294508592,2,153,-1,0,983584,0,48,0,58720273,0,3489923072,0,10517376,0,4293066815,0,1,0,2013265920,2,177,2,0,0,2089,0,3221225552,0,201375904,2,0,-2,0,256,0,122880,0,16777216,2,150,0,4160757760,2,0,-6,2,167,-11,0,3263218176,-1,0,49664,0,2160197632,0,8388802,-1,0,12713984,-1,2,154,2,159,2,178,-2,2,162,-20,0,3758096385,-2,2,155,0,4292878336,2,90,2,169,0,4294057984,-2,2,163,2,156,2,175,-2,2,155,-1,2,182,-1,2,170,2,124,0,4026593280,0,14,0,4292919296,-1,2,158,0,939588608,-1,0,805306368,-1,2,124,0,1610612736,2,156,2,157,2,4,2,0,-2,2,158,2,159,-3,0,267386880,-1,2,160,0,7168,-1,0,65024,2,154,2,161,2,179,-7,2,168,-8,2,162,-1,0,1426112704,2,163,-1,2,164,0,271581216,0,2149777408,2,23,2,161,2,124,0,851967,2,180,-1,2,23,2,181,-4,2,158,-20,2,195,2,165,-56,0,3145728,2,185,-4,2,166,2,124,-4,0,32505856,-1,2,167,-1,0,2147385088,2,90,1,2155905152,2,-3,2,103,2,0,2,168,-2,2,169,-6,2,170,0,4026597375,0,1,-1,0,1,-1,2,171,-3,2,117,2,64,-2,2,166,-2,2,176,2,124,-878,2,159,-36,2,172,-1,2,201,-10,2,188,-5,2,174,-6,0,4294965251,2,27,-1,2,173,-1,2,174,-2,0,4227874752,-3,0,2146435072,2,159,-2,0,1006649344,2,124,-1,2,90,0,201375744,-3,0,134217720,2,90,0,4286677377,0,32896,-1,2,158,-3,2,175,-349,2,176,0,1920,2,177,3,0,264,-11,2,157,-2,2,178,2,0,0,520617856,0,2692743168,0,36,-3,0,524284,-11,2,23,-1,2,187,-1,2,184,0,3221291007,2,178,-1,2,202,0,2158720,-3,2,159,0,1,-4,2,124,0,3808625411,0,3489628288,2,200,0,1207959680,0,3221274624,2,0,-3,2,179,0,120,0,7340032,-2,2,180,2,4,2,23,2,163,3,0,4,2,159,-1,2,181,2,177,-1,0,8176,2,182,2,179,2,183,-1,0,4290773232,2,0,-4,2,163,2,189,0,15728640,2,177,-1,2,161,-1,0,4294934512,3,0,4,-9,2,90,2,170,2,184,3,0,4,0,704,0,1849688064,2,185,-1,2,124,0,4294901887,2,0,0,130547712,0,1879048192,2,199,3,0,2,-1,2,186,2,187,-1,0,17829776,0,2025848832,0,4261477888,-2,2,0,-1,0,4286580608,-1,0,29360128,2,192,0,16252928,0,3791388672,2,38,3,0,2,-2,2,196,2,0,-1,2,103,-1,0,66584576,-1,2,191,3,0,9,2,124,-1,0,4294755328,3,0,2,-1,2,161,2,178,3,0,2,2,23,2,188,2,90,-2,0,245760,0,2147418112,-1,2,150,2,203,0,4227923456,-1,2,164,2,161,2,90,-3,0,4292870145,0,262144,2,124,3,0,2,0,1073758848,2,189,-1,0,4227921920,2,190,0,68289024,0,528402016,0,4292927536,3,0,4,-2,0,268435456,2,91,-2,2,191,3,0,5,-1,2,192,2,163,2,0,-2,0,4227923936,2,62,-1,2,155,2,95,2,0,2,154,2,158,3,0,6,-1,2,177,3,0,3,-2,0,2146959360,0,9440640,0,104857600,0,4227923840,3,0,2,0,768,2,193,2,77,-2,2,161,-2,2,119,-1,2,155,3,0,8,0,512,0,8388608,2,194,2,172,2,187,0,4286578944,3,0,2,0,1152,0,1266679808,2,191,0,576,0,4261707776,2,95,3,0,9,2,155,3,0,5,2,16,-1,0,2147221504,-28,2,178,3,0,3,-3,0,4292902912,-6,2,96,3,0,85,-33,0,4294934528,3,0,126,-18,2,195,3,0,269,-17,2,155,2,124,2,198,3,0,2,2,23,0,4290822144,-2,0,67174336,0,520093700,2,17,3,0,21,-2,2,179,3,0,3,-2,0,30720,-1,0,32512,3,0,2,0,4294770656,-191,2,174,-38,2,170,2,0,2,196,3,0,279,-8,2,124,2,0,0,4294508543,0,65295,-11,2,177,3,0,72,-3,0,3758159872,0,201391616,3,0,155,-7,2,170,-1,0,384,-1,0,133693440,-3,2,196,-2,2,26,3,0,4,2,169,-2,2,90,2,155,3,0,4,-2,2,164,-1,2,150,0,335552923,2,197,-1,0,538974272,0,2214592512,0,132e3,-10,0,192,-8,0,12288,-21,0,134213632,0,4294901761,3,0,42,0,100663424,0,4294965284,3,0,6,-1,0,3221282816,2,198,3,0,11,-1,2,199,3,0,40,-6,0,4286578784,2,0,-2,0,1006694400,3,0,24,2,35,-1,2,94,3,0,2,0,1,2,163,3,0,6,2,197,0,4110942569,0,1432950139,0,2701658217,0,4026532864,0,4026532881,2,0,2,45,3,0,8,-1,2,158,-2,2,169,0,98304,0,65537,2,170,-5,0,4294950912,2,0,2,118,0,65528,2,177,0,4294770176,2,26,3,0,4,-30,2,174,0,3758153728,-3,2,169,-2,2,155,2,188,2,158,-1,2,191,-1,2,161,0,4294754304,3,0,2,-3,0,33554432,-2,2,200,-3,2,169,0,4175478784,2,201,0,4286643712,0,4286644216,2,0,-4,2,202,-1,2,165,0,4227923967,3,0,32,-1334,2,163,2,0,-129,2,94,-6,2,163,-180,2,203,-233,2,4,3,0,96,-16,2,163,3,0,47,-154,2,165,3,0,22381,-7,2,17,3,0,6128],[4294967295,4294967291,4092460543,4294828031,4294967294,134217726,268435455,2147483647,1048575,1073741823,3892314111,134217727,1061158911,536805376,4294910143,4160749567,4294901759,4294901760,536870911,262143,8388607,4294902783,4294918143,65535,67043328,2281701374,4294967232,2097151,4294903807,4194303,255,67108863,4294967039,511,524287,131071,127,4292870143,4294902271,4294549487,33554431,1023,67047423,4294901888,4286578687,4294770687,67043583,32767,15,2047999,67043343,16777215,4294902e3,4294934527,4294966783,4294967279,2047,262083,20511,4290772991,41943039,493567,4294959104,603979775,65536,602799615,805044223,4294965206,8191,1031749119,4294917631,2134769663,4286578493,4282253311,4294942719,33540095,4294905855,4294967264,2868854591,1608515583,265232348,534519807,2147614720,1060109444,4093640016,17376,2139062143,224,4169138175,4294909951,4286578688,4294967292,4294965759,2044,4292870144,4294966272,4294967280,8289918,4294934399,4294901775,4294965375,1602223615,4294967259,4294443008,268369920,4292804608,486341884,4294963199,3087007615,1073692671,4128527,4279238655,4294902015,4294966591,2445279231,3670015,3238002687,31,63,4294967288,4294705151,4095,3221208447,4294549472,2147483648,4285526655,4294966527,4294705152,4294966143,64,4294966719,16383,3774873592,458752,536807423,67043839,3758096383,3959414372,3755993023,2080374783,4294835295,4294967103,4160749565,4087,184024726,2862017156,1593309078,268434431,268434414,4294901763,536870912,2952790016,202506752,139264,402653184,4261412864,4227922944,49152,61440,3758096384,117440512,65280,3233808384,3221225472,2097152,4294965248,32768,57152,67108864,4293918720,4290772992,25165824,57344,4227915776,4278190080,4227907584,65520,4026531840,4227858432,4160749568,3758129152,4294836224,63488,1073741824,4294967040,4194304,251658240,196608,4294963200,64512,417808,4227923712,12582912,50331648,65472,4294967168,4294966784,16,4294917120,2080374784,4096,65408,524288,65532]);function h(u){return u.column++,u.currentChar=u.source.charCodeAt(++u.index)}function Xe(u,e){if((e&64512)!==55296)return 0;let n=u.source.charCodeAt(u.index+1);return(n&64512)!==56320?0:(e=u.currentChar=65536+((e&1023)<<10)+(n&1023),E2[(e>>>5)+0]>>>e&31&1||d(u,18,G(e)),u.index++,u.column++,1)}function p2(u,e){u.currentChar=u.source.charCodeAt(++u.index),u.flags|=1,e&4||(u.column=0,u.line++)}function c2(u){u.flags|=1,u.currentChar=u.source.charCodeAt(++u.index),u.column=0,u.line++}function ze(u){return u===160||u===65279||u===133||u===5760||u>=8192&&u<=8203||u===8239||u===8287||u===12288||u===8201||u===65519}function G(u){return u<=65535?String.fromCharCode(u):String.fromCharCode(u>>>10)+String.fromCharCode(u&1023)}function H(u){return u<65?u-48:u-65+10&15}function He(u){switch(u){case 134283266:return"NumericLiteral";case 134283267:return"StringLiteral";case 86021:case 86022:return"BooleanLiteral";case 86023:return"NullLiteral";case 65540:return"RegularExpression";case 67174408:case 67174409:case 132:return"TemplateLiteral";default:return(u&143360)===143360?"Identifier":(u&4096)===4096?"Keyword":"Punctuator"}}var L=[0,0,0,0,0,0,0,0,0,0,1032,0,0,2056,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8192,0,3,0,0,8192,0,0,0,256,0,33024,0,0,242,242,114,114,114,114,114,114,594,594,0,0,16384,0,0,0,0,67,67,67,67,67,67,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,0,1,0,0,4099,0,71,71,71,71,71,71,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,16384,0,0,0,0],Ke=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0],Lu=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0];function Q2(u){return u<=127?Ke[u]:E2[(u>>>5)+34816]>>>u&31&1}function U2(u){return u<=127?Lu[u]:E2[(u>>>5)+0]>>>u&31&1||u===8204||u===8205}var Iu=["SingleLine","MultiLine","HTMLOpen","HTMLClose","HashbangComment"];function $e(u){let e=u.source;u.currentChar===35&&e.charCodeAt(u.index+1)===33&&(h(u),h(u),uu(u,e,0,4,u.tokenPos,u.linePos,u.colPos))}function Eu(u,e,n,t,i,o,l,f){return t&2048&&d(u,0),uu(u,e,n,i,o,l,f)}function uu(u,e,n,t,i,o,l){let{index:f}=u;for(u.tokenPos=u.index,u.linePos=u.line,u.colPos=u.column;u.index<u.end;){if(L[u.currentChar]&8){let c=u.currentChar===13;c2(u),c&&u.index<u.end&&u.currentChar===10&&(u.currentChar=e.charCodeAt(++u.index));break}else if((u.currentChar^8232)<=1){c2(u);break}h(u),u.tokenPos=u.index,u.linePos=u.line,u.colPos=u.column}if(u.onComment){let c={start:{line:o,column:l},end:{line:u.linePos,column:u.colPos}};u.onComment(Iu[t&255],e.slice(f,u.tokenPos),i,u.tokenPos,c)}return n|1}function We(u,e,n){let{index:t}=u;for(;u.index<u.end;)if(u.currentChar<43){let i=!1;for(;u.currentChar===42;)if(i||(n&=-5,i=!0),h(u)===47){if(h(u),u.onComment){let o={start:{line:u.linePos,column:u.colPos},end:{line:u.line,column:u.column}};u.onComment(Iu[1],e.slice(t,u.index-2),t-2,u.index,o)}return u.tokenPos=u.index,u.linePos=u.line,u.colPos=u.column,n}if(i)continue;L[u.currentChar]&8?u.currentChar===13?(n|=5,c2(u)):(p2(u,n),n=n&-5|1):h(u)}else(u.currentChar^8232)<=1?(n=n&-5|1,c2(u)):(n&=-5,h(u));d(u,16)}function _e(u,e){let n=u.index,t=0;u:for(;;){let g=u.currentChar;if(h(u),t&1)t&=-2;else switch(g){case 47:if(t)break;break u;case 92:t|=1;break;case 91:t|=2;break;case 93:t&=1;break;case 13:case 10:case 8232:case 8233:d(u,32)}if(u.index>=u.source.length)return d(u,32)}let i=u.index-1,o=0,l=u.currentChar,{index:f}=u;for(;U2(l);){switch(l){case 103:o&2&&d(u,34,"g"),o|=2;break;case 105:o&1&&d(u,34,"i"),o|=1;break;case 109:o&4&&d(u,34,"m"),o|=4;break;case 117:o&16&&d(u,34,"u"),o|=16;break;case 121:o&8&&d(u,34,"y"),o|=8;break;case 115:o&32&&d(u,34,"s"),o|=32;break;case 100:o&64&&d(u,34,"d"),o|=64;break;default:d(u,33)}l=h(u)}let c=u.source.slice(f,u.index),a=u.source.slice(n,i);return u.tokenRegExp={pattern:a,flags:c},e&512&&(u.tokenRaw=u.source.slice(u.tokenPos,u.index)),u.tokenValue=Ye(u,a,c),65540}function Ye(u,e,n){try{return new RegExp(e,n)}catch{try{return new RegExp(e,n.replace("d","")),null}catch{d(u,32)}}}function Qe(u,e,n){let{index:t}=u,i="",o=h(u),l=u.index;for(;!(L[o]&8);){if(o===n)return i+=u.source.slice(l,u.index),h(u),e&512&&(u.tokenRaw=u.source.slice(t,u.index)),u.tokenValue=i,134283267;if((o&8)===8&&o===92){if(i+=u.source.slice(l,u.index),o=h(u),o<127||o===8232||o===8233){let f=Nu(u,e,o);f>=0?i+=G(f):Vu(u,f,0)}else i+=G(o);l=u.index+1}u.index>=u.end&&d(u,14),o=h(u)}d(u,14)}function Nu(u,e,n){switch(n){case 98:return 8;case 102:return 12;case 114:return 13;case 110:return 10;case 116:return 9;case 118:return 11;case 13:if(u.index<u.end){let t=u.source.charCodeAt(u.index+1);t===10&&(u.index=u.index+1,u.currentChar=t)}case 10:case 8232:case 8233:return u.column=-1,u.line++,-1;case 48:case 49:case 50:case 51:{let t=n-48,i=u.index+1,o=u.column+1;if(i<u.end){let l=u.source.charCodeAt(i);if(L[l]&32){if(e&1024)return-2;if(u.currentChar=l,t=t<<3|l-48,i++,o++,i<u.end){let f=u.source.charCodeAt(i);L[f]&32&&(u.currentChar=f,t=t<<3|f-48,i++,o++)}u.flags|=64,u.index=i-1,u.column=o-1}else if((t!==0||L[l]&512)&&e&1024)return-2}return t}case 52:case 53:case 54:case 55:{if(e&1024)return-2;let t=n-48,i=u.index+1,o=u.column+1;if(i<u.end){let l=u.source.charCodeAt(i);L[l]&32&&(t=t<<3|l-48,u.currentChar=l,u.index=i,u.column=o)}return u.flags|=64,t}case 120:{let t=h(u);if(!(L[t]&64))return-4;let i=H(t),o=h(u);if(!(L[o]&64))return-4;let l=H(o);return i<<4|l}case 117:{let t=h(u);if(u.currentChar===123){let i=0;for(;L[h(u)]&64;)if(i=i<<4|H(u.currentChar),i>1114111)return-5;return u.currentChar<1||u.currentChar!==125?-4:i}else{if(!(L[t]&64))return-4;let i=u.source.charCodeAt(u.index+1);if(!(L[i]&64))return-4;let o=u.source.charCodeAt(u.index+2);if(!(L[o]&64))return-4;let l=u.source.charCodeAt(u.index+3);return L[l]&64?(u.index+=3,u.column+=3,u.currentChar=u.source.charCodeAt(u.index),H(t)<<12|H(i)<<8|H(o)<<4|H(l)):-4}}case 56:case 57:if(!(e&256))return-3;default:return n}}function Vu(u,e,n){switch(e){case-1:return;case-2:d(u,n?2:1);case-3:d(u,13);case-4:d(u,6);case-5:d(u,102)}}function Ru(u,e){let{index:n}=u,t=67174409,i="",o=h(u);for(;o!==96;){if(o===36&&u.source.charCodeAt(u.index+1)===123){h(u),t=67174408;break}else if((o&8)===8&&o===92)if(o=h(u),o>126)i+=G(o);else{let l=Nu(u,e|1024,o);if(l>=0)i+=G(l);else if(l!==-1&&e&65536){i=void 0,o=Ze(u,o),o<0&&(t=67174408);break}else Vu(u,l,1)}else u.index<u.end&&o===13&&u.source.charCodeAt(u.index)===10&&(i+=G(o),u.currentChar=u.source.charCodeAt(++u.index)),((o&83)<3&&o===10||(o^8232)<=1)&&(u.column=-1,u.line++),i+=G(o);u.index>=u.end&&d(u,15),o=h(u)}return h(u),u.tokenValue=i,u.tokenRaw=u.source.slice(n+1,u.index-(t===67174409?1:2)),t}function Ze(u,e){for(;e!==96;){switch(e){case 36:{let n=u.index+1;if(n<u.end&&u.source.charCodeAt(n)===123)return u.index=n,u.column++,-e;break}case 10:case 8232:case 8233:u.column=-1,u.line++}u.index>=u.end&&d(u,15),e=h(u)}return e}function re(u,e){return u.index>=u.end&&d(u,0),u.index--,u.column--,Ru(u,e)}function wu(u,e,n){let t=u.currentChar,i=0,o=9,l=n&64?0:1,f=0,c=0;if(n&64)i="."+I2(u,t),t=u.currentChar,t===110&&d(u,11);else{if(t===48)if(t=h(u),(t|32)===120){for(n=136,t=h(u);L[t]&4160;){if(t===95){c||d(u,147),c=0,t=h(u);continue}c=1,i=i*16+H(t),f++,t=h(u)}(f===0||!c)&&d(u,f===0?19:148)}else if((t|32)===111){for(n=132,t=h(u);L[t]&4128;){if(t===95){c||d(u,147),c=0,t=h(u);continue}c=1,i=i*8+(t-48),f++,t=h(u)}(f===0||!c)&&d(u,f===0?0:148)}else if((t|32)===98){for(n=130,t=h(u);L[t]&4224;){if(t===95){c||d(u,147),c=0,t=h(u);continue}c=1,i=i*2+(t-48),f++,t=h(u)}(f===0||!c)&&d(u,f===0?0:148)}else if(L[t]&32)for(e&1024&&d(u,1),n=1;L[t]&16;){if(L[t]&512){n=32,l=0;break}i=i*8+(t-48),t=h(u)}else L[t]&512?(e&1024&&d(u,1),u.flags|=64,n=32):t===95&&d(u,0);if(n&48){if(l){for(;o>=0&&L[t]&4112;){if(t===95){t=h(u),(t===95||n&32)&&A2(u.index,u.line,u.index+1,147),c=1;continue}c=0,i=10*i+(t-48),t=h(u),--o}if(c&&A2(u.index,u.line,u.index+1,148),o>=0&&!Q2(t)&&t!==46)return u.tokenValue=i,e&512&&(u.tokenRaw=u.source.slice(u.tokenPos,u.index)),134283266}i+=I2(u,t),t=u.currentChar,t===46&&(h(u)===95&&d(u,0),n=64,i+="."+I2(u,u.currentChar),t=u.currentChar)}}let a=u.index,g=0;if(t===110&&n&128)g=1,t=h(u);else if((t|32)===101){t=h(u),L[t]&256&&(t=h(u));let{index:m}=u;L[t]&16||d(u,10),i+=u.source.substring(a,m)+I2(u,t),t=u.currentChar}return(u.index<u.end&&L[t]&16||Q2(t))&&d(u,12),g?(u.tokenRaw=u.source.slice(u.tokenPos,u.index),u.tokenValue=BigInt(i),134283389):(u.tokenValue=n&15?i:n&32?parseFloat(u.source.substring(u.tokenPos,u.index)):+i,e&512&&(u.tokenRaw=u.source.slice(u.tokenPos,u.index)),134283266)}function I2(u,e){let n=0,t=u.index,i="";for(;L[e]&4112;){if(e===95){let{index:o}=u;e=h(u),e===95&&A2(u.index,u.line,u.index+1,147),n=1,i+=u.source.substring(t,o),t=u.index;continue}n=0,e=h(u)}return n&&A2(u.index,u.line,u.index+1,148),i+u.source.substring(t,u.index)}var U=["end of source","identifier","number","string","regular expression","false","true","null","template continuation","template tail","=>","(","{",".","...","}",")",";",",","[","]",":","?","'",'"',"</","/>","++","--","=","<<=",">>=",">>>=","**=","+=","-=","*=","/=","%=","^=","|=","&=","||=","&&=","??=","typeof","delete","void","!","~","+","-","in","instanceof","*","%","/","**","&&","||","===","!==","==","!=","<=",">=","<",">","<<",">>",">>>","&","|","^","var","let","const","break","case","catch","class","continue","debugger","default","do","else","export","extends","finally","for","function","if","import","new","return","super","switch","this","throw","try","while","with","implements","interface","package","private","protected","public","static","yield","as","async","await","constructor","get","set","from","of","enum","eval","arguments","escaped keyword","escaped future reserved keyword","reserved if strict","#","BigIntLiteral","??","?.","WhiteSpace","Illegal","LineTerminator","PrivateField","Template","@","target","meta","LineFeed","Escaped","JSXText"],Ou=Object.create(null,{this:{value:86113},function:{value:86106},if:{value:20571},return:{value:20574},var:{value:86090},else:{value:20565},for:{value:20569},new:{value:86109},in:{value:8738868},typeof:{value:16863277},while:{value:20580},case:{value:20558},break:{value:20557},try:{value:20579},catch:{value:20559},delete:{value:16863278},throw:{value:86114},switch:{value:86112},continue:{value:20561},default:{value:20563},instanceof:{value:8476725},do:{value:20564},void:{value:16863279},finally:{value:20568},async:{value:209007},await:{value:209008},class:{value:86096},const:{value:86092},constructor:{value:12401},debugger:{value:20562},export:{value:20566},extends:{value:20567},false:{value:86021},from:{value:12404},get:{value:12402},implements:{value:36966},import:{value:86108},interface:{value:36967},let:{value:241739},null:{value:86023},of:{value:274549},package:{value:36968},private:{value:36969},protected:{value:36970},public:{value:36971},set:{value:12403},static:{value:36972},super:{value:86111},true:{value:86022},with:{value:20581},yield:{value:241773},enum:{value:86134},eval:{value:537079927},as:{value:77934},arguments:{value:537079928},target:{value:143494},meta:{value:143495}});function Su(u,e,n){for(;Lu[h(u)];);return u.tokenValue=u.source.slice(u.tokenPos,u.index),u.currentChar!==92&&u.currentChar<=126?Ou[u.tokenValue]||208897:eu(u,e,0,n)}function Ge(u,e){let n=Uu(u);return U2(n)||d(u,4),u.tokenValue=G(n),eu(u,e,1,L[n]&4)}function eu(u,e,n,t){let i=u.index;for(;u.index<u.end;)if(u.currentChar===92){u.tokenValue+=u.source.slice(i,u.index),n=1;let l=Uu(u);U2(l)||d(u,4),t=t&&L[l]&4,u.tokenValue+=G(l),i=u.index}else if(U2(u.currentChar)||Xe(u,u.currentChar))h(u);else break;u.index<=u.end&&(u.tokenValue+=u.source.slice(i,u.index));let o=u.tokenValue.length;if(t&&o>=2&&o<=11){let l=Ou[u.tokenValue];return l===void 0?208897:n?l===209008?e&4196352?121:l:e&1024?l===36972||(l&36864)===36864?122:(l&20480)===20480?e&1073741824&&!(e&8192)?l:121:143483:e&1073741824&&!(e&8192)&&(l&20480)===20480?l:l===241773?e&1073741824?143483:e&2097152?121:l:l===209007?143483:(l&36864)===36864?l:121:l}return 208897}function xe(u){return Q2(h(u))||d(u,94),131}function Uu(u){return u.source.charCodeAt(u.index+1)!==117&&d(u,4),u.currentChar=u.source.charCodeAt(u.index+=2),pe(u)}function pe(u){let e=0,n=u.currentChar;if(n===123){let l=u.index-2;for(;L[h(u)]&64;)e=e<<4|H(u.currentChar),e>1114111&&A2(l,u.line,u.index+1,102);return u.currentChar!==125&&A2(l,u.line,u.index-1,6),h(u),e}L[n]&64||d(u,6);let t=u.source.charCodeAt(u.index+1);L[t]&64||d(u,6);let i=u.source.charCodeAt(u.index+2);L[i]&64||d(u,6);let o=u.source.charCodeAt(u.index+3);return L[o]&64||d(u,6),e=H(n)<<12|H(t)<<8|H(i)<<4|H(o),u.currentChar=u.source.charCodeAt(u.index+=4),e}var Mu=[129,129,129,129,129,129,129,129,129,128,136,128,128,130,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,128,16842800,134283267,131,208897,8457015,8455751,134283267,67174411,16,8457014,25233970,18,25233971,67108877,8457016,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,134283266,21,1074790417,8456258,1077936157,8456259,22,133,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,208897,69271571,137,20,8455497,208897,132,4096,4096,4096,4096,4096,4096,4096,208897,4096,208897,208897,4096,208897,4096,208897,4096,208897,4096,4096,4096,208897,4096,4096,208897,4096,4096,2162700,8455240,1074790415,16842801,129];function D(u,e){if(u.flags=(u.flags|1)^1,u.startPos=u.index,u.startColumn=u.column,u.startLine=u.line,u.token=Ju(u,e,0),u.onToken&&u.token!==1048576){let n={start:{line:u.linePos,column:u.colPos},end:{line:u.line,column:u.column}};u.onToken(He(u.token),u.tokenPos,u.index,n)}}function Ju(u,e,n){let t=u.index===0,i=u.source,o=u.index,l=u.line,f=u.column;for(;u.index<u.end;){u.tokenPos=u.index,u.colPos=u.column,u.linePos=u.line;let c=u.currentChar;if(c<=126){let a=Mu[c];switch(a){case 67174411:case 16:case 2162700:case 1074790415:case 69271571:case 20:case 21:case 1074790417:case 18:case 16842801:case 133:case 129:return h(u),a;case 208897:return Su(u,e,0);case 4096:return Su(u,e,1);case 134283266:return wu(u,e,144);case 134283267:return Qe(u,e,c);case 132:return Ru(u,e);case 137:return Ge(u,e);case 131:return xe(u);case 128:h(u);break;case 130:n|=5,c2(u);break;case 136:p2(u,n),n=n&-5|1;break;case 8456258:let g=h(u);if(u.index<u.end){if(g===60)return u.index<u.end&&h(u)===61?(h(u),4194334):8456516;if(g===61)return h(u),8456256;if(g===33){let s=u.index+1;if(s+1<u.end&&i.charCodeAt(s)===45&&i.charCodeAt(s+1)==45){u.column+=3,u.currentChar=i.charCodeAt(u.index+=3),n=Eu(u,i,n,e,2,u.tokenPos,u.linePos,u.colPos),o=u.tokenPos,l=u.linePos,f=u.colPos;continue}return 8456258}if(g===47){if(!(e&16))return 8456258;let s=u.index+1;if(s<u.end&&(g=i.charCodeAt(s),g===42||g===47))break;return h(u),25}}return 8456258;case 1077936157:{h(u);let s=u.currentChar;return s===61?h(u)===61?(h(u),8455996):8455998:s===62?(h(u),10):1077936157}case 16842800:return h(u)!==61?16842800:h(u)!==61?8455999:(h(u),8455997);case 8457015:return h(u)!==61?8457015:(h(u),4194342);case 8457014:{if(h(u),u.index>=u.end)return 8457014;let s=u.currentChar;return s===61?(h(u),4194340):s!==42?8457014:h(u)!==61?8457273:(h(u),4194337)}case 8455497:return h(u)!==61?8455497:(h(u),4194343);case 25233970:{h(u);let s=u.currentChar;return s===43?(h(u),33619995):s===61?(h(u),4194338):25233970}case 25233971:{h(u);let s=u.currentChar;if(s===45){if(h(u),(n&1||t)&&u.currentChar===62){e&256||d(u,109),h(u),n=Eu(u,i,n,e,3,o,l,f),o=u.tokenPos,l=u.linePos,f=u.colPos;continue}return 33619996}return s===61?(h(u),4194339):25233971}case 8457016:{if(h(u),u.index<u.end){let s=u.currentChar;if(s===47){h(u),n=uu(u,i,n,0,u.tokenPos,u.linePos,u.colPos),o=u.tokenPos,l=u.linePos,f=u.colPos;continue}if(s===42){h(u),n=We(u,i,n),o=u.tokenPos,l=u.linePos,f=u.colPos;continue}if(e&32768)return _e(u,e);if(s===61)return h(u),4259877}return 8457016}case 67108877:let m=h(u);if(m>=48&&m<=57)return wu(u,e,80);if(m===46){let s=u.index+1;if(s<u.end&&i.charCodeAt(s)===46)return u.column+=2,u.currentChar=i.charCodeAt(u.index+=2),14}return 67108877;case 8455240:{h(u);let s=u.currentChar;return s===124?(h(u),u.currentChar===61?(h(u),4194346):8979003):s===61?(h(u),4194344):8455240}case 8456259:{h(u);let s=u.currentChar;if(s===61)return h(u),8456257;if(s!==62)return 8456259;if(h(u),u.index<u.end){let k=u.currentChar;if(k===62)return h(u)===61?(h(u),4194336):8456518;if(k===61)return h(u),4194335}return 8456517}case 8455751:{h(u);let s=u.currentChar;return s===38?(h(u),u.currentChar===61?(h(u),4194347):8979258):s===61?(h(u),4194345):8455751}case 22:{let s=h(u);if(s===63)return h(u),u.currentChar===61?(h(u),4194348):276889982;if(s===46){let k=u.index+1;if(k<u.end&&(s=i.charCodeAt(k),!(s>=48&&s<=57)))return h(u),67108991}return 22}}}else{if((c^8232)<=1){n=n&-5|1,c2(u);continue}if((c&64512)===55296||E2[(c>>>5)+34816]>>>c&31&1)return(c&64512)===56320&&(c=(c&1023)<<10|c&1023|65536,E2[(c>>>5)+0]>>>c&31&1||d(u,18,G(c)),u.index++,u.currentChar=c),u.column++,u.tokenValue="",eu(u,e,0,0);if(ze(c)){h(u);continue}d(u,18,G(c))}}return 1048576}function u1(u,e){return u.startPos=u.tokenPos=u.index,u.startColumn=u.colPos=u.column,u.startLine=u.linePos=u.line,u.token=L[u.currentChar]&8192?e1(u,e):Ju(u,e,0),u.token}function e1(u,e){let n=u.currentChar,t=h(u),i=u.index;for(;t!==n;)u.index>=u.end&&d(u,14),t=h(u);return t!==n&&d(u,14),u.tokenValue=u.source.slice(i,u.index),h(u),e&512&&(u.tokenRaw=u.source.slice(u.tokenPos,u.index)),134283267}function d2(u,e){if(u.startPos=u.tokenPos=u.index,u.startColumn=u.colPos=u.column,u.startLine=u.linePos=u.line,u.index>=u.end)return u.token=1048576;switch(Mu[u.source.charCodeAt(u.index)]){case 8456258:{h(u),u.currentChar===47?(h(u),u.token=25):u.token=8456258;break}case 2162700:{h(u),u.token=2162700;break}default:{let t=0;for(;u.index<u.end;){let o=L[u.source.charCodeAt(u.index)];if(o&1024?(t|=5,c2(u)):o&2048?(p2(u,t),t=t&-5|1):h(u),L[u.currentChar]&16384)break}let i=u.source.slice(u.tokenPos,u.index);e&512&&(u.tokenRaw=i),u.tokenValue=i,u.token=138}}return u.token}function Z2(u){if((u.token&143360)===143360){let{index:e}=u,n=u.currentChar;for(;L[n]&32770;)n=h(u);u.tokenValue+=u.source.slice(e,u.index)}return u.token=208897,u.token}function z(u,e,n){!(u.flags&1)&&(u.token&1048576)!==1048576&&!n&&d(u,28,U[u.token&255]),q(u,e,1074790417)}function ju(u,e,n,t){return e-n<13&&t==="use strict"&&((u.token&1048576)===1048576||u.flags&1)?1:0}function nu(u,e,n){return u.token!==n?0:(D(u,e),1)}function q(u,e,n){return u.token!==n?!1:(D(u,e),!0)}function P(u,e,n){u.token!==n&&d(u,23,U[n&255]),D(u,e)}function r(u,e){switch(e.type){case"ArrayExpression":e.type="ArrayPattern";let n=e.elements;for(let i=0,o=n.length;i<o;++i){let l=n[i];l&&r(u,l)}return;case"ObjectExpression":e.type="ObjectPattern";let t=e.properties;for(let i=0,o=t.length;i<o;++i)r(u,t[i]);return;case"AssignmentExpression":e.type="AssignmentPattern",e.operator!=="="&&d(u,69),delete e.operator,r(u,e.left);return;case"Property":r(u,e.value);return;case"SpreadElement":e.type="RestElement",r(u,e.argument)}}function M2(u,e,n,t,i){e&1024&&((t&36864)===36864&&d(u,115),!i&&(t&537079808)===537079808&&d(u,116)),(t&20480)===20480&&d(u,100),n&24&&t===241739&&d(u,98),e&4196352&&t===209008&&d(u,96),e&2098176&&t===241773&&d(u,95,"yield")}function Xu(u,e,n){e&1024&&((n&36864)===36864&&d(u,115),(n&537079808)===537079808&&d(u,116),n===122&&d(u,93),n===121&&d(u,93)),(n&20480)===20480&&d(u,100),e&4196352&&n===209008&&d(u,96),e&2098176&&n===241773&&d(u,95,"yield")}function zu(u,e,n){return n===209008&&(e&4196352&&d(u,96),u.destructible|=128),n===241773&&e&2097152&&d(u,95,"yield"),(n&20480)===20480||(n&36864)===36864||n==122}function n1(u){return u.property?u.property.type==="PrivateIdentifier":!1}function Hu(u,e,n,t){for(;e;){if(e["$"+n])return t&&d(u,134),1;t&&e.loop&&(t=0),e=e.$}return 0}function i1(u,e,n){let t=e;for(;t;)t["$"+n]&&d(u,133,n),t=t.$;e["$"+n]=1}function y(u,e,n,t,i,o){return e&2&&(o.start=n,o.end=u.startPos,o.range=[n,u.startPos]),e&4&&(o.loc={start:{line:t,column:i},end:{line:u.startLine,column:u.startColumn}},u.sourceFile&&(o.loc.source=u.sourceFile)),o}function J2(u){switch(u.type){case"JSXIdentifier":return u.name;case"JSXNamespacedName":return u.namespace+":"+u.name;case"JSXMemberExpression":return J2(u.object)+"."+J2(u.property)}}function H2(u,e,n){let t=J(s2(),1024);return t2(u,e,t,n,1,0),t}function r2(u,e,...n){let{index:t,line:i,column:o}=u;return{type:e,params:n,index:t,line:i,column:o}}function s2(){return{parent:void 0,type:2}}function J(u,e){return{parent:u,type:e,scopeError:void 0}}function u2(u,e,n,t,i,o){i&4?Ku(u,e,n,t,i):t2(u,e,n,t,i,o),o&64&&l2(u,t)}function t2(u,e,n,t,i,o){let l=n["#"+t];l&&!(l&2)&&(i&1?n.scopeError=r2(u,141,t):e&256&&l&64&&o&2||d(u,141,t)),n.type&128&&n.parent["#"+t]&&!(n.parent["#"+t]&2)&&d(u,141,t),n.type&1024&&l&&!(l&2)&&i&1&&(n.scopeError=r2(u,141,t)),n.type&64&&n.parent["#"+t]&768&&d(u,154,t),n["#"+t]=i}function Ku(u,e,n,t,i){let o=n;for(;o&&!(o.type&256);){let l=o["#"+t];l&248&&(e&256&&!(e&1024)&&(i&128&&l&68||l&128&&i&68)||d(u,141,t)),o===n&&l&1&&i&1&&(o.scopeError=r2(u,141,t)),l&768&&(!(l&512)||!(e&256)||e&1024)&&d(u,141,t),o["#"+t]=i,o=o.parent}}function l2(u,e){u.exportedNames!==void 0&&e!==""&&(u.exportedNames["#"+e]&&d(u,142,e),u.exportedNames["#"+e]=1)}function t1(u,e){u.exportedBindings!==void 0&&e!==""&&(u.exportedBindings["#"+e]=1)}function o1(u,e){return function(n,t,i,o,l){let f={type:n,value:t};u&2&&(f.start=i,f.end=o,f.range=[i,o]),u&4&&(f.loc=l),e.push(f)}}function l1(u,e){return function(n,t,i,o){let l={token:n};u&2&&(l.start=t,l.end=i,l.range=[t,i]),u&4&&(l.loc=o),e.push(l)}}function iu(u,e){return u&2098176?u&2048&&e===209008||u&2097152&&e===241773?!1:(e&143360)===143360||(e&12288)===12288:(e&143360)===143360||(e&12288)===12288||(e&36864)===36864}function tu(u,e,n,t){(n&537079808)===537079808&&(e&1024&&d(u,116),t&&(u.flags|=512)),iu(e,n)||d(u,0)}function f1(u,e,n,t){return{source:u,flags:0,index:0,line:1,column:0,startPos:0,end:u.length,tokenPos:0,startColumn:0,colPos:0,linePos:1,startLine:1,sourceFile:e,tokenValue:"",token:1048576,tokenRaw:"",tokenRegExp:void 0,currentChar:u.charCodeAt(0),exportedNames:[],exportedBindings:[],assignable:1,destructible:0,onComment:n,onToken:t,leadingDecorators:[]}}function c1(u,e,n){let t="",i,o;e!=null&&(e.module&&(n|=3072),e.next&&(n|=1),e.loc&&(n|=4),e.ranges&&(n|=2),e.uniqueKeyInPattern&&(n|=-2147483648),e.lexical&&(n|=64),e.webcompat&&(n|=256),e.directives&&(n|=520),e.globalReturn&&(n|=32),e.raw&&(n|=512),e.preserveParens&&(n|=128),e.impliedStrict&&(n|=1024),e.jsx&&(n|=16),e.identifierPattern&&(n|=268435456),e.specDeviation&&(n|=536870912),e.source&&(t=e.source),e.onComment!=null&&(i=Array.isArray(e.onComment)?o1(n,e.onComment):e.onComment),e.onToken!=null&&(o=Array.isArray(e.onToken)?l1(n,e.onToken):e.onToken));let l=f1(u,t,i,o);n&1&&$e(l);let f=n&64?s2():void 0,c=[],a="script";if(n&2048){if(a="module",c=s1(l,n|8192,f),f)for(let m in l.exportedBindings)m[0]==="#"&&!f[m]&&d(l,143,m.slice(1))}else c=d1(l,n|8192,f);let g={type:"Program",sourceType:a,body:c};return n&2&&(g.start=0,g.end=u.length,g.range=[0,u.length]),n&4&&(g.loc={start:{line:1,column:0},end:{line:l.line,column:l.column}},l.sourceFile&&(g.loc.source=t)),g}function d1(u,e,n){D(u,e|32768|1073741824);let t=[];for(;u.token===134283267;){let{index:i,tokenPos:o,tokenValue:l,linePos:f,colPos:c,token:a}=u,g=X(u,e);ju(u,i,o,l)&&(e|=1024),t.push(lu(u,e,g,a,o,f,c))}for(;u.token!==1048576;)t.push(S2(u,e,n,4,{}));return t}function s1(u,e,n){D(u,e|32768);let t=[];if(e&8)for(;u.token===134283267;){let{tokenPos:i,linePos:o,colPos:l,token:f}=u;t.push(lu(u,e,X(u,e),f,i,o,l))}for(;u.token!==1048576;)t.push(a1(u,e,n));return t}function a1(u,e,n){u.leadingDecorators=W2(u,e);let t;switch(u.token){case 20566:t=I1(u,e,n);break;case 86108:t=q1(u,e,n);break;default:t=S2(u,e,n,4,{})}return u.leadingDecorators.length&&d(u,165),t}function S2(u,e,n,t,i){let o=u.tokenPos,l=u.linePos,f=u.colPos;switch(u.token){case 86106:return i2(u,e,n,t,1,0,0,o,l,f);case 133:case 86096:return x2(u,e,n,0,o,l,f);case 86092:return G2(u,e,n,16,0,o,l,f);case 241739:return T1(u,e,n,t,o,l,f);case 20566:d(u,101,"export");case 86108:switch(D(u,e),u.token){case 67174411:return Qu(u,e,o,l,f);case 67108877:return Yu(u,e,o,l,f);default:d(u,101,"import")}case 209007:return $u(u,e,n,t,i,1,o,l,f);default:return B2(u,e,n,t,i,1,o,l,f)}}function B2(u,e,n,t,i,o,l,f,c){switch(u.token){case 86090:return Wu(u,e,n,0,l,f,c);case 20574:return g1(u,e,l,f,c);case 20571:return h1(u,e,n,i,l,f,c);case 20569:return F1(u,e,n,i,l,f,c);case 20564:return v1(u,e,n,i,l,f,c);case 20580:return D1(u,e,n,i,l,f,c);case 86112:return A1(u,e,n,i,l,f,c);case 1074790417:return y1(u,e,l,f,c);case 2162700:return w2(u,e,n&&J(n,2),i,l,f,c);case 86114:return k1(u,e,l,f,c);case 20557:return C1(u,e,i,l,f,c);case 20561:return b1(u,e,i,l,f,c);case 20579:return w1(u,e,n,i,l,f,c);case 20581:return P1(u,e,n,i,l,f,c);case 20562:return E1(u,e,l,f,c);case 209007:return $u(u,e,n,t,i,0,l,f,c);case 20559:d(u,157);case 20568:d(u,158);case 86106:d(u,e&1024?74:e&256?75:76);case 86096:d(u,77);default:return m1(u,e,n,t,i,o,l,f,c)}}function m1(u,e,n,t,i,o,l,f,c){let{tokenValue:a,token:g}=u,m;switch(g){case 241739:m=I(u,e,0),e&1024&&d(u,83),u.token===69271571&&d(u,82);break;default:m=K(u,e,2,0,1,0,0,1,u.tokenPos,u.linePos,u.colPos)}return g&143360&&u.token===21?ou(u,e,n,t,i,a,m,g,o,l,f,c):(m=N(u,e,m,0,0,l,f,c),m=O(u,e,0,0,l,f,c,m),u.token===18&&(m=e2(u,e,0,l,f,c,m)),D2(u,e,m,l,f,c))}function w2(u,e,n,t,i,o,l){let f=[];for(P(u,e|32768,2162700);u.token!==1074790415;)f.push(S2(u,e,n,2,{$:t}));return P(u,e|32768,1074790415),y(u,e,i,o,l,{type:"BlockStatement",body:f})}function g1(u,e,n,t,i){!(e&32)&&e&8192&&d(u,90),D(u,e|32768);let o=u.flags&1||u.token&1048576?null:j(u,e,0,1,u.tokenPos,u.linePos,u.colPos);return z(u,e|32768),y(u,e,n,t,i,{type:"ReturnStatement",argument:o})}function D2(u,e,n,t,i,o){return z(u,e|32768),y(u,e,t,i,o,{type:"ExpressionStatement",expression:n})}function ou(u,e,n,t,i,o,l,f,c,a,g,m){M2(u,e,0,f,1),i1(u,i,o),D(u,e|32768);let s=c&&!(e&1024)&&e&256&&u.token===86106?i2(u,e,J(n,2),t,0,0,0,u.tokenPos,u.linePos,u.colPos):B2(u,e,n,t,i,c,u.tokenPos,u.linePos,u.colPos);return y(u,e,a,g,m,{type:"LabeledStatement",label:l,body:s})}function $u(u,e,n,t,i,o,l,f,c){let{token:a,tokenValue:g}=u,m=I(u,e,0);if(u.token===21)return ou(u,e,n,t,i,g,m,a,1,l,f,c);let s=u.flags&1;if(!s){if(u.token===86106)return o||d(u,120),i2(u,e,n,t,1,0,1,l,f,c);if((u.token&143360)===143360)return m=ne(u,e,1,l,f,c),u.token===18&&(m=e2(u,e,0,l,f,c,m)),D2(u,e,m,l,f,c)}return u.token===67174411?m=au(u,e,m,1,1,0,s,l,f,c):(u.token===10&&(tu(u,e,a,1),m=$2(u,e,u.tokenValue,m,0,1,0,l,f,c)),u.assignable=1),m=N(u,e,m,0,0,l,f,c),u.token===18&&(m=e2(u,e,0,l,f,c,m)),m=O(u,e,0,0,l,f,c,m),u.assignable=1,D2(u,e,m,l,f,c)}function lu(u,e,n,t,i,o,l){return t!==1074790417&&(u.assignable=2,n=N(u,e,n,0,0,i,o,l),u.token!==1074790417&&(n=O(u,e,0,0,i,o,l,n),u.token===18&&(n=e2(u,e,0,i,o,l,n))),z(u,e|32768)),e&8&&n.type==="Literal"&&typeof n.value=="string"?y(u,e,i,o,l,{type:"ExpressionStatement",expression:n,directive:n.raw.slice(1,-1)}):y(u,e,i,o,l,{type:"ExpressionStatement",expression:n})}function y1(u,e,n,t,i){return D(u,e|32768),y(u,e,n,t,i,{type:"EmptyStatement"})}function k1(u,e,n,t,i){D(u,e|32768),u.flags&1&&d(u,88);let o=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos);return z(u,e|32768),y(u,e,n,t,i,{type:"ThrowStatement",argument:o})}function h1(u,e,n,t,i,o,l){D(u,e),P(u,e|32768,67174411),u.assignable=1;let f=j(u,e,0,1,u.tokenPos,u.line,u.colPos);P(u,e|32768,16);let c=Bu(u,e,n,t,u.tokenPos,u.linePos,u.colPos),a=null;return u.token===20565&&(D(u,e|32768),a=Bu(u,e,n,t,u.tokenPos,u.linePos,u.colPos)),y(u,e,i,o,l,{type:"IfStatement",test:f,consequent:c,alternate:a})}function Bu(u,e,n,t,i,o,l){return e&1024||!(e&256)||u.token!==86106?B2(u,e,n,0,{$:t},0,u.tokenPos,u.linePos,u.colPos):i2(u,e,J(n,2),0,0,0,0,i,o,l)}function A1(u,e,n,t,i,o,l){D(u,e),P(u,e|32768,67174411);let f=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos);P(u,e,16),P(u,e,2162700);let c=[],a=0;for(n&&(n=J(n,8));u.token!==1074790415;){let{tokenPos:g,linePos:m,colPos:s}=u,k=null,C=[];for(q(u,e|32768,20558)?k=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos):(P(u,e|32768,20563),a&&d(u,87),a=1),P(u,e|32768,21);u.token!==20558&&u.token!==1074790415&&u.token!==20563;)C.push(S2(u,e|4096,n,2,{$:t}));c.push(y(u,e,g,m,s,{type:"SwitchCase",test:k,consequent:C}))}return P(u,e|32768,1074790415),y(u,e,i,o,l,{type:"SwitchStatement",discriminant:f,cases:c})}function D1(u,e,n,t,i,o,l){D(u,e),P(u,e|32768,67174411);let f=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos);P(u,e|32768,16);let c=P2(u,e,n,t);return y(u,e,i,o,l,{type:"WhileStatement",test:f,body:c})}function P2(u,e,n,t){return B2(u,(e|134217728)^134217728|131072,n,0,{loop:1,$:t},0,u.tokenPos,u.linePos,u.colPos)}function b1(u,e,n,t,i,o){e&131072||d(u,66),D(u,e);let l=null;if(!(u.flags&1)&&u.token&143360){let{tokenValue:f}=u;l=I(u,e|32768,0),Hu(u,n,f,1)||d(u,135,f)}return z(u,e|32768),y(u,e,t,i,o,{type:"ContinueStatement",label:l})}function C1(u,e,n,t,i,o){D(u,e|32768);let l=null;if(!(u.flags&1)&&u.token&143360){let{tokenValue:f}=u;l=I(u,e|32768,0),Hu(u,n,f,0)||d(u,135,f)}else e&135168||d(u,67);return z(u,e|32768),y(u,e,t,i,o,{type:"BreakStatement",label:l})}function P1(u,e,n,t,i,o,l){D(u,e),e&1024&&d(u,89),P(u,e|32768,67174411);let f=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos);P(u,e|32768,16);let c=B2(u,e,n,2,t,0,u.tokenPos,u.linePos,u.colPos);return y(u,e,i,o,l,{type:"WithStatement",object:f,body:c})}function E1(u,e,n,t,i){return D(u,e|32768),z(u,e|32768),y(u,e,n,t,i,{type:"DebuggerStatement"})}function w1(u,e,n,t,i,o,l){D(u,e|32768);let f=n?J(n,32):void 0,c=w2(u,e,f,{$:t},u.tokenPos,u.linePos,u.colPos),{tokenPos:a,linePos:g,colPos:m}=u,s=q(u,e|32768,20559)?S1(u,e,n,t,a,g,m):null,k=null;if(u.token===20568){D(u,e|32768);let C=f?J(n,4):void 0;k=w2(u,e,C,{$:t},u.tokenPos,u.linePos,u.colPos)}return!s&&!k&&d(u,86),y(u,e,i,o,l,{type:"TryStatement",block:c,handler:s,finalizer:k})}function S1(u,e,n,t,i,o,l){let f=null,c=n;q(u,e,67174411)&&(n&&(n=J(n,4)),f=oe(u,e,n,(u.token&2097152)===2097152?256:512,0,u.tokenPos,u.linePos,u.colPos),u.token===18?d(u,84):u.token===1077936157&&d(u,85),P(u,e|32768,16),n&&(c=J(n,64)));let a=w2(u,e,c,{$:t},u.tokenPos,u.linePos,u.colPos);return y(u,e,i,o,l,{type:"CatchClause",param:f,body:a})}function B1(u,e,n,t,i,o){n&&(n=J(n,2));let l=540672;e=(e|l)^l|262144;let{body:f}=w2(u,e,n,{},t,i,o);return y(u,e,t,i,o,{type:"StaticBlock",body:f})}function v1(u,e,n,t,i,o,l){D(u,e|32768);let f=P2(u,e,n,t);P(u,e,20580),P(u,e|32768,67174411);let c=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos);return P(u,e|32768,16),q(u,e|32768,1074790417),y(u,e,i,o,l,{type:"DoWhileStatement",body:f,test:c})}function T1(u,e,n,t,i,o,l){let{token:f,tokenValue:c}=u,a=I(u,e,0);if(u.token&2240512){let g=y2(u,e,n,8,0);return z(u,e|32768),y(u,e,i,o,l,{type:"VariableDeclaration",kind:"let",declarations:g})}if(u.assignable=1,e&1024&&d(u,83),u.token===21)return ou(u,e,n,t,{},c,a,f,0,i,o,l);if(u.token===10){let g;e&64&&(g=H2(u,e,c)),u.flags=(u.flags|128)^128,a=v2(u,e,g,[a],0,i,o,l)}else a=N(u,e,a,0,0,i,o,l),a=O(u,e,0,0,i,o,l,a);return u.token===18&&(a=e2(u,e,0,i,o,l,a)),D2(u,e,a,i,o,l)}function G2(u,e,n,t,i,o,l,f){D(u,e);let c=y2(u,e,n,t,i);return z(u,e|32768),y(u,e,o,l,f,{type:"VariableDeclaration",kind:t&8?"let":"const",declarations:c})}function Wu(u,e,n,t,i,o,l){D(u,e);let f=y2(u,e,n,4,t);return z(u,e|32768),y(u,e,i,o,l,{type:"VariableDeclaration",kind:"var",declarations:f})}function y2(u,e,n,t,i){let o=1,l=[vu(u,e,n,t,i)];for(;q(u,e,18);)o++,l.push(vu(u,e,n,t,i));return o>1&&i&32&&u.token&262144&&d(u,59,U[u.token&255]),l}function vu(u,e,n,t,i){let{token:o,tokenPos:l,linePos:f,colPos:c}=u,a=null,g=oe(u,e,n,t,i,l,f,c);return u.token===1077936157?(D(u,e|32768),a=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos),(i&32||!(o&2097152))&&(u.token===274549||u.token===8738868&&(o&2097152||!(t&4)||e&1024))&&h2(l,u.line,u.index-3,58,u.token===274549?"of":"in")):(t&16||(o&2097152)>0)&&(u.token&262144)!==262144&&d(u,57,t&16?"const":"destructuring"),y(u,e,l,f,c,{type:"VariableDeclarator",id:g,init:a})}function F1(u,e,n,t,i,o,l){D(u,e);let f=((e&4194304)>0||(e&2048)>0&&(e&8192)>0)&&q(u,e,209008);P(u,e|32768,67174411),n&&(n=J(n,1));let c=null,a=null,g=0,m=null,s=u.token===86090||u.token===241739||u.token===86092,k,{token:C,tokenPos:b,linePos:E,colPos:w}=u;if(s?C===241739?(m=I(u,e,0),u.token&2240512?(u.token===8738868?e&1024&&d(u,65):m=y(u,e,b,E,w,{type:"VariableDeclaration",kind:"let",declarations:y2(u,e|134217728,n,8,32)}),u.assignable=1):e&1024?d(u,65):(s=!1,u.assignable=1,m=N(u,e,m,0,0,b,E,w),u.token===274549&&d(u,112))):(D(u,e),m=y(u,e,b,E,w,C===86090?{type:"VariableDeclaration",kind:"var",declarations:y2(u,e|134217728,n,4,32)}:{type:"VariableDeclaration",kind:"const",declarations:y2(u,e|134217728,n,16,32)}),u.assignable=1):C===1074790417?f&&d(u,80):(C&2097152)===2097152?(m=C===2162700?Y(u,e,void 0,1,0,0,2,32,b,E,w):_(u,e,void 0,1,0,0,2,32,b,E,w),g=u.destructible,e&256&&g&64&&d(u,61),u.assignable=g&16?2:1,m=N(u,e|134217728,m,0,0,u.tokenPos,u.linePos,u.colPos)):m=W(u,e|134217728,1,0,1,b,E,w),(u.token&262144)===262144){if(u.token===274549){u.assignable&2&&d(u,78,f?"await":"of"),r(u,m),D(u,e|32768),k=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos),P(u,e|32768,16);let S=P2(u,e,n,t);return y(u,e,i,o,l,{type:"ForOfStatement",left:m,right:k,body:S,await:f})}u.assignable&2&&d(u,78,"in"),r(u,m),D(u,e|32768),f&&d(u,80),k=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos),P(u,e|32768,16);let M=P2(u,e,n,t);return y(u,e,i,o,l,{type:"ForInStatement",body:M,left:m,right:k})}f&&d(u,80),s||(g&8&&u.token!==1077936157&&d(u,78,"loop"),m=O(u,e|134217728,0,0,b,E,w,m)),u.token===18&&(m=e2(u,e,0,u.tokenPos,u.linePos,u.colPos,m)),P(u,e|32768,1074790417),u.token!==1074790417&&(c=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos)),P(u,e|32768,1074790417),u.token!==16&&(a=j(u,e,0,1,u.tokenPos,u.linePos,u.colPos)),P(u,e|32768,16);let v=P2(u,e,n,t);return y(u,e,i,o,l,{type:"ForStatement",init:m,test:c,update:a,body:v})}function _u(u,e,n){return iu(e,u.token)||d(u,115),(u.token&537079808)===537079808&&d(u,116),n&&t2(u,e,n,u.tokenValue,8,0),I(u,e,0)}function q1(u,e,n){let t=u.tokenPos,i=u.linePos,o=u.colPos;D(u,e);let l=null,{tokenPos:f,linePos:c,colPos:a}=u,g=[];if(u.token===134283267)l=X(u,e);else{if(u.token&143360){let m=_u(u,e,n);if(g=[y(u,e,f,c,a,{type:"ImportDefaultSpecifier",local:m})],q(u,e,18))switch(u.token){case 8457014:g.push(Tu(u,e,n));break;case 2162700:Fu(u,e,n,g);break;default:d(u,105)}}else switch(u.token){case 8457014:g=[Tu(u,e,n)];break;case 2162700:Fu(u,e,n,g);break;case 67174411:return Qu(u,e,t,i,o);case 67108877:return Yu(u,e,t,i,o);default:d(u,28,U[u.token&255])}l=L1(u,e)}return z(u,e|32768),y(u,e,t,i,o,{type:"ImportDeclaration",specifiers:g,source:l})}function Tu(u,e,n){let{tokenPos:t,linePos:i,colPos:o}=u;return D(u,e),P(u,e,77934),(u.token&134217728)===134217728&&h2(t,u.line,u.index,28,U[u.token&255]),y(u,e,t,i,o,{type:"ImportNamespaceSpecifier",local:_u(u,e,n)})}function L1(u,e){return q(u,e,12404),u.token!==134283267&&d(u,103,"Import"),X(u,e)}function Fu(u,e,n,t){for(D(u,e);u.token&143360;){let{token:i,tokenValue:o,tokenPos:l,linePos:f,colPos:c}=u,a=I(u,e,0),g;q(u,e,77934)?((u.token&134217728)===134217728||u.token===18?d(u,104):M2(u,e,16,u.token,0),o=u.tokenValue,g=I(u,e,0)):(M2(u,e,16,i,0),g=a),n&&t2(u,e,n,o,8,0),t.push(y(u,e,l,f,c,{type:"ImportSpecifier",local:g,imported:a})),u.token!==1074790415&&P(u,e,18)}return P(u,e,1074790415),t}function Yu(u,e,n,t,i){let o=ru(u,e,y(u,e,n,t,i,{type:"Identifier",name:"import"}),n,t,i);return o=N(u,e,o,0,0,n,t,i),o=O(u,e,0,0,n,t,i,o),D2(u,e,o,n,t,i)}function Qu(u,e,n,t,i){let o=Gu(u,e,0,n,t,i);return o=N(u,e,o,0,0,n,t,i),u.token===18&&(o=e2(u,e,0,n,t,i,o)),D2(u,e,o,n,t,i)}function I1(u,e,n){let t=u.tokenPos,i=u.linePos,o=u.colPos;D(u,e|32768);let l=[],f=null,c=null,a;if(q(u,e|32768,20563)){switch(u.token){case 86106:{f=i2(u,e,n,4,1,1,0,u.tokenPos,u.linePos,u.colPos);break}case 133:case 86096:f=x2(u,e,n,1,u.tokenPos,u.linePos,u.colPos);break;case 209007:let{tokenPos:g,linePos:m,colPos:s}=u;f=I(u,e,0);let{flags:k}=u;k&1||(u.token===86106?f=i2(u,e,n,4,1,1,1,g,m,s):u.token===67174411?(f=au(u,e,f,1,1,0,k,g,m,s),f=N(u,e,f,0,0,g,m,s),f=O(u,e,0,0,g,m,s,f)):u.token&143360&&(n&&(n=H2(u,e,u.tokenValue)),f=I(u,e,0),f=v2(u,e,n,[f],1,g,m,s)));break;default:f=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos),z(u,e|32768)}return n&&l2(u,"default"),y(u,e,t,i,o,{type:"ExportDefaultDeclaration",declaration:f})}switch(u.token){case 8457014:{D(u,e);let k=null;return q(u,e,77934)&&(n&&l2(u,u.tokenValue),k=I(u,e,0)),P(u,e,12404),u.token!==134283267&&d(u,103,"Export"),c=X(u,e),z(u,e|32768),y(u,e,t,i,o,{type:"ExportAllDeclaration",source:c,exported:k})}case 2162700:{D(u,e);let k=[],C=[];for(;u.token&143360;){let{tokenPos:b,tokenValue:E,linePos:w,colPos:v}=u,M=I(u,e,0),S;u.token===77934?(D(u,e),(u.token&134217728)===134217728&&d(u,104),n&&(k.push(u.tokenValue),C.push(E)),S=I(u,e,0)):(n&&(k.push(u.tokenValue),C.push(u.tokenValue)),S=M),l.push(y(u,e,b,w,v,{type:"ExportSpecifier",local:M,exported:S})),u.token!==1074790415&&P(u,e,18)}if(P(u,e,1074790415),q(u,e,12404))u.token!==134283267&&d(u,103,"Export"),c=X(u,e);else if(n){let b=0,E=k.length;for(;b<E;b++)l2(u,k[b]);for(b=0,E=C.length;b<E;b++)t1(u,C[b])}z(u,e|32768);break}case 86096:f=x2(u,e,n,2,u.tokenPos,u.linePos,u.colPos);break;case 86106:f=i2(u,e,n,4,1,2,0,u.tokenPos,u.linePos,u.colPos);break;case 241739:f=G2(u,e,n,8,64,u.tokenPos,u.linePos,u.colPos);break;case 86092:f=G2(u,e,n,16,64,u.tokenPos,u.linePos,u.colPos);break;case 86090:f=Wu(u,e,n,64,u.tokenPos,u.linePos,u.colPos);break;case 209007:let{tokenPos:g,linePos:m,colPos:s}=u;if(D(u,e),!(u.flags&1)&&u.token===86106){f=i2(u,e,n,4,1,2,1,g,m,s),n&&(a=f.id?f.id.name:"",l2(u,a));break}default:d(u,28,U[u.token&255])}return y(u,e,t,i,o,{type:"ExportNamedDeclaration",declaration:f,specifiers:l,source:c})}function R(u,e,n,t,i,o,l,f){let c=K(u,e,2,0,n,t,i,1,o,l,f);return c=N(u,e,c,i,0,o,l,f),O(u,e,i,0,o,l,f,c)}function e2(u,e,n,t,i,o,l){let f=[l];for(;q(u,e|32768,18);)f.push(R(u,e,1,0,n,u.tokenPos,u.linePos,u.colPos));return y(u,e,t,i,o,{type:"SequenceExpression",expressions:f})}function j(u,e,n,t,i,o,l){let f=R(u,e,t,0,n,i,o,l);return u.token===18?e2(u,e,n,i,o,l,f):f}function O(u,e,n,t,i,o,l,f){let{token:c}=u;if((c&4194304)===4194304){u.assignable&2&&d(u,24),(!t&&c===1077936157&&f.type==="ArrayExpression"||f.type==="ObjectExpression")&&r(u,f),D(u,e|32768);let a=R(u,e,1,1,n,u.tokenPos,u.linePos,u.colPos);return u.assignable=2,y(u,e,i,o,l,t?{type:"AssignmentPattern",left:f,right:a}:{type:"AssignmentExpression",left:f,operator:U[c&255],right:a})}return(c&8454144)===8454144&&(f=n2(u,e,n,i,o,l,4,c,f)),q(u,e|32768,22)&&(f=f2(u,e,f,i,o,l)),f}function N2(u,e,n,t,i,o,l,f){let{token:c}=u;D(u,e|32768);let a=R(u,e,1,1,n,u.tokenPos,u.linePos,u.colPos);return f=y(u,e,i,o,l,t?{type:"AssignmentPattern",left:f,right:a}:{type:"AssignmentExpression",left:f,operator:U[c&255],right:a}),u.assignable=2,f}function f2(u,e,n,t,i,o){let l=R(u,(e|134217728)^134217728,1,0,0,u.tokenPos,u.linePos,u.colPos);P(u,e|32768,21),u.assignable=1;let f=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos);return u.assignable=2,y(u,e,t,i,o,{type:"ConditionalExpression",test:n,consequent:l,alternate:f})}function n2(u,e,n,t,i,o,l,f,c){let a=-((e&134217728)>0)&8738868,g,m;for(u.assignable=2;u.token&8454144&&(g=u.token,m=g&3840,(g&524288&&f&268435456||f&524288&&g&268435456)&&d(u,160),!(m+((g===8457273)<<8)-((a===g)<<12)<=l));)D(u,e|32768),c=y(u,e,t,i,o,{type:g&524288||g&268435456?"LogicalExpression":"BinaryExpression",left:c,right:n2(u,e,n,u.tokenPos,u.linePos,u.colPos,m,g,W(u,e,0,n,1,u.tokenPos,u.linePos,u.colPos)),operator:U[g&255]});return u.token===1077936157&&d(u,24),c}function N1(u,e,n,t,i,o,l){n||d(u,0);let f=u.token;D(u,e|32768);let c=W(u,e,0,l,1,u.tokenPos,u.linePos,u.colPos);return u.token===8457273&&d(u,31),e&1024&&f===16863278&&(c.type==="Identifier"?d(u,118):n1(c)&&d(u,124)),u.assignable=2,y(u,e,t,i,o,{type:"UnaryExpression",operator:U[f&255],argument:c,prefix:!0})}function V1(u,e,n,t,i,o,l,f,c,a){let{token:g}=u,m=I(u,e,o),{flags:s}=u;if(!(s&1)){if(u.token===86106)return pu(u,e,1,n,f,c,a);if((u.token&143360)===143360)return t||d(u,0),ne(u,e,i,f,c,a)}return!l&&u.token===67174411?au(u,e,m,i,1,0,s,f,c,a):u.token===10?(tu(u,e,g,1),l&&d(u,49),$2(u,e,u.tokenValue,m,l,i,0,f,c,a)):m}function R1(u,e,n,t,i,o,l){if(n&&(u.destructible|=256),e&2097152){D(u,e|32768),e&8388608&&d(u,30),t||d(u,24),u.token===22&&d(u,121);let f=null,c=!1;return u.flags&1||(c=q(u,e|32768,8457014),(u.token&77824||c)&&(f=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos))),u.assignable=2,y(u,e,i,o,l,{type:"YieldExpression",argument:f,delegate:c})}return e&1024&&d(u,95,"yield"),su(u,e,i,o,l)}function O1(u,e,n,t,i,o,l){if(t&&(u.destructible|=128),e&4194304||e&2048&&e&8192){n&&d(u,0),e&8388608&&h2(u.index,u.line,u.index,29),D(u,e|32768);let f=W(u,e,0,0,1,u.tokenPos,u.linePos,u.colPos);return u.token===8457273&&d(u,31),u.assignable=2,y(u,e,i,o,l,{type:"AwaitExpression",argument:f})}return e&2048&&d(u,96),su(u,e,i,o,l)}function K2(u,e,n,t,i,o){let{tokenPos:l,linePos:f,colPos:c}=u;P(u,e|32768,2162700);let a=[],g=e;if(u.token!==1074790415){for(;u.token===134283267;){let{index:m,tokenPos:s,tokenValue:k,token:C}=u,b=X(u,e);ju(u,m,s,k)&&(e|=1024,u.flags&128&&h2(u.index,u.line,u.tokenPos,64),u.flags&64&&h2(u.index,u.line,u.tokenPos,8)),a.push(lu(u,e,b,C,s,u.linePos,u.colPos))}e&1024&&(i&&((i&537079808)===537079808&&d(u,116),(i&36864)===36864&&d(u,38)),u.flags&512&&d(u,116),u.flags&256&&d(u,115)),e&64&&n&&o!==void 0&&!(g&1024)&&!(e&8192)&&z2(o)}for(u.flags=(u.flags|512|256|64)^832,u.destructible=(u.destructible|256)^256;u.token!==1074790415;)a.push(S2(u,e,n,4,{}));return P(u,t&24?e|32768:e,1074790415),u.flags&=-193,u.token===1077936157&&d(u,24),y(u,e,l,f,c,{type:"BlockStatement",body:a})}function U1(u,e,n,t,i){switch(D(u,e),u.token){case 67108991:d(u,162);case 67174411:{e&524288||d(u,26),e&16384&&d(u,27),u.assignable=2;break}case 69271571:case 67108877:{e&262144||d(u,27),e&16384&&d(u,27),u.assignable=1;break}default:d(u,28,"super")}return y(u,e,n,t,i,{type:"Super"})}function W(u,e,n,t,i,o,l,f){let c=K(u,e,2,0,n,0,t,i,o,l,f);return N(u,e,c,t,0,o,l,f)}function M1(u,e,n,t,i,o){u.assignable&2&&d(u,53);let{token:l}=u;return D(u,e),u.assignable=2,y(u,e,t,i,o,{type:"UpdateExpression",argument:n,operator:U[l&255],prefix:!1})}function N(u,e,n,t,i,o,l,f){if((u.token&33619968)===33619968&&!(u.flags&1))n=M1(u,e,n,o,l,f);else if((u.token&67108864)===67108864){switch(e=(e|134217728)^134217728,u.token){case 67108877:{D(u,(e|1073741824|8192)^8192),u.assignable=1;let c=Zu(u,e);n=y(u,e,o,l,f,{type:"MemberExpression",object:n,computed:!1,property:c});break}case 69271571:{let c=!1;(u.flags&2048)===2048&&(c=!0,u.flags=(u.flags|2048)^2048),D(u,e|32768);let{tokenPos:a,linePos:g,colPos:m}=u,s=j(u,e,t,1,a,g,m);P(u,e,20),u.assignable=1,n=y(u,e,o,l,f,{type:"MemberExpression",object:n,computed:!0,property:s}),c&&(u.flags|=2048);break}case 67174411:{if((u.flags&1024)===1024)return u.flags=(u.flags|1024)^1024,n;let c=!1;(u.flags&2048)===2048&&(c=!0,u.flags=(u.flags|2048)^2048);let a=du(u,e,t);u.assignable=2,n=y(u,e,o,l,f,{type:"CallExpression",callee:n,arguments:a}),c&&(u.flags|=2048);break}case 67108991:{D(u,(e|1073741824|8192)^8192),u.flags|=2048,u.assignable=2,n=J1(u,e,n,o,l,f);break}default:(u.flags&2048)===2048&&d(u,161),u.assignable=2,n=y(u,e,o,l,f,{type:"TaggedTemplateExpression",tag:n,quasi:u.token===67174408?cu(u,e|65536):fu(u,e,u.tokenPos,u.linePos,u.colPos)})}n=N(u,e,n,0,1,o,l,f)}return i===0&&(u.flags&2048)===2048&&(u.flags=(u.flags|2048)^2048,n=y(u,e,o,l,f,{type:"ChainExpression",expression:n})),n}function J1(u,e,n,t,i,o){let l=!1,f;if((u.token===69271571||u.token===67174411)&&(u.flags&2048)===2048&&(l=!0,u.flags=(u.flags|2048)^2048),u.token===69271571){D(u,e|32768);let{tokenPos:c,linePos:a,colPos:g}=u,m=j(u,e,0,1,c,a,g);P(u,e,20),u.assignable=2,f=y(u,e,t,i,o,{type:"MemberExpression",object:n,computed:!0,optional:!0,property:m})}else if(u.token===67174411){let c=du(u,e,0);u.assignable=2,f=y(u,e,t,i,o,{type:"CallExpression",callee:n,arguments:c,optional:!0})}else{u.token&143360||d(u,155);let c=I(u,e,0);u.assignable=2,f=y(u,e,t,i,o,{type:"MemberExpression",object:n,computed:!1,optional:!0,property:c})}return l&&(u.flags|=2048),f}function Zu(u,e){return!(u.token&143360)&&u.token!==131&&d(u,155),e&1&&u.token===131?X2(u,e,u.tokenPos,u.linePos,u.colPos):I(u,e,0)}function j1(u,e,n,t,i,o,l){n&&d(u,54),t||d(u,0);let{token:f}=u;D(u,e|32768);let c=W(u,e,0,0,1,u.tokenPos,u.linePos,u.colPos);return u.assignable&2&&d(u,53),u.assignable=2,y(u,e,i,o,l,{type:"UpdateExpression",argument:c,operator:U[f&255],prefix:!0})}function K(u,e,n,t,i,o,l,f,c,a,g){if((u.token&143360)===143360){switch(u.token){case 209008:return O1(u,e,t,l,c,a,g);case 241773:return R1(u,e,l,i,c,a,g);case 209007:return V1(u,e,l,f,i,o,t,c,a,g)}let{token:m,tokenValue:s}=u,k=I(u,e|65536,o);return u.token===10?(f||d(u,0),tu(u,e,m,1),$2(u,e,s,k,t,i,0,c,a,g)):(e&16384&&m===537079928&&d(u,127),m===241739&&(e&1024&&d(u,110),n&24&&d(u,98)),u.assignable=e&1024&&(m&537079808)===537079808?2:1,k)}if((u.token&134217728)===134217728)return X(u,e);switch(u.token){case 33619995:case 33619996:return j1(u,e,t,f,c,a,g);case 16863278:case 16842800:case 16842801:case 25233970:case 25233971:case 16863277:case 16863279:return N1(u,e,f,c,a,g,l);case 86106:return pu(u,e,0,l,c,a,g);case 2162700:return W1(u,e,i?0:1,l,c,a,g);case 69271571:return $1(u,e,i?0:1,l,c,a,g);case 67174411:return Y1(u,e,i,1,0,c,a,g);case 86021:case 86022:case 86023:return H1(u,e,c,a,g);case 86113:return K1(u,e);case 65540:return r1(u,e,c,a,g);case 133:case 86096:return G1(u,e,l,c,a,g);case 86111:return U1(u,e,c,a,g);case 67174409:return fu(u,e,c,a,g);case 67174408:return cu(u,e);case 86109:return Q1(u,e,l,c,a,g);case 134283389:return xu(u,e,c,a,g);case 131:return X2(u,e,c,a,g);case 86108:return X1(u,e,t,l,c,a,g);case 8456258:if(e&16)return gu(u,e,1,c,a,g);default:if(iu(e,u.token))return su(u,e,c,a,g);d(u,28,U[u.token&255])}}function X1(u,e,n,t,i,o,l){let f=I(u,e,0);return u.token===67108877?ru(u,e,f,i,o,l):(n&&d(u,138),f=Gu(u,e,t,i,o,l),u.assignable=2,N(u,e,f,t,0,i,o,l))}function ru(u,e,n,t,i,o){return e&2048||d(u,164),D(u,e),u.token!==143495&&u.tokenValue!=="meta"&&d(u,28,U[u.token&255]),u.assignable=2,y(u,e,t,i,o,{type:"MetaProperty",meta:n,property:I(u,e,0)})}function Gu(u,e,n,t,i,o){P(u,e|32768,67174411),u.token===14&&d(u,139);let l=R(u,e,1,0,n,u.tokenPos,u.linePos,u.colPos);return P(u,e,16),y(u,e,t,i,o,{type:"ImportExpression",source:l})}function xu(u,e,n,t,i){let{tokenRaw:o,tokenValue:l}=u;return D(u,e),u.assignable=2,y(u,e,n,t,i,e&512?{type:"Literal",value:l,bigint:o.slice(0,-1),raw:o}:{type:"Literal",value:l,bigint:o.slice(0,-1)})}function fu(u,e,n,t,i){u.assignable=2;let{tokenValue:o,tokenRaw:l,tokenPos:f,linePos:c,colPos:a}=u;P(u,e,67174409);let g=[R2(u,e,o,l,f,c,a,!0)];return y(u,e,n,t,i,{type:"TemplateLiteral",expressions:[],quasis:g})}function cu(u,e){e=(e|134217728)^134217728;let{tokenValue:n,tokenRaw:t,tokenPos:i,linePos:o,colPos:l}=u;P(u,e|32768,67174408);let f=[R2(u,e,n,t,i,o,l,!1)],c=[j(u,e,0,1,u.tokenPos,u.linePos,u.colPos)];for(u.token!==1074790415&&d(u,81);(u.token=re(u,e))!==67174409;){let{tokenValue:a,tokenRaw:g,tokenPos:m,linePos:s,colPos:k}=u;P(u,e|32768,67174408),f.push(R2(u,e,a,g,m,s,k,!1)),c.push(j(u,e,0,1,u.tokenPos,u.linePos,u.colPos)),u.token!==1074790415&&d(u,81)}{let{tokenValue:a,tokenRaw:g,tokenPos:m,linePos:s,colPos:k}=u;P(u,e,67174409),f.push(R2(u,e,a,g,m,s,k,!0))}return y(u,e,i,o,l,{type:"TemplateLiteral",expressions:c,quasis:f})}function R2(u,e,n,t,i,o,l,f){let c=y(u,e,i,o,l,{type:"TemplateElement",value:{cooked:n,raw:t},tail:f}),a=f?1:2;return e&2&&(c.start+=1,c.range[0]+=1,c.end-=a,c.range[1]-=a),e&4&&(c.loc.start.column+=1,c.loc.end.column-=a),c}function z1(u,e,n,t,i){e=(e|134217728)^134217728,P(u,e|32768,14);let o=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos);return u.assignable=1,y(u,e,n,t,i,{type:"SpreadElement",argument:o})}function du(u,e,n){D(u,e|32768);let t=[];if(u.token===16)return D(u,e),t;for(;u.token!==16&&(u.token===14?t.push(z1(u,e,u.tokenPos,u.linePos,u.colPos)):t.push(R(u,e,1,0,n,u.tokenPos,u.linePos,u.colPos)),!(u.token!==18||(D(u,e|32768),u.token===16))););return P(u,e,16),t}function I(u,e,n){let{tokenValue:t,tokenPos:i,linePos:o,colPos:l}=u;return D(u,e),y(u,e,i,o,l,e&268435456?{type:"Identifier",name:t,pattern:n===1}:{type:"Identifier",name:t})}function X(u,e){let{tokenValue:n,tokenRaw:t,tokenPos:i,linePos:o,colPos:l}=u;return u.token===134283389?xu(u,e,i,o,l):(D(u,e),u.assignable=2,y(u,e,i,o,l,e&512?{type:"Literal",value:n,raw:t}:{type:"Literal",value:n}))}function H1(u,e,n,t,i){let o=U[u.token&255],l=u.token===86023?null:o==="true";return D(u,e),u.assignable=2,y(u,e,n,t,i,e&512?{type:"Literal",value:l,raw:o}:{type:"Literal",value:l})}function K1(u,e){let{tokenPos:n,linePos:t,colPos:i}=u;return D(u,e),u.assignable=2,y(u,e,n,t,i,{type:"ThisExpression"})}function i2(u,e,n,t,i,o,l,f,c,a){D(u,e|32768);let g=i?nu(u,e,8457014):0,m=null,s,k=n?s2():void 0;if(u.token===67174411)o&1||d(u,37,"Function");else{let E=t&4&&(!(e&8192)||!(e&2048))?4:64;Xu(u,e|(e&3072)<<11,u.token),n&&(E&4?Ku(u,e,n,u.tokenValue,E):t2(u,e,n,u.tokenValue,E,t),k=J(k,256),o&&o&2&&l2(u,u.tokenValue)),s=u.token,u.token&143360?m=I(u,e,0):d(u,28,U[u.token&255])}e=(e|32243712)^32243712|67108864|l*2+g<<21|(g?0:1073741824),n&&(k=J(k,512));let C=ee(u,e|8388608,k,0,1),b=K2(u,(e|8192|4096|131072)^143360,n?J(k,128):k,8,s,n?k.scopeError:void 0);return y(u,e,f,c,a,{type:"FunctionDeclaration",id:m,params:C,body:b,async:l===1,generator:g===1})}function pu(u,e,n,t,i,o,l){D(u,e|32768);let f=nu(u,e,8457014),c=n*2+f<<21,a=null,g,m=e&64?s2():void 0;(u.token&176128)>0&&(Xu(u,(e|32243712)^32243712|c,u.token),m&&(m=J(m,256)),g=u.token,a=I(u,e,0)),e=(e|32243712)^32243712|67108864|c|(f?0:1073741824),m&&(m=J(m,512));let s=ee(u,e|8388608,m,t,1),k=K2(u,e&-134377473,m&&J(m,128),0,g,void 0);return u.assignable=2,y(u,e,i,o,l,{type:"FunctionExpression",id:a,params:s,body:k,async:n===1,generator:f===1})}function $1(u,e,n,t,i,o,l){let f=_(u,e,void 0,n,t,0,2,0,i,o,l);return e&256&&u.destructible&64&&d(u,61),u.destructible&8&&d(u,60),f}function _(u,e,n,t,i,o,l,f,c,a,g){D(u,e|32768);let m=[],s=0;for(e=(e|134217728)^134217728;u.token!==20;)if(q(u,e|32768,18))m.push(null);else{let C,{token:b,tokenPos:E,linePos:w,colPos:v,tokenValue:M}=u;if(b&143360)if(C=K(u,e,l,0,1,0,i,1,E,w,v),u.token===1077936157){u.assignable&2&&d(u,24),D(u,e|32768),n&&u2(u,e,n,M,l,f);let S=R(u,e,1,1,i,u.tokenPos,u.linePos,u.colPos);C=y(u,e,E,w,v,o?{type:"AssignmentPattern",left:C,right:S}:{type:"AssignmentExpression",operator:"=",left:C,right:S}),s|=u.destructible&256?256:0|u.destructible&128?128:0}else u.token===18||u.token===20?(u.assignable&2?s|=16:n&&u2(u,e,n,M,l,f),s|=u.destructible&256?256:0|u.destructible&128?128:0):(s|=l&1?32:l&2?0:16,C=N(u,e,C,i,0,E,w,v),u.token!==18&&u.token!==20?(u.token!==1077936157&&(s|=16),C=O(u,e,i,o,E,w,v,C)):u.token!==1077936157&&(s|=u.assignable&2?16:32));else b&2097152?(C=u.token===2162700?Y(u,e,n,0,i,o,l,f,E,w,v):_(u,e,n,0,i,o,l,f,E,w,v),s|=u.destructible,u.assignable=u.destructible&16?2:1,u.token===18||u.token===20?u.assignable&2&&(s|=16):u.destructible&8?d(u,69):(C=N(u,e,C,i,0,E,w,v),s=u.assignable&2?16:0,u.token!==18&&u.token!==20?C=O(u,e,i,o,E,w,v,C):u.token!==1077936157&&(s|=u.assignable&2?16:32))):b===14?(C=b2(u,e,n,20,l,f,0,i,o,E,w,v),s|=u.destructible,u.token!==18&&u.token!==20&&d(u,28,U[u.token&255])):(C=W(u,e,1,0,1,E,w,v),u.token!==18&&u.token!==20?(C=O(u,e,i,o,E,w,v,C),!(l&3)&&b===67174411&&(s|=16)):u.assignable&2?s|=16:b===67174411&&(s|=u.assignable&1&&l&3?32:16));if(m.push(C),q(u,e|32768,18)){if(u.token===20)break}else break}P(u,e,20);let k=y(u,e,c,a,g,{type:o?"ArrayPattern":"ArrayExpression",elements:m});return!t&&u.token&4194304?ue(u,e,s,i,o,c,a,g,k):(u.destructible=s,k)}function ue(u,e,n,t,i,o,l,f,c){u.token!==1077936157&&d(u,24),D(u,e|32768),n&16&&d(u,24),i||r(u,c);let{tokenPos:a,linePos:g,colPos:m}=u,s=R(u,e,1,1,t,a,g,m);return u.destructible=(n|64|8)^72|(u.destructible&128?128:0)|(u.destructible&256?256:0),y(u,e,o,l,f,i?{type:"AssignmentPattern",left:c,right:s}:{type:"AssignmentExpression",left:c,operator:"=",right:s})}function b2(u,e,n,t,i,o,l,f,c,a,g,m){D(u,e|32768);let s=null,k=0,{token:C,tokenValue:b,tokenPos:E,linePos:w,colPos:v}=u;if(C&143360)u.assignable=1,s=K(u,e,i,0,1,0,f,1,E,w,v),C=u.token,s=N(u,e,s,f,0,E,w,v),u.token!==18&&u.token!==t&&(u.assignable&2&&u.token===1077936157&&d(u,69),k|=16,s=O(u,e,f,c,E,w,v,s)),u.assignable&2?k|=16:C===t||C===18?n&&u2(u,e,n,b,i,o):k|=32,k|=u.destructible&128?128:0;else if(C===t)d(u,39);else if(C&2097152)s=u.token===2162700?Y(u,e,n,1,f,c,i,o,E,w,v):_(u,e,n,1,f,c,i,o,E,w,v),C=u.token,C!==1077936157&&C!==t&&C!==18?(u.destructible&8&&d(u,69),s=N(u,e,s,f,0,E,w,v),k|=u.assignable&2?16:0,(u.token&4194304)===4194304?(u.token!==1077936157&&(k|=16),s=O(u,e,f,c,E,w,v,s)):((u.token&8454144)===8454144&&(s=n2(u,e,1,E,w,v,4,C,s)),q(u,e|32768,22)&&(s=f2(u,e,s,E,w,v)),k|=u.assignable&2?16:32)):k|=t===1074790415&&C!==1077936157?16:u.destructible;else{k|=32,s=W(u,e,1,f,1,u.tokenPos,u.linePos,u.colPos);let{token:M,tokenPos:S,linePos:V,colPos:A}=u;return M===1077936157&&M!==t&&M!==18?(u.assignable&2&&d(u,24),s=O(u,e,f,c,S,V,A,s),k|=16):(M===18?k|=16:M!==t&&(s=O(u,e,f,c,S,V,A,s)),k|=u.assignable&1?32:16),u.destructible=k,u.token!==t&&u.token!==18&&d(u,156),y(u,e,a,g,m,{type:c?"RestElement":"SpreadElement",argument:s})}if(u.token!==t)if(i&1&&(k|=l?16:32),q(u,e|32768,1077936157)){k&16&&d(u,24),r(u,s);let M=R(u,e,1,1,f,u.tokenPos,u.linePos,u.colPos);s=y(u,e,E,w,v,c?{type:"AssignmentPattern",left:s,right:M}:{type:"AssignmentExpression",left:s,operator:"=",right:M}),k=16}else k|=16;return u.destructible=k,y(u,e,a,g,m,{type:c?"RestElement":"SpreadElement",argument:s})}function Z(u,e,n,t,i,o,l){let f=n&64?14680064:31981568;e=(e|f)^f|(n&88)<<18|100925440;let c=e&64?J(s2(),512):void 0,a=_1(u,e|8388608,c,n,1,t);c&&(c=J(c,128));let g=K2(u,e&-134230017,c,0,void 0,void 0);return y(u,e,i,o,l,{type:"FunctionExpression",params:a,body:g,async:(n&16)>0,generator:(n&8)>0,id:null})}function W1(u,e,n,t,i,o,l){let f=Y(u,e,void 0,n,t,0,2,0,i,o,l);return e&256&&u.destructible&64&&d(u,61),u.destructible&8&&d(u,60),f}function Y(u,e,n,t,i,o,l,f,c,a,g){D(u,e);let m=[],s=0,k=0;for(e=(e|134217728)^134217728;u.token!==1074790415;){let{token:b,tokenValue:E,linePos:w,colPos:v,tokenPos:M}=u;if(b===14)m.push(b2(u,e,n,1074790415,l,f,0,i,o,M,w,v));else{let S=0,V=null,A,Q=u.token;if(u.token&143360||u.token===121)if(V=I(u,e,0),u.token===18||u.token===1074790415||u.token===1077936157)if(S|=4,e&1024&&(b&537079808)===537079808?s|=16:M2(u,e,l,b,0),n&&u2(u,e,n,E,l,f),q(u,e|32768,1077936157)){s|=8;let B=R(u,e,1,1,i,u.tokenPos,u.linePos,u.colPos);s|=u.destructible&256?256:0|u.destructible&128?128:0,A=y(u,e,M,w,v,{type:"AssignmentPattern",left:e&-2147483648?Object.assign({},V):V,right:B})}else s|=(b===209008?128:0)|(b===121?16:0),A=e&-2147483648?Object.assign({},V):V;else if(q(u,e|32768,21)){let{tokenPos:B,linePos:F,colPos:T}=u;if(E==="__proto__"&&k++,u.token&143360){let o2=u.token,m2=u.tokenValue;s|=Q===121?16:0,A=K(u,e,l,0,1,0,i,1,B,F,T);let{token:x}=u;A=N(u,e,A,i,0,B,F,T),u.token===18||u.token===1074790415?x===1077936157||x===1074790415||x===18?(s|=u.destructible&128?128:0,u.assignable&2?s|=16:n&&(o2&143360)===143360&&u2(u,e,n,m2,l,f)):s|=u.assignable&1?32:16:(u.token&4194304)===4194304?(u.assignable&2?s|=16:x!==1077936157?s|=32:n&&u2(u,e,n,m2,l,f),A=O(u,e,i,o,B,F,T,A)):(s|=16,(u.token&8454144)===8454144&&(A=n2(u,e,1,B,F,T,4,x,A)),q(u,e|32768,22)&&(A=f2(u,e,A,B,F,T)))}else(u.token&2097152)===2097152?(A=u.token===69271571?_(u,e,n,0,i,o,l,f,B,F,T):Y(u,e,n,0,i,o,l,f,B,F,T),s=u.destructible,u.assignable=s&16?2:1,u.token===18||u.token===1074790415?u.assignable&2&&(s|=16):u.destructible&8?d(u,69):(A=N(u,e,A,i,0,B,F,T),s=u.assignable&2?16:0,(u.token&4194304)===4194304?A=N2(u,e,i,o,B,F,T,A):((u.token&8454144)===8454144&&(A=n2(u,e,1,B,F,T,4,b,A)),q(u,e|32768,22)&&(A=f2(u,e,A,B,F,T)),s|=u.assignable&2?16:32))):(A=W(u,e,1,i,1,B,F,T),s|=u.assignable&1?32:16,u.token===18||u.token===1074790415?u.assignable&2&&(s|=16):(A=N(u,e,A,i,0,B,F,T),s=u.assignable&2?16:0,u.token!==18&&b!==1074790415&&(u.token!==1077936157&&(s|=16),A=O(u,e,i,o,B,F,T,A))))}else u.token===69271571?(s|=16,b===209007&&(S|=16),S|=(b===12402?256:b===12403?512:1)|2,V=g2(u,e,i),s|=u.assignable,A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos)):u.token&143360?(s|=16,b===121&&d(u,93),b===209007&&(u.flags&1&&d(u,129),S|=16),V=I(u,e,0),S|=b===12402?256:b===12403?512:1,A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos)):u.token===67174411?(s|=16,S|=1,A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos)):u.token===8457014?(s|=16,b===12402?d(u,40):b===12403?d(u,41):b===143483&&d(u,93),D(u,e),S|=9|(b===209007?16:0),u.token&143360?V=I(u,e,0):(u.token&134217728)===134217728?V=X(u,e):u.token===69271571?(S|=2,V=g2(u,e,i),s|=u.assignable):d(u,28,U[u.token&255]),A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos)):(u.token&134217728)===134217728?(b===209007&&(S|=16),S|=b===12402?256:b===12403?512:1,s|=16,V=X(u,e),A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos)):d(u,130);else if((u.token&134217728)===134217728)if(V=X(u,e),u.token===21){P(u,e|32768,21);let{tokenPos:B,linePos:F,colPos:T}=u;if(E==="__proto__"&&k++,u.token&143360){A=K(u,e,l,0,1,0,i,1,B,F,T);let{token:o2,tokenValue:m2}=u;A=N(u,e,A,i,0,B,F,T),u.token===18||u.token===1074790415?o2===1077936157||o2===1074790415||o2===18?u.assignable&2?s|=16:n&&u2(u,e,n,m2,l,f):s|=u.assignable&1?32:16:u.token===1077936157?(u.assignable&2&&(s|=16),A=O(u,e,i,o,B,F,T,A)):(s|=16,A=O(u,e,i,o,B,F,T,A))}else(u.token&2097152)===2097152?(A=u.token===69271571?_(u,e,n,0,i,o,l,f,B,F,T):Y(u,e,n,0,i,o,l,f,B,F,T),s=u.destructible,u.assignable=s&16?2:1,u.token===18||u.token===1074790415?u.assignable&2&&(s|=16):(u.destructible&8)!==8&&(A=N(u,e,A,i,0,B,F,T),s=u.assignable&2?16:0,(u.token&4194304)===4194304?A=N2(u,e,i,o,B,F,T,A):((u.token&8454144)===8454144&&(A=n2(u,e,1,B,F,T,4,b,A)),q(u,e|32768,22)&&(A=f2(u,e,A,B,F,T)),s|=u.assignable&2?16:32))):(A=W(u,e,1,0,1,B,F,T),s|=u.assignable&1?32:16,u.token===18||u.token===1074790415?u.assignable&2&&(s|=16):(A=N(u,e,A,i,0,B,F,T),s=u.assignable&1?0:16,u.token!==18&&u.token!==1074790415&&(u.token!==1077936157&&(s|=16),A=O(u,e,i,o,B,F,T,A))))}else u.token===67174411?(S|=1,A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos),s=u.assignable|16):d(u,131);else if(u.token===69271571)if(V=g2(u,e,i),s|=u.destructible&256?256:0,S|=2,u.token===21){D(u,e|32768);let{tokenPos:B,linePos:F,colPos:T,tokenValue:o2,token:m2}=u;if(u.token&143360){A=K(u,e,l,0,1,0,i,1,B,F,T);let{token:x}=u;A=N(u,e,A,i,0,B,F,T),(u.token&4194304)===4194304?(s|=u.assignable&2?16:x===1077936157?0:32,A=N2(u,e,i,o,B,F,T,A)):u.token===18||u.token===1074790415?x===1077936157||x===1074790415||x===18?u.assignable&2?s|=16:n&&(m2&143360)===143360&&u2(u,e,n,o2,l,f):s|=u.assignable&1?32:16:(s|=16,A=O(u,e,i,o,B,F,T,A))}else(u.token&2097152)===2097152?(A=u.token===69271571?_(u,e,n,0,i,o,l,f,B,F,T):Y(u,e,n,0,i,o,l,f,B,F,T),s=u.destructible,u.assignable=s&16?2:1,u.token===18||u.token===1074790415?u.assignable&2&&(s|=16):s&8?d(u,60):(A=N(u,e,A,i,0,B,F,T),s=u.assignable&2?s|16:0,(u.token&4194304)===4194304?(u.token!==1077936157&&(s|=16),A=N2(u,e,i,o,B,F,T,A)):((u.token&8454144)===8454144&&(A=n2(u,e,1,B,F,T,4,b,A)),q(u,e|32768,22)&&(A=f2(u,e,A,B,F,T)),s|=u.assignable&2?16:32))):(A=W(u,e,1,0,1,B,F,T),s|=u.assignable&1?32:16,u.token===18||u.token===1074790415?u.assignable&2&&(s|=16):(A=N(u,e,A,i,0,B,F,T),s=u.assignable&1?0:16,u.token!==18&&u.token!==1074790415&&(u.token!==1077936157&&(s|=16),A=O(u,e,i,o,B,F,T,A))))}else u.token===67174411?(S|=1,A=Z(u,e,S,i,u.tokenPos,w,v),s=16):d(u,42);else if(b===8457014)if(P(u,e|32768,8457014),S|=8,u.token&143360){let{token:B,line:F,index:T}=u;V=I(u,e,0),S|=1,u.token===67174411?(s|=16,A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos)):h2(T,F,T,B===209007?44:B===12402||u.token===12403?43:45,U[B&255])}else(u.token&134217728)===134217728?(s|=16,V=X(u,e),S|=1,A=Z(u,e,S,i,M,w,v)):u.token===69271571?(s|=16,S|=3,V=g2(u,e,i),A=Z(u,e,S,i,u.tokenPos,u.linePos,u.colPos)):d(u,123);else d(u,28,U[b&255]);s|=u.destructible&128?128:0,u.destructible=s,m.push(y(u,e,M,w,v,{type:"Property",key:V,value:A,kind:S&768?S&512?"set":"get":"init",computed:(S&2)>0,method:(S&1)>0,shorthand:(S&4)>0}))}if(s|=u.destructible,u.token!==18)break;D(u,e)}P(u,e,1074790415),k>1&&(s|=64);let C=y(u,e,c,a,g,{type:o?"ObjectPattern":"ObjectExpression",properties:m});return!t&&u.token&4194304?ue(u,e,s,i,o,c,a,g,C):(u.destructible=s,C)}function _1(u,e,n,t,i,o){P(u,e,67174411);let l=[];if(u.flags=(u.flags|128)^128,u.token===16)return t&512&&d(u,35,"Setter","one",""),D(u,e),l;t&256&&d(u,35,"Getter","no","s"),t&512&&u.token===14&&d(u,36),e=(e|134217728)^134217728;let f=0,c=0;for(;u.token!==18;){let a=null,{tokenPos:g,linePos:m,colPos:s}=u;if(u.token&143360?(e&1024||((u.token&36864)===36864&&(u.flags|=256),(u.token&537079808)===537079808&&(u.flags|=512)),a=mu(u,e,n,t|1,0,g,m,s)):(u.token===2162700?a=Y(u,e,n,1,o,1,i,0,g,m,s):u.token===69271571?a=_(u,e,n,1,o,1,i,0,g,m,s):u.token===14&&(a=b2(u,e,n,16,i,0,0,o,1,g,m,s)),c=1,u.destructible&48&&d(u,48)),u.token===1077936157){D(u,e|32768),c=1;let k=R(u,e,1,1,0,u.tokenPos,u.linePos,u.colPos);a=y(u,e,g,m,s,{type:"AssignmentPattern",left:a,right:k})}if(f++,l.push(a),!q(u,e,18)||u.token===16)break}return t&512&&f!==1&&d(u,35,"Setter","one",""),n&&n.scopeError!==void 0&&z2(n.scopeError),c&&(u.flags|=128),P(u,e,16),l}function g2(u,e,n){D(u,e|32768);let t=R(u,(e|134217728)^134217728,1,0,n,u.tokenPos,u.linePos,u.colPos);return P(u,e,20),t}function Y1(u,e,n,t,i,o,l,f){u.flags=(u.flags|128)^128;let{tokenPos:c,linePos:a,colPos:g}=u;D(u,e|32768|1073741824);let m=e&64?J(s2(),1024):void 0;if(e=(e|134217728)^134217728,q(u,e,16))return j2(u,e,m,[],n,0,o,l,f);let s=0;u.destructible&=-385;let k,C=[],b=0,E=0,{tokenPos:w,linePos:v,colPos:M}=u;for(u.assignable=1;u.token!==16;){let{token:S,tokenPos:V,linePos:A,colPos:Q}=u;if(S&143360)m&&t2(u,e,m,u.tokenValue,1,0),k=K(u,e,t,0,1,0,1,1,V,A,Q),u.token===16||u.token===18?u.assignable&2?(s|=16,E=1):((S&537079808)===537079808||(S&36864)===36864)&&(E=1):(u.token===1077936157?E=1:s|=16,k=N(u,e,k,1,0,V,A,Q),u.token!==16&&u.token!==18&&(k=O(u,e,1,0,V,A,Q,k)));else if((S&2097152)===2097152)k=S===2162700?Y(u,e|1073741824,m,0,1,0,t,i,V,A,Q):_(u,e|1073741824,m,0,1,0,t,i,V,A,Q),s|=u.destructible,E=1,u.assignable=2,u.token!==16&&u.token!==18&&(s&8&&d(u,119),k=N(u,e,k,0,0,V,A,Q),s|=16,u.token!==16&&u.token!==18&&(k=O(u,e,0,0,V,A,Q,k)));else if(S===14){k=b2(u,e,m,16,t,i,0,1,0,V,A,Q),u.destructible&16&&d(u,72),E=1,b&&(u.token===16||u.token===18)&&C.push(k),s|=8;break}else{if(s|=16,k=R(u,e,1,0,1,V,A,Q),b&&(u.token===16||u.token===18)&&C.push(k),u.token===18&&(b||(b=1,C=[k])),b){for(;q(u,e|32768,18);)C.push(R(u,e,1,0,1,u.tokenPos,u.linePos,u.colPos));u.assignable=2,k=y(u,e,w,v,M,{type:"SequenceExpression",expressions:C})}return P(u,e,16),u.destructible=s,k}if(b&&(u.token===16||u.token===18)&&C.push(k),!q(u,e|32768,18))break;if(b||(b=1,C=[k]),u.token===16){s|=8;break}}return b&&(u.assignable=2,k=y(u,e,w,v,M,{type:"SequenceExpression",expressions:C})),P(u,e,16),s&16&&s&8&&d(u,146),s|=u.destructible&256?256:0|u.destructible&128?128:0,u.token===10?(s&48&&d(u,47),e&4196352&&s&128&&d(u,29),e&2098176&&s&256&&d(u,30),E&&(u.flags|=128),j2(u,e,m,b?C:[k],n,0,o,l,f)):(s&8&&d(u,140),u.destructible=(u.destructible|256)^256|s,e&128?y(u,e,c,a,g,{type:"ParenthesizedExpression",expression:k}):k)}function su(u,e,n,t,i){let{tokenValue:o}=u,l=I(u,e,0);if(u.assignable=1,u.token===10){let f;return e&64&&(f=H2(u,e,o)),u.flags=(u.flags|128)^128,v2(u,e,f,[l],0,n,t,i)}return l}function $2(u,e,n,t,i,o,l,f,c,a){o||d(u,55),i&&d(u,49),u.flags&=-129;let g=e&64?H2(u,e,n):void 0;return v2(u,e,g,[t],l,f,c,a)}function j2(u,e,n,t,i,o,l,f,c){i||d(u,55);for(let a=0;a<t.length;++a)r(u,t[a]);return v2(u,e,n,t,o,l,f,c)}function v2(u,e,n,t,i,o,l,f){u.flags&1&&d(u,46),P(u,e|32768,10),e=(e|15728640)^15728640|i<<22;let c=u.token!==2162700,a;if(n&&n.scopeError!==void 0&&z2(n.scopeError),c)a=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos);else{switch(n&&(n=J(n,128)),a=K2(u,(e|134221824|8192|16384)^134246400,n,16,void 0,void 0),u.token){case 69271571:u.flags&1||d(u,113);break;case 67108877:case 67174409:case 22:d(u,114);case 67174411:u.flags&1||d(u,113),u.flags|=1024;break}(u.token&8454144)===8454144&&!(u.flags&1)&&d(u,28,U[u.token&255]),(u.token&33619968)===33619968&&d(u,122)}return u.assignable=2,y(u,e,o,l,f,{type:"ArrowFunctionExpression",params:t,body:a,async:i===1,expression:c})}function ee(u,e,n,t,i){P(u,e,67174411),u.flags=(u.flags|128)^128;let o=[];if(q(u,e,16))return o;e=(e|134217728)^134217728;let l=0;for(;u.token!==18;){let f,{tokenPos:c,linePos:a,colPos:g}=u;if(u.token&143360?(e&1024||((u.token&36864)===36864&&(u.flags|=256),(u.token&537079808)===537079808&&(u.flags|=512)),f=mu(u,e,n,i|1,0,c,a,g)):(u.token===2162700?f=Y(u,e,n,1,t,1,i,0,c,a,g):u.token===69271571?f=_(u,e,n,1,t,1,i,0,c,a,g):u.token===14?f=b2(u,e,n,16,i,0,0,t,1,c,a,g):d(u,28,U[u.token&255]),l=1,u.destructible&48&&d(u,48)),u.token===1077936157){D(u,e|32768),l=1;let m=R(u,e,1,1,t,u.tokenPos,u.linePos,u.colPos);f=y(u,e,c,a,g,{type:"AssignmentPattern",left:f,right:m})}if(o.push(f),!q(u,e,18)||u.token===16)break}return l&&(u.flags|=128),n&&(l||e&1024)&&n.scopeError!==void 0&&z2(n.scopeError),P(u,e,16),o}function O2(u,e,n,t,i,o,l){let{token:f}=u;if(f&67108864){if(f===67108877){D(u,e|1073741824),u.assignable=1;let c=Zu(u,e);return O2(u,e,y(u,e,i,o,l,{type:"MemberExpression",object:n,computed:!1,property:c}),0,i,o,l)}else if(f===69271571){D(u,e|32768);let{tokenPos:c,linePos:a,colPos:g}=u,m=j(u,e,t,1,c,a,g);return P(u,e,20),u.assignable=1,O2(u,e,y(u,e,i,o,l,{type:"MemberExpression",object:n,computed:!0,property:m}),0,i,o,l)}else if(f===67174408||f===67174409)return u.assignable=2,O2(u,e,y(u,e,i,o,l,{type:"TaggedTemplateExpression",tag:n,quasi:u.token===67174408?cu(u,e|65536):fu(u,e,u.tokenPos,u.linePos,u.colPos)}),0,i,o,l)}return n}function Q1(u,e,n,t,i,o){let l=I(u,e|32768,0),{tokenPos:f,linePos:c,colPos:a}=u;if(q(u,e,67108877)){if(e&67108864&&u.token===143494)return u.assignable=2,Z1(u,e,l,t,i,o);d(u,92)}u.assignable=2,(u.token&16842752)===16842752&&d(u,63,U[u.token&255]);let g=K(u,e,2,1,0,0,n,1,f,c,a);e=(e|134217728)^134217728,u.token===67108991&&d(u,163);let m=O2(u,e,g,n,f,c,a);return u.assignable=2,y(u,e,t,i,o,{type:"NewExpression",callee:m,arguments:u.token===67174411?du(u,e,n):[]})}function Z1(u,e,n,t,i,o){let l=I(u,e,0);return y(u,e,t,i,o,{type:"MetaProperty",meta:n,property:l})}function ne(u,e,n,t,i,o){return u.token===209008&&d(u,29),e&2098176&&u.token===241773&&d(u,30),(u.token&537079808)===537079808&&(u.flags|=512),$2(u,e,u.tokenValue,I(u,e,0),0,n,1,t,i,o)}function au(u,e,n,t,i,o,l,f,c,a){D(u,e|32768);let g=e&64?J(s2(),1024):void 0;if(e=(e|134217728)^134217728,q(u,e,16))return u.token===10?(l&1&&d(u,46),j2(u,e,g,[],t,1,f,c,a)):y(u,e,f,c,a,{type:"CallExpression",callee:n,arguments:[]});let m=0,s=null,k=0;u.destructible=(u.destructible|256|128)^384;let C=[];for(;u.token!==16;){let{token:b,tokenPos:E,linePos:w,colPos:v}=u;if(b&143360)g&&t2(u,e,g,u.tokenValue,i,0),s=K(u,e,i,0,1,0,1,1,E,w,v),u.token===16||u.token===18?u.assignable&2?(m|=16,k=1):(b&537079808)===537079808?u.flags|=512:(b&36864)===36864&&(u.flags|=256):(u.token===1077936157?k=1:m|=16,s=N(u,e,s,1,0,E,w,v),u.token!==16&&u.token!==18&&(s=O(u,e,1,0,E,w,v,s)));else if(b&2097152)s=b===2162700?Y(u,e,g,0,1,0,i,o,E,w,v):_(u,e,g,0,1,0,i,o,E,w,v),m|=u.destructible,k=1,u.token!==16&&u.token!==18&&(m&8&&d(u,119),s=N(u,e,s,0,0,E,w,v),m|=16,(u.token&8454144)===8454144&&(s=n2(u,e,1,f,c,a,4,b,s)),q(u,e|32768,22)&&(s=f2(u,e,s,f,c,a)));else if(b===14)s=b2(u,e,g,16,i,o,1,1,0,E,w,v),m|=(u.token===16?0:16)|u.destructible,k=1;else{for(s=R(u,e,1,0,0,E,w,v),m=u.assignable,C.push(s);q(u,e|32768,18);)C.push(R(u,e,1,0,0,E,w,v));return m|=u.assignable,P(u,e,16),u.destructible=m|16,u.assignable=2,y(u,e,f,c,a,{type:"CallExpression",callee:n,arguments:C})}if(C.push(s),!q(u,e|32768,18))break}return P(u,e,16),m|=u.destructible&256?256:0|u.destructible&128?128:0,u.token===10?(m&48&&d(u,25),(u.flags&1||l&1)&&d(u,46),m&128&&d(u,29),e&2098176&&m&256&&d(u,30),k&&(u.flags|=128),j2(u,e,g,C,t,1,f,c,a)):(m&8&&d(u,60),u.assignable=2,y(u,e,f,c,a,{type:"CallExpression",callee:n,arguments:C}))}function r1(u,e,n,t,i){let{tokenRaw:o,tokenRegExp:l,tokenValue:f}=u;return D(u,e),u.assignable=2,e&512?y(u,e,n,t,i,{type:"Literal",value:f,regex:l,raw:o}):y(u,e,n,t,i,{type:"Literal",value:f,regex:l})}function x2(u,e,n,t,i,o,l){e=(e|16777216|1024)^16777216;let f=W2(u,e);f.length&&(i=u.tokenPos,o=u.linePos,l=u.colPos),u.leadingDecorators.length&&(u.leadingDecorators.push(...f),f=u.leadingDecorators,u.leadingDecorators=[]),D(u,e);let c=null,a=null,{tokenValue:g}=u;u.token&4096&&u.token!==20567?(zu(u,e,u.token)&&d(u,115),(u.token&537079808)===537079808&&d(u,116),n&&(t2(u,e,n,g,32,0),t&&t&2&&l2(u,g)),c=I(u,e,0)):t&1||d(u,37,"Class");let m=e;q(u,e|32768,20567)?(a=W(u,e,0,0,0,u.tokenPos,u.linePos,u.colPos),m|=524288):m=(m|524288)^524288;let s=ie(u,m,e,n,2,8,0);return y(u,e,i,o,l,e&1?{type:"ClassDeclaration",id:c,superClass:a,decorators:f,body:s}:{type:"ClassDeclaration",id:c,superClass:a,body:s})}function G1(u,e,n,t,i,o){let l=null,f=null;e=(e|1024|16777216)^16777216;let c=W2(u,e);c.length&&(t=u.tokenPos,i=u.linePos,o=u.colPos),D(u,e),u.token&4096&&u.token!==20567&&(zu(u,e,u.token)&&d(u,115),(u.token&537079808)===537079808&&d(u,116),l=I(u,e,0));let a=e;q(u,e|32768,20567)?(f=W(u,e,0,n,0,u.tokenPos,u.linePos,u.colPos),a|=524288):a=(a|524288)^524288;let g=ie(u,a,e,void 0,2,0,n);return u.assignable=2,y(u,e,t,i,o,e&1?{type:"ClassExpression",id:l,superClass:f,decorators:c,body:g}:{type:"ClassExpression",id:l,superClass:f,body:g})}function W2(u,e){let n=[];if(e&1)for(;u.token===133;)n.push(x1(u,e,u.tokenPos,u.linePos,u.colPos));return n}function x1(u,e,n,t,i){D(u,e|32768);let o=K(u,e,2,0,1,0,0,1,n,t,i);return o=N(u,e,o,0,0,n,t,i),y(u,e,n,t,i,{type:"Decorator",expression:o})}function ie(u,e,n,t,i,o,l){let{tokenPos:f,linePos:c,colPos:a}=u;P(u,e|32768,2162700),e=(e|134217728)^134217728;let g=u.flags&32;u.flags=(u.flags|32)^32;let m=[],s;for(;u.token!==1074790415;){let k=0;if(s=W2(u,e),k=s.length,k>0&&u.tokenValue==="constructor"&&d(u,107),u.token===1074790415&&d(u,106),q(u,e,1074790417)){k>0&&d(u,117);continue}m.push(te(u,e,t,n,i,s,0,l,u.tokenPos,u.linePos,u.colPos))}return P(u,o&8?e|32768:e,1074790415),u.flags=u.flags&-33|g,y(u,e,f,c,a,{type:"ClassBody",body:m})}function te(u,e,n,t,i,o,l,f,c,a,g){let m=l?32:0,s=null,{token:k,tokenPos:C,linePos:b,colPos:E}=u;if(k&176128)switch(s=I(u,e,0),k){case 36972:if(!l&&u.token!==67174411&&(u.token&1048576)!==1048576&&u.token!==1077936157)return te(u,e,n,t,i,o,1,f,c,a,g);break;case 209007:if(u.token!==67174411&&!(u.flags&1)){if(e&1&&(u.token&1073741824)===1073741824)return V2(u,e,s,m,o,C,b,E);m|=16|(nu(u,e,8457014)?8:0)}break;case 12402:if(u.token!==67174411){if(e&1&&(u.token&1073741824)===1073741824)return V2(u,e,s,m,o,C,b,E);m|=256}break;case 12403:if(u.token!==67174411){if(e&1&&(u.token&1073741824)===1073741824)return V2(u,e,s,m,o,C,b,E);m|=512}break}else if(k===69271571)m|=2,s=g2(u,t,f);else if((k&134217728)===134217728)s=X(u,e);else if(k===8457014)m|=8,D(u,e);else if(e&1&&u.token===131)m|=4096,s=X2(u,e|16384,C,b,E);else if(e&1&&(u.token&1073741824)===1073741824)m|=128;else{if(l&&k===2162700)return B1(u,e,n,C,b,E);k===122?(s=I(u,e,0),u.token!==67174411&&d(u,28,U[u.token&255])):d(u,28,U[u.token&255])}if(m&792&&(u.token&143360?s=I(u,e,0):(u.token&134217728)===134217728?s=X(u,e):u.token===69271571?(m|=2,s=g2(u,e,0)):u.token===122?s=I(u,e,0):e&1&&u.token===131?(m|=4096,s=X2(u,e,C,b,E)):d(u,132)),m&2||(u.tokenValue==="constructor"?((u.token&1073741824)===1073741824?d(u,126):!(m&32)&&u.token===67174411&&(m&920?d(u,51,"accessor"):e&524288||(u.flags&32?d(u,52):u.flags|=32)),m|=64):!(m&4096)&&m&824&&u.tokenValue==="prototype"&&d(u,50)),e&1&&u.token!==67174411)return V2(u,e,s,m,o,C,b,E);let w=Z(u,e,m,f,u.tokenPos,u.linePos,u.colPos);return y(u,e,c,a,g,e&1?{type:"MethodDefinition",kind:!(m&32)&&m&64?"constructor":m&256?"get":m&512?"set":"method",static:(m&32)>0,computed:(m&2)>0,key:s,decorators:o,value:w}:{type:"MethodDefinition",kind:!(m&32)&&m&64?"constructor":m&256?"get":m&512?"set":"method",static:(m&32)>0,computed:(m&2)>0,key:s,value:w})}function X2(u,e,n,t,i){D(u,e);let{tokenValue:o}=u;return o==="constructor"&&d(u,125),D(u,e),y(u,e,n,t,i,{type:"PrivateIdentifier",name:o})}function V2(u,e,n,t,i,o,l,f){let c=null;if(t&8&&d(u,0),u.token===1077936157){D(u,e|32768);let{tokenPos:a,linePos:g,colPos:m}=u;u.token===537079928&&d(u,116),c=K(u,e|16384,2,0,1,0,0,1,a,g,m),((u.token&1073741824)!==1073741824||(u.token&4194304)===4194304)&&(c=N(u,e|16384,c,0,0,a,g,m),c=O(u,e|16384,0,0,a,g,m,c),u.token===18&&(c=e2(u,e,0,o,l,f,c)))}return y(u,e,o,l,f,{type:"PropertyDefinition",key:n,value:c,static:(t&32)>0,computed:(t&2)>0,decorators:i})}function oe(u,e,n,t,i,o,l,f){if(u.token&143360)return mu(u,e,n,t,i,o,l,f);(u.token&2097152)!==2097152&&d(u,28,U[u.token&255]);let c=u.token===69271571?_(u,e,n,1,0,1,t,i,o,l,f):Y(u,e,n,1,0,1,t,i,o,l,f);return u.destructible&16&&d(u,48),u.destructible&32&&d(u,48),c}function mu(u,e,n,t,i,o,l,f){let{tokenValue:c,token:a}=u;return e&1024&&((a&537079808)===537079808?d(u,116):(a&36864)===36864&&d(u,115)),(a&20480)===20480&&d(u,100),e&2099200&&a===241773&&d(u,30),a===241739&&t&24&&d(u,98),e&4196352&&a===209008&&d(u,96),D(u,e),n&&u2(u,e,n,c,t,i),y(u,e,o,l,f,{type:"Identifier",name:c})}function gu(u,e,n,t,i,o){if(D(u,e),u.token===8456259)return y(u,e,t,i,o,{type:"JSXFragment",openingFragment:p1(u,e,t,i,o),children:qu(u,e),closingFragment:e0(u,e,n,u.tokenPos,u.linePos,u.colPos)});let l=null,f=[],c=t0(u,e,n,t,i,o);if(!c.selfClosing){f=qu(u,e),l=u0(u,e,n,u.tokenPos,u.linePos,u.colPos);let a=J2(l.name);J2(c.name)!==a&&d(u,150,a)}return y(u,e,t,i,o,{type:"JSXElement",children:f,openingElement:c,closingElement:l})}function p1(u,e,n,t,i){return d2(u,e),y(u,e,n,t,i,{type:"JSXOpeningFragment"})}function u0(u,e,n,t,i,o){P(u,e,25);let l=le(u,e,u.tokenPos,u.linePos,u.colPos);return n?P(u,e,8456259):u.token=d2(u,e),y(u,e,t,i,o,{type:"JSXClosingElement",name:l})}function e0(u,e,n,t,i,o){return P(u,e,25),P(u,e,8456259),y(u,e,t,i,o,{type:"JSXClosingFragment"})}function qu(u,e){let n=[];for(;u.token!==25;)u.index=u.tokenPos=u.startPos,u.column=u.colPos=u.startColumn,u.line=u.linePos=u.startLine,d2(u,e),n.push(n0(u,e,u.tokenPos,u.linePos,u.colPos));return n}function n0(u,e,n,t,i){if(u.token===138)return i0(u,e,n,t,i);if(u.token===2162700)return ce(u,e,0,0,n,t,i);if(u.token===8456258)return gu(u,e,0,n,t,i);d(u,0)}function i0(u,e,n,t,i){d2(u,e);let o={type:"JSXText",value:u.tokenValue};return e&512&&(o.raw=u.tokenRaw),y(u,e,n,t,i,o)}function t0(u,e,n,t,i,o){(u.token&143360)!==143360&&(u.token&4096)!==4096&&d(u,0);let l=le(u,e,u.tokenPos,u.linePos,u.colPos),f=l0(u,e),c=u.token===8457016;return u.token===8456259?d2(u,e):(P(u,e,8457016),n?P(u,e,8456259):d2(u,e)),y(u,e,t,i,o,{type:"JSXOpeningElement",name:l,attributes:f,selfClosing:c})}function le(u,e,n,t,i){Z2(u);let o=_2(u,e,n,t,i);if(u.token===21)return fe(u,e,o,n,t,i);for(;q(u,e,67108877);)Z2(u),o=o0(u,e,o,n,t,i);return o}function o0(u,e,n,t,i,o){let l=_2(u,e,u.tokenPos,u.linePos,u.colPos);return y(u,e,t,i,o,{type:"JSXMemberExpression",object:n,property:l})}function l0(u,e){let n=[];for(;u.token!==8457016&&u.token!==8456259&&u.token!==1048576;)n.push(c0(u,e,u.tokenPos,u.linePos,u.colPos));return n}function f0(u,e,n,t,i){D(u,e),P(u,e,14);let o=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos);return P(u,e,1074790415),y(u,e,n,t,i,{type:"JSXSpreadAttribute",argument:o})}function c0(u,e,n,t,i){if(u.token===2162700)return f0(u,e,n,t,i);Z2(u);let o=null,l=_2(u,e,n,t,i);if(u.token===21&&(l=fe(u,e,l,n,t,i)),u.token===1077936157){let f=u1(u,e),{tokenPos:c,linePos:a,colPos:g}=u;switch(f){case 134283267:o=X(u,e);break;case 8456258:o=gu(u,e,1,c,a,g);break;case 2162700:o=ce(u,e,1,1,c,a,g);break;default:d(u,149)}}return y(u,e,n,t,i,{type:"JSXAttribute",value:o,name:l})}function fe(u,e,n,t,i,o){P(u,e,21);let l=_2(u,e,u.tokenPos,u.linePos,u.colPos);return y(u,e,t,i,o,{type:"JSXNamespacedName",namespace:n,name:l})}function ce(u,e,n,t,i,o,l){D(u,e|32768);let{tokenPos:f,linePos:c,colPos:a}=u;if(u.token===14)return d0(u,e,i,o,l);let g=null;return u.token===1074790415?(t&&d(u,152),g=s0(u,e,u.startPos,u.startLine,u.startColumn)):g=R(u,e,1,0,0,f,c,a),n?P(u,e,1074790415):d2(u,e),y(u,e,i,o,l,{type:"JSXExpressionContainer",expression:g})}function d0(u,e,n,t,i){P(u,e,14);let o=R(u,e,1,0,0,u.tokenPos,u.linePos,u.colPos);return P(u,e,1074790415),y(u,e,n,t,i,{type:"JSXSpreadChild",expression:o})}function s0(u,e,n,t,i){return u.startPos=u.tokenPos,u.startLine=u.linePos,u.startColumn=u.colPos,y(u,e,n,t,i,{type:"JSXEmptyExpression"})}function _2(u,e,n,t,i){let{tokenValue:o}=u;return D(u,e),y(u,e,n,t,i,{type:"JSXIdentifier",name:o})}function de(u,e){return c1(u,e,0)}function a0(u,e){let n=new SyntaxError(u+" ("+e.loc.start.line+":"+e.loc.start.column+")");return Object.assign(n,e)}var se=a0;function m0(u){let e=[];for(let n of u)try{return n()}catch(t){e.push(t)}throw Object.assign(new Error("All combinations failed"),{errors:e})}var ae=m0;var g0=(u,e,n)=>{if(!(u&&e==null))return Array.isArray(e)||typeof e=="string"?e[n<0?e.length+n:n]:e.at(n)},yu=g0;function y0(u){return Array.isArray(u)&&u.length>0}var me=y0;function $(u){var t,i,o;let e=((t=u.range)==null?void 0:t[0])??u.start,n=(o=((i=u.declaration)==null?void 0:i.decorators)??u.decorators)==null?void 0:o[0];return n?Math.min($(n),e):e}function p(u){var e;return((e=u.range)==null?void 0:e[1])??u.end}function k0(u){let e=new Set(u);return n=>e.has(n==null?void 0:n.type)}var ge=k0;var h0=ge(["Block","CommentBlock","MultiLine"]),T2=h0;function A0(u){let e=`*${u.value}*`.split(`
`);return e.length>1&&e.every(n=>n.trimStart()[0]==="*")}var ku=A0;function D0(u){return T2(u)&&u.value[0]==="*"&&/@(?:type|satisfies)\b/.test(u.value)}var ye=D0;var F2=null;function q2(u){if(F2!==null&&typeof F2.property){let e=F2;return F2=q2.prototype=null,e}return F2=q2.prototype=u??Object.create(null),new q2}var b0=10;for(let u=0;u<=b0;u++)q2();function hu(u){return q2(u)}function C0(u,e="type"){hu(u);function n(t){let i=t[e],o=u[i];if(!Array.isArray(o))throw Object.assign(new Error(`Missing visitor keys for '${i}'.`),{node:t});return o}return n}var ke=C0;var he={ArrayExpression:["elements"],AssignmentExpression:["left","right"],BinaryExpression:["left","right"],InterpreterDirective:[],Directive:["value"],DirectiveLiteral:[],BlockStatement:["directives","body"],BreakStatement:["label"],CallExpression:["callee","arguments","typeParameters","typeArguments"],CatchClause:["param","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DoWhileStatement:["test","body"],EmptyStatement:[],ExpressionStatement:["expression"],File:["program"],ForInStatement:["left","right","body"],ForStatement:["init","test","update","body"],FunctionDeclaration:["id","params","body","returnType","typeParameters","predicate"],FunctionExpression:["id","params","body","returnType","typeParameters"],Identifier:["typeAnnotation","decorators"],IfStatement:["test","consequent","alternate"],LabeledStatement:["label","body"],StringLiteral:[],NumericLiteral:[],NullLiteral:[],BooleanLiteral:[],RegExpLiteral:[],LogicalExpression:["left","right"],MemberExpression:["object","property"],NewExpression:["callee","arguments","typeParameters","typeArguments"],Program:["directives","body"],ObjectExpression:["properties"],ObjectMethod:["key","params","body","decorators","returnType","typeParameters"],ObjectProperty:["key","value","decorators"],RestElement:["argument","typeAnnotation","decorators"],ReturnStatement:["argument"],SequenceExpression:["expressions"],ParenthesizedExpression:["expression"],SwitchCase:["test","consequent"],SwitchStatement:["discriminant","cases"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],AssignmentPattern:["left","right","decorators","typeAnnotation"],ArrayPattern:["elements","typeAnnotation","decorators"],ArrowFunctionExpression:["params","body","returnType","typeParameters","predicate"],ClassBody:["body"],ClassExpression:["id","body","superClass","mixins","typeParameters","superTypeParameters","implements","decorators","superTypeArguments"],ClassDeclaration:["id","body","superClass","mixins","typeParameters","superTypeParameters","implements","decorators","superTypeArguments"],ExportAllDeclaration:["source","attributes","exported"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source","attributes"],ExportSpecifier:["local","exported"],ForOfStatement:["left","right","body"],ImportDeclaration:["specifiers","source","attributes"],ImportDefaultSpecifier:["local"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["local","imported"],ImportExpression:["source","options","attributes"],MetaProperty:["meta","property"],ClassMethod:["key","params","body","decorators","returnType","typeParameters"],ObjectPattern:["properties","typeAnnotation","decorators"],SpreadElement:["argument"],Super:[],TaggedTemplateExpression:["tag","quasi","typeParameters","typeArguments"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],YieldExpression:["argument"],AwaitExpression:["argument"],Import:[],BigIntLiteral:[],ExportNamespaceSpecifier:["exported"],OptionalMemberExpression:["object","property"],OptionalCallExpression:["callee","arguments","typeParameters","typeArguments"],ClassProperty:["key","value","typeAnnotation","decorators","variance"],ClassAccessorProperty:["key","value","typeAnnotation","decorators"],ClassPrivateProperty:["key","value","decorators","typeAnnotation","variance"],ClassPrivateMethod:["key","params","body","decorators","returnType","typeParameters"],PrivateName:["id"],StaticBlock:["body"],AnyTypeAnnotation:[],ArrayTypeAnnotation:["elementType"],BooleanTypeAnnotation:[],BooleanLiteralTypeAnnotation:[],NullLiteralTypeAnnotation:[],ClassImplements:["id","typeParameters"],DeclareClass:["id","typeParameters","extends","mixins","implements","body"],DeclareFunction:["id","predicate"],DeclareInterface:["id","typeParameters","extends","body"],DeclareModule:["id","body"],DeclareModuleExports:["typeAnnotation"],DeclareTypeAlias:["id","typeParameters","right"],DeclareOpaqueType:["id","typeParameters","supertype"],DeclareVariable:["id"],DeclareExportDeclaration:["declaration","specifiers","source"],DeclareExportAllDeclaration:["source"],DeclaredPredicate:["value"],ExistsTypeAnnotation:[],FunctionTypeAnnotation:["typeParameters","params","rest","returnType","this"],FunctionTypeParam:["name","typeAnnotation"],GenericTypeAnnotation:["id","typeParameters"],InferredPredicate:[],InterfaceExtends:["id","typeParameters"],InterfaceDeclaration:["id","typeParameters","extends","body"],InterfaceTypeAnnotation:["extends","body"],IntersectionTypeAnnotation:["types"],MixedTypeAnnotation:[],EmptyTypeAnnotation:[],NullableTypeAnnotation:["typeAnnotation"],NumberLiteralTypeAnnotation:[],NumberTypeAnnotation:[],ObjectTypeAnnotation:["properties","indexers","callProperties","internalSlots"],ObjectTypeInternalSlot:["id","value","optional","static","method"],ObjectTypeCallProperty:["value"],ObjectTypeIndexer:["id","key","value","variance"],ObjectTypeProperty:["key","value","variance"],ObjectTypeSpreadProperty:["argument"],OpaqueType:["id","typeParameters","supertype","impltype"],QualifiedTypeIdentifier:["id","qualification"],StringLiteralTypeAnnotation:[],StringTypeAnnotation:[],SymbolTypeAnnotation:[],ThisTypeAnnotation:[],TupleTypeAnnotation:["types","elementTypes"],TypeofTypeAnnotation:["argument","typeArguments"],TypeAlias:["id","typeParameters","right"],TypeAnnotation:["typeAnnotation"],TypeCastExpression:["expression","typeAnnotation"],TypeParameter:["bound","default","variance"],TypeParameterDeclaration:["params"],TypeParameterInstantiation:["params"],UnionTypeAnnotation:["types"],Variance:[],VoidTypeAnnotation:[],EnumDeclaration:["id","body"],EnumBooleanBody:["members"],EnumNumberBody:["members"],EnumStringBody:["members"],EnumSymbolBody:["members"],EnumBooleanMember:["id","init"],EnumNumberMember:["id","init"],EnumStringMember:["id","init"],EnumDefaultedMember:["id"],IndexedAccessType:["objectType","indexType"],OptionalIndexedAccessType:["objectType","indexType"],JSXAttribute:["name","value"],JSXClosingElement:["name"],JSXElement:["openingElement","children","closingElement"],JSXEmptyExpression:[],JSXExpressionContainer:["expression"],JSXSpreadChild:["expression"],JSXIdentifier:[],JSXMemberExpression:["object","property"],JSXNamespacedName:["namespace","name"],JSXOpeningElement:["name","attributes","typeArguments","typeParameters"],JSXSpreadAttribute:["argument"],JSXText:[],JSXFragment:["openingFragment","children","closingFragment"],JSXOpeningFragment:[],JSXClosingFragment:[],Noop:[],Placeholder:[],V8IntrinsicIdentifier:[],ArgumentPlaceholder:[],BindExpression:["object","callee"],ImportAttribute:["key","value"],Decorator:["expression"],DoExpression:["body"],ExportDefaultSpecifier:["exported"],RecordExpression:["properties"],TupleExpression:["elements"],DecimalLiteral:[],ModuleExpression:["body"],TopicReference:[],PipelineTopicExpression:["expression"],PipelineBareFunction:["callee"],PipelinePrimaryTopicReference:[],TSParameterProperty:["parameter","decorators"],TSDeclareFunction:["id","typeParameters","params","returnType","body"],TSDeclareMethod:["decorators","key","typeParameters","params","returnType"],TSQualifiedName:["left","right"],TSCallSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructSignatureDeclaration:["typeParameters","parameters","typeAnnotation","params","returnType"],TSPropertySignature:["key","typeAnnotation"],TSMethodSignature:["key","typeParameters","parameters","typeAnnotation","params","returnType"],TSIndexSignature:["parameters","typeAnnotation"],TSAnyKeyword:[],TSBooleanKeyword:[],TSBigIntKeyword:[],TSIntrinsicKeyword:[],TSNeverKeyword:[],TSNullKeyword:[],TSNumberKeyword:[],TSObjectKeyword:[],TSStringKeyword:[],TSSymbolKeyword:[],TSUndefinedKeyword:[],TSUnknownKeyword:[],TSVoidKeyword:[],TSThisType:[],TSFunctionType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSConstructorType:["typeParameters","parameters","typeAnnotation","params","returnType"],TSTypeReference:["typeName","typeParameters","typeArguments"],TSTypePredicate:["parameterName","typeAnnotation"],TSTypeQuery:["exprName","typeParameters","typeArguments"],TSTypeLiteral:["members"],TSArrayType:["elementType"],TSTupleType:["elementTypes"],TSOptionalType:["typeAnnotation"],TSRestType:["typeAnnotation"],TSNamedTupleMember:["label","elementType"],TSUnionType:["types"],TSIntersectionType:["types"],TSConditionalType:["checkType","extendsType","trueType","falseType"],TSInferType:["typeParameter"],TSParenthesizedType:["typeAnnotation"],TSTypeOperator:["typeAnnotation"],TSIndexedAccessType:["objectType","indexType"],TSMappedType:["typeParameter","typeAnnotation","nameType"],TSLiteralType:["literal"],TSExpressionWithTypeArguments:["expression","typeParameters"],TSInterfaceDeclaration:["id","typeParameters","extends","body"],TSInterfaceBody:["body"],TSTypeAliasDeclaration:["id","typeParameters","typeAnnotation"],TSInstantiationExpression:["expression","typeParameters","typeArguments"],TSAsExpression:["expression","typeAnnotation"],TSSatisfiesExpression:["expression","typeAnnotation"],TSTypeAssertion:["typeAnnotation","expression"],TSEnumDeclaration:["id","members"],TSEnumMember:["id","initializer"],TSModuleDeclaration:["id","body"],TSModuleBlock:["body"],TSImportType:["argument","qualifier","typeParameters","typeArguments"],TSImportEqualsDeclaration:["id","moduleReference"],TSExternalModuleReference:["expression"],TSNonNullExpression:["expression"],TSExportAssignment:["expression"],TSNamespaceExportDeclaration:["id"],TSTypeAnnotation:["typeAnnotation"],TSTypeParameterInstantiation:["params"],TSTypeParameterDeclaration:["params"],TSTypeParameter:["constraint","default","name"],ChainExpression:["expression"],ExperimentalRestProperty:["argument"],ExperimentalSpreadProperty:["argument"],Literal:[],MethodDefinition:["decorators","key","value"],PrivateIdentifier:[],Property:["key","value"],PropertyDefinition:["decorators","key","typeAnnotation","value","variance"],AccessorProperty:["decorators","key","typeAnnotation","value"],TSAbstractAccessorProperty:["decorators","key","typeAnnotation"],TSAbstractKeyword:[],TSAbstractMethodDefinition:["key","value"],TSAbstractPropertyDefinition:["decorators","key","typeAnnotation"],TSAsyncKeyword:[],TSClassImplements:["expression","typeArguments","typeParameters"],TSDeclareKeyword:[],TSEmptyBodyFunctionExpression:["id","typeParameters","params","returnType"],TSExportKeyword:[],TSInterfaceHeritage:["expression","typeArguments","typeParameters"],TSPrivateKeyword:[],TSProtectedKeyword:[],TSPublicKeyword:[],TSReadonlyKeyword:[],TSStaticKeyword:[],TSTemplateLiteralType:["quasis","types"],AsExpression:["expression","typeAnnotation"],BigIntLiteralTypeAnnotation:[],BigIntTypeAnnotation:[],ConditionalTypeAnnotation:["checkType","extendsType","trueType","falseType"],DeclareEnum:["id","body"],InferTypeAnnotation:["typeParameter"],KeyofTypeAnnotation:["argument"],ObjectTypeMappedTypeProperty:["keyTparam","propType","sourceType","variance"],QualifiedTypeofIdentifier:["qualification","id"],TupleTypeLabeledElement:["label","elementType","variance"],TupleTypeSpreadElement:["label","typeAnnotation"],TypeOperator:["typeAnnotation"],TypePredicate:["parameterName","typeAnnotation","asserts"],NGRoot:["node"],NGPipeExpression:["left","right","arguments"],NGChainedExpression:["expressions"],NGEmptyExpression:[],NGMicrosyntax:["body"],NGMicrosyntaxKey:[],NGMicrosyntaxExpression:["expression","alias"],NGMicrosyntaxKeyedExpression:["key","expression"],NGMicrosyntaxLet:["key","value"],NGMicrosyntaxAs:["key","alias"],JsExpressionRoot:["node"],JsonRoot:["node"],TSJSDocAllType:[],TSJSDocUnknownType:[],TSJSDocNullableType:["typeAnnotation"],TSJSDocNonNullableType:["typeAnnotation"],NeverTypeAnnotation:[],UndefinedTypeAnnotation:[],UnknownTypeAnnotation:[],AsConstExpression:["expression"],SatisfiesExpression:["expression","typeAnnotation"]};var P0=ke(he),Ae=P0;function Au(u,e){if(!(u!==null&&typeof u=="object"))return u;if(Array.isArray(u)){for(let t=0;t<u.length;t++)u[t]=Au(u[t],e);return u}let n=Ae(u);for(let t=0;t<n.length;t++)u[n[t]]=Au(u[n[t]],e);return e(u)||u}var Y2=Au;function E0(u,e){let{parser:n,text:t}=e;if(u.type==="File"&&u.program.interpreter){let{program:{interpreter:i},comments:o}=u;delete u.program.interpreter,o.unshift(i)}if(n==="babel"){let i=new Set;u=Y2(u,o=>{var l;(l=o.leadingComments)!=null&&l.some(ye)&&i.add($(o))}),u=Y2(u,o=>{if(o.type==="ParenthesizedExpression"){let{expression:l}=o;if(l.type==="TypeCastExpression")return l.range=[...o.range],l;let f=$(o);if(!i.has(f))return l.extra={...l.extra,parenthesized:!0},l}})}if(u=Y2(u,i=>{var o;switch(i.type){case"LogicalExpression":if(De(i))return Du(i);break;case"VariableDeclaration":{let l=yu(!1,i.declarations,-1);l!=null&&l.init&&t[p(l)]!==";"&&(i.range=[$(i),p(l)]);break}case"TSParenthesizedType":return i.typeAnnotation;case"TSTypeParameter":if(typeof i.name=="string"){let l=$(i);i.name={type:"Identifier",name:i.name,range:[l,l+i.name.length]}}break;case"TopicReference":u.extra={...u.extra,__isUsingHackPipeline:!0};break;case"ExportAllDeclaration":if(n==="meriyah"&&((o=i.exported)==null?void 0:o.type)==="Identifier"){let{exported:l}=i,f=t.slice($(l),p(l));(f.startsWith('"')||f.startsWith("'"))&&(i.exported={...i.exported,type:"Literal",value:i.exported.name,raw:f})}break;case"TSUnionType":case"TSIntersectionType":if(i.types.length===1)return i.types[0];break}}),me(u.comments)){let i=yu(!1,u.comments,-1);for(let o=u.comments.length-2;o>=0;o--){let l=u.comments[o];p(l)===$(i)&&T2(l)&&T2(i)&&ku(l)&&ku(i)&&(u.comments.splice(o+1,1),l.value+="*//*"+i.value,l.range=[$(l),p(i)]),i=l}}return u.type==="Program"&&(u.range=[0,t.length]),u}function De(u){return u.type==="LogicalExpression"&&u.right.type==="LogicalExpression"&&u.operator===u.right.operator}function Du(u){return De(u)?Du({type:"LogicalExpression",operator:u.operator,left:Du({type:"LogicalExpression",operator:u.operator,left:u.left,right:u.right.left,range:[$(u.left),p(u.right.left)]}),right:u.right.right,range:[$(u),p(u)]}):u}var be=E0;var C2=Me(ve(),1);function N0(u){if(!u.startsWith("#!"))return"";let e=u.indexOf(`
`);return e===-1?u:u.slice(0,e)}var Te=N0;function V0(u){let e=Te(u);e&&(u=u.slice(e.length+1));let n=(0,C2.extract)(u),{pragmas:t,comments:i}=(0,C2.parseWithComments)(n);return{shebang:e,text:u,pragmas:t,comments:i}}function Fe(u){let{pragmas:e}=V0(u);return Object.prototype.hasOwnProperty.call(e,"prettier")||Object.prototype.hasOwnProperty.call(e,"format")}function R0(u){return u=typeof u=="function"?{parse:u}:u,{astFormat:"estree",hasPragma:Fe,locStart:$,locEnd:p,...u}}var qe=R0;function O0(u){let{filepath:e}=u;if(e){if(e=e.toLowerCase(),e.endsWith(".cjs"))return"script";if(e.endsWith(".mjs"))return"module"}}var Le=O0;var U0={next:!0,ranges:!0,webcompat:!0,loc:!0,raw:!0,directives:!0,globalReturn:!0,impliedStrict:!1,preserveParens:!1,lexical:!1,identifierPattern:!1,jsx:!0,specDeviation:!0,uniqueKeyInPattern:!1};function M0(u,e){let n=[],t=[],i=de(u,{...U0,module:e==="module",onComment:n,onToken:t});return i.comments=n,i.tokens=t,i}function J0(u){var o;let{message:e,line:n,column:t}=u,i=(o=e.match(/^\[(?<line>\d+):(?<column>\d+)]: (?<message>.*)$/))==null?void 0:o.groups;return i&&(e=i.message,typeof n!="number"&&(n=Number(i.line),t=Number(i.column))),typeof n!="number"?u:se(e,{loc:{start:{line:n,column:t}},cause:u})}function j0(u,e={}){let n=Le(e),t=(n?[n]:["module","script"]).map(o=>()=>M0(u,o)),i;try{i=ae(t)}catch({errors:[o]}){throw J0(o)}return be(i,{parser:"meriyah",text:u})}var X0=qe(j0);return Je(z0);});