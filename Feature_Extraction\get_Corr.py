# -*- coding:utf-8 -*-
import numpy as np
from scipy.fftpack import fft
import matplotlib.pyplot as plt

from get_TransFunction import read_date


def FFT (Fs,data):
    # print("Fs:",Fs)
    L = len (data)                        # 信号长度
    N =int(np.power(2,np.ceil(np.log2(L))))    # 下一个最近二次幂
    FFT_y1 = np.abs(fft(data,N))/L*2      # N点FFT 变化,但除以信号长度
    Fre = np.arange(int(N/2))*Fs/N        # 频率坐标
    FFT_y1 = FFT_y1[range(int(N/2))]      # 取一半
    return Fre, FFT_y1

def getRangeFreqFFT(Fs,data):
    Fre, FFTy = FFT(Fs, data)
    FreRange = []
    FFTRange = []
    for i in range(len(Fre)):
        if 20 < Fre[i]:
            FreRange.append(Fre[i])
            FFTRange.append(FFTy[i])
    return FreRange, FFTRange


#对FFT分段计算相关性
def getSegCorr(data1, data2, frameRate):
    Fre1, dateLeft = getRangeFreqFFT(frameRate, data1)
    Fre2, dataRight = getRangeFreqFFT(frameRate, data2)
    # print("Fre2:", Fre2)
    lenSegment = 22 #分段长度,大约为10Hz
    overlap = 11 #重叠长度
    listCorr = []
    # print("len(FFT1_1):", len(FFT1_1))
    for i in range(int(len(dateLeft)/overlap)):  #总分段数目为len(dateLeft)/overlap
        startIndex = overlap*i  #标记分段开始下标
        endIndex = overlap*i + lenSegment  #标记分段结束下标
        if endIndex < len(dateLeft):
            leftSegment = dateLeft[startIndex:endIndex]
            rightSegment = dataRight[startIndex:endIndex]
            corr = np.corrcoef(leftSegment,rightSegment)[0,1] #计算皮尔逊相关性
            listCorr.append(corr)
        elif endIndex >= len(dateLeft): #最后一个分段
            leftSegment = dateLeft[startIndex:len(dateLeft)]
            rightSegment = dataRight[startIndex:len(dateLeft)]
            corr = np.corrcoef(leftSegment,rightSegment)[0,1] #计算皮尔逊相关性
            listCorr.append(corr)
            break #结束循环
    return listCorr


if __name__ == '__main__':

    frameRate = 467
    dataX, dataY, dataZ = read_date("L_1.txt")
    data1 = dataZ
    # current_axis = zeroMeanNorm(current_axis)

    dataX_, dataY_, dataZ_ = read_date("R_1.txt")
    data2 = dataZ_
    Fre1, FFT1 = getRangeFreqFFT(frameRate, data1)
    Fre2, FFT2 = getRangeFreqFFT(frameRate, data2)
    listCorr1_1 = getSegCorr(FFT1, FFT2, frameRate)

    print("Fre1:", Fre1)
    print("len(listCorr1_1):", len(listCorr1_1))

    index = [x for x in range(len(listCorr1_1))]

    plt.plot(index, listCorr1_1)

    plt.show()

