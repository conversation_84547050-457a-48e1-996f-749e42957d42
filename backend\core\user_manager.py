# -*- coding: utf-8 -*-
"""
用户管理模块
负责用户创建、文件管理、状态跟踪等功能
"""

import os
import json
import uuid
from datetime import datetime
from pathlib import Path
from werkzeug.utils import secure_filename

from ..utils.config import Config
from ..utils.logger import setup_logger

logger = setup_logger()

class UserManager:
    def __init__(self):
        self.users_file = Config.BASE_DIR / 'data' / 'users.json'
        self.users_data = self._load_users()
        
        # 确保必要目录存在
        Config.ensure_directories()
    
    def _load_users(self):
        """加载用户数据"""
        if self.users_file.exists():
            try:
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading users data: {e}")
                return {}
        return {}
    
    def _save_users(self):
        """保存用户数据"""
        try:
            # 确保目录存在
            os.makedirs(self.users_file.parent, exist_ok=True)
            
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(self.users_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving users data: {e}")
            raise
    
    def create_user(self):
        """创建新用户"""
        user_id = f"user-{uuid.uuid4().hex[:8]}"
        
        # 确保用户ID唯一
        while user_id in self.users_data:
            user_id = f"user-{uuid.uuid4().hex[:8]}"
        
        # 创建用户数据
        self.users_data[user_id] = {
            'id': user_id,
            'created_at': datetime.now().isoformat(),
            'registered': False,
            'model_path': None,
            'files': {},
            'registration_data': {}
        }
        
        # 创建用户目录
        user_dir = Config.UPLOAD_FOLDER / user_id
        os.makedirs(user_dir, exist_ok=True)
        
        self._save_users()
        logger.info(f"Created user: {user_id}")
        
        return user_id
    
    def user_exists(self, user_id):
        """检查用户是否存在"""
        return user_id in self.users_data
    
    def is_user_registered(self, user_id):
        """检查用户是否已完成注册"""
        if not self.user_exists(user_id):
            return False
        return self.users_data[user_id].get('registered', False)
    
    def save_user_file(self, user_id, data_type, file, filename):
        """保存用户上传的文件"""
        if not self.user_exists(user_id):
            raise ValueError(f"User {user_id} does not exist")
        
        # 创建用户目录
        user_dir = Config.UPLOAD_FOLDER / user_id
        os.makedirs(user_dir, exist_ok=True)
        
        # 生成安全的文件名
        safe_filename = secure_filename(filename)
        if not safe_filename:
            safe_filename = f"{data_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
        
        # 保存文件
        file_path = user_dir / safe_filename
        file.save(str(file_path))
        
        # 更新用户文件记录
        if 'files' not in self.users_data[user_id]:
            self.users_data[user_id]['files'] = {}
        
        self.users_data[user_id]['files'][data_type] = {
            'filename': safe_filename,
            'path': str(file_path),
            'uploaded_at': datetime.now().isoformat()
        }
        
        self._save_users()
        logger.info(f"Saved file for user {user_id}, type {data_type}: {safe_filename}")
        
        return str(file_path)
    
    def get_user_files(self, user_id):
        """获取用户的所有文件"""
        if not self.user_exists(user_id):
            return {}
        
        return self.users_data[user_id].get('files', {})
    
    def complete_registration(self, user_id, model_path):
        """完成用户注册"""
        if not self.user_exists(user_id):
            raise ValueError(f"User {user_id} does not exist")
        
        self.users_data[user_id]['registered'] = True
        self.users_data[user_id]['model_path'] = model_path
        self.users_data[user_id]['registered_at'] = datetime.now().isoformat()
        
        self._save_users()
        logger.info(f"Completed registration for user {user_id}")
    
    def get_user_model_path(self, user_id):
        """获取用户模型路径"""
        if not self.user_exists(user_id):
            return None
        
        return self.users_data[user_id].get('model_path')
    
    def get_user_info(self, user_id):
        """获取用户信息"""
        if not self.user_exists(user_id):
            return None
        
        return self.users_data[user_id].copy()
    
    def delete_user(self, user_id):
        """删除用户（谨慎使用）"""
        if not self.user_exists(user_id):
            return False
        
        # 删除用户文件目录
        user_dir = Config.UPLOAD_FOLDER / user_id
        if user_dir.exists():
            import shutil
            shutil.rmtree(user_dir)
        
        # 删除用户模型文件
        model_path = self.users_data[user_id].get('model_path')
        if model_path and os.path.exists(model_path):
            os.remove(model_path)
        
        # 从数据中删除用户
        del self.users_data[user_id]
        self._save_users()
        
        logger.info(f"Deleted user: {user_id}")
        return True
    
    def cleanup_old_files(self, days=30):
        """清理旧文件（可选功能）"""
        from datetime import timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days)
        users_to_delete = []
        
        for user_id, user_data in self.users_data.items():
            created_at = datetime.fromisoformat(user_data['created_at'])
            if created_at < cutoff_date and not user_data.get('registered', False):
                users_to_delete.append(user_id)
        
        for user_id in users_to_delete:
            self.delete_user(user_id)
            logger.info(f"Cleaned up old user: {user_id}")
        
        return len(users_to_delete)
