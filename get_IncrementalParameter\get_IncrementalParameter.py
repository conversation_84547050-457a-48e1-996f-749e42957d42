# -*- coding:utf-8 -*-

from OriginDataSet import getOriginDataset, GetSubset, getI<PERSON><PERSON>
from SiameseDatasets import IncrementalDataset, SiameseMNIST
import torch
from torch.utils.data import DataLoader

from losses import ContrastiveLoss
from networks import EmbeddingNet, SiameseNet
from torch.optim import lr_scheduler
import torch.optim as optim
import random
import numpy as np

from trainer import fit

if __name__ == '__main__':



    # # 创建预训练数据集
    # pre_trainDataset = getOriginDataset('/root/project/Paper2/dataset') 
    # # 构造预训练数据集
    # siamese_train_dataset = SiameseMNIST(pre_trainDataset) # Returns pairs of images and target same/different
    #
    # # 设置预训练数据集超参数[该参数经过grid search]
    # batch_size = 16
    # dropRate = 0.2
    # lr = 1e-3
    #
    # # 加载预训练数据集
    # cuda = torch.cuda.is_available()
    # kwargs = {'num_workers': 1, 'pin_memory': True} if cuda else {}
    # siamese_train_loader = DataLoader(siamese_train_dataset, batch_size=batch_size, shuffle=True, **kwargs)
    #
    # #初始化模型
    # margin = 1.
    # embedding_net = EmbeddingNet(dropRate)
    # model = SiameseNet(embedding_net)
    # if cuda:
    #     embedding_net.cuda()
    #     model.cuda()
    #
    # #设置损失函数 优化器
    # loss_fn = ContrastiveLoss(margin)
    # # loss_fn = TripletLoss(margin)
    # optimizer = optim.Adam(model.parameters(), lr=lr)
    # scheduler = lr_scheduler.StepLR(optimizer, 20, gamma=0.1, last_epoch=-1)
    #
    # n_epochs = 60 #原值是20
    # log_interval = 70
    #
    # # 这里没有指明metric，有关metric内容都没有输出
    # fit(siamese_train_loader, model, loss_fn, optimizer, scheduler, n_epochs, cuda, log_interval)



    # model = torch.load("model.pth")

    '''接下来要调增量学习数据集的参数'''

    # 设置超参数
    batchSize_list = [8, 16, 32, 64]
    lr_list = [0.001]
    n_epochs = 60


    for batchSize in batchSize_list:
        for lr in lr_list:
            trainLoss_list = []
            validLoss_list = []
            for i in range(1, 46):
                embedding_net = torch.load("embedding_net.pth")
                UserID = i
                # 构造默认数据集
                Defult_User = getOriginDataset('/root/project/Paper2/dataset', startUser = 46, endUser=61)
                # 构造用于预训练的数据集
                User = getOriginDataset('/root/project/Paper2/dataset', startUser = UserID, endUser = UserID+1)
                # 构造非法数据集
                Illegal_User = getIllegal('/root/project/Paper2/dataset', startUser=1, endUser=46, legalUser=UserID)

                # 训练、测试集划分
                train_indices = []
                test_indices = []
                indices = [j for j, label in enumerate(User.labels) if label == UserID]
                train_indices.extend(random.sample(indices, 100))  # 随机选择100个样本作为训练集
                test_indices.extend([index for index in indices if index not in train_indices])  # 其余样本作为测试集
                train_User = GetSubset(User, train_indices, train=True)
                test_User = GetSubset(User, test_indices, train=False)

                # 构造增量学习数据集
                IncrementalTrain = IncrementalDataset(train_User, Defult_User, UserID) # Returns pairs of images and target same/different
                # 构造测试数据集
                IncrementalTest = IncrementalDataset(test_User, Illegal_User, UserID)  # Returns pairs of images and target same/different


                # 加载增量学习数据集
                cuda = torch.cuda.is_available()
                kwargs = {'num_workers': 1, 'pin_memory': True} if cuda else {}
                IncrementalTrainset_loader = DataLoader(IncrementalTrain, batch_size=batchSize, shuffle=True, **kwargs)
                IncrementalTestset_loader = DataLoader(IncrementalTest, batch_size=batchSize, shuffle=True, **kwargs)
                #初始化模型
                margin = 1.
                model = SiameseNet(embedding_net)
                if cuda:
                    embedding_net.cuda()
                    model.cuda()

                # 设置损失函数 优化器
                loss_fn = ContrastiveLoss(margin)
                optimizer = optim.Adam(model.parameters(), lr=lr)
                scheduler = lr_scheduler.StepLR(optimizer, 20, gamma=0.1, last_epoch=-1)
                log_interval = 50
                # 训练
                trainloss, validloss = fit(IncrementalTrainset_loader, IncrementalTestset_loader, model, loss_fn, optimizer, scheduler, n_epochs, cuda, log_interval)

                trainLoss_list.append(trainloss)
                validLoss_list.append(validloss)


            trainloss = np.sum(trainLoss_list)/len(trainLoss_list)
            validloss = np.sum(validLoss_list) / len(validLoss_list)

            message1 = 'batchSize: {}, lr: {}.'.format(batchSize, lr)
            print(message1)
            # 打印总体训练的最低Loss
            min_trainLoss = min(trainLoss_list)
            min_trainIndex = trainLoss_list.index(min_trainLoss)
            min_validLoss = min(validLoss_list)
            min_validIndex = validLoss_list.index(min_validLoss)
            message2 = '\ntrainLoss: {}-{}. validLoss：{}-{}.'.format(min_trainIndex, min_trainLoss, min_validIndex, min_validLoss)
            print(message2)


        # '''单独加载用户训练数据集'''
        # temp_dataloader = DataLoader(train_User, batch_size=1)
        # # 计算template
        # output_list = []
        # with torch.no_grad():
        #     for data in temp_dataloader:
        #         sample, target = data
        #         sample = sample.cuda()
        #         output = embedding_net(sample)
        #         output_list.append(output)
        # concatenated = torch.cat(output_list, dim=0)
        # summed = torch.sum(concatenated, dim=0)
        # template = summed / len(output_list)
        # template = template.unsqueeze(0) # 扩张一个维度变成[1,32]
        #
        #
        # '''下面是测试过程'''
        # '''计算合法、非法样本的输出'''
        # Illegal_User = getIllegal('/root/project/Paper2/dataset', startUser=1, endUser=46, legalUser=UserID)
        # Illegal_dataloader = DataLoader(Illegal_User, batch_size=1)
        # legal_dataloader = DataLoader(test_User, batch_size=1)
        # embedding_net.eval()
        #
        # # 计算合法样本距离
        # leDistanceAll = 0
        # with torch.no_grad():
        #     for data in legal_dataloader:
        #         samples, targets = data
        #         samples = samples.cuda()
        #         targets = targets.cuda()
        #         outputs = embedding_net(samples)
        #         # 计算template和当前output的欧氏距离
        #         distance = torch.dist(template, outputs)
        #         leDistanceAll = leDistanceAll + np.abs(distance.item())
        # leDistance = leDistanceAll / len(test_User)
        #
        # # 计算非法样本距离
        # IllDistanceAll = 0
        # with torch.no_grad():
        #     for data in Illegal_dataloader:
        #         samples, targets = data
        #         samples = samples.cuda()
        #         targets = targets.cuda()
        #         outputs = embedding_net(samples)
        #         # 计算template和当前output的欧氏距离
        #         distance = torch.dist(template, outputs)
        #         IllDistanceAll = IllDistanceAll + np.abs(distance.item())
        # IllDistance = IllDistanceAll / len(Illegal_User)
        #
        # # 计算总的平均距离，值越大越好
        # subDistance = IllDistance - leDistance
        #
        # print(UserID, subDistance)
        #
        #
