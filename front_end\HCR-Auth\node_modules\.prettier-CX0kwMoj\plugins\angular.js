(function(n){function e(){var i=n();return i.default||i}if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var t=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};t.prettierPlugins=t.prettierPlugins||{},t.prettierPlugins.angular=e()}})(function(){"use strict";var Pe=Object.defineProperty;var Rt=Object.getOwnPropertyDescriptor;var Pt=Object.getOwnPropertyNames;var Lt=Object.prototype.hasOwnProperty;var ze=(r,e)=>{for(var t in e)Pe(r,t,{get:e[t],enumerable:!0})},Kt=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Pt(e))!Lt.call(r,n)&&n!==t&&Pe(r,n,{get:()=>e[n],enumerable:!(s=Rt(e,n))||s.enumerable});return r};var bt=r=>Kt(Pe({},"__esModule",{value:!0}),r);var Lr={};ze(Lr,{parsers:()=>Qe});var Qe={};ze(Qe,{__ng_action:()=>Ir,__ng_binding:()=>kr,__ng_directive:()=>Pr,__ng_interpolation:()=>Rr});var G=class{constructor(e,t,s,n){this.input=t,this.errLocation=s,this.ctxLocation=n,this.message=`Parser Error: ${e} ${s} [${t}] in ${n}`}},P=class{constructor(e,t){this.start=e,this.end=t}toAbsolute(e){return new I(e+this.start,e+this.end)}},w=class{constructor(e,t){this.span=e,this.sourceSpan=t}toString(){return"AST"}},W=class extends w{constructor(e,t,s){super(e,t),this.nameSpan=s}},$=class extends w{visit(e,t=null){}},L=class extends w{visit(e,t=null){return e.visitImplicitReceiver(this,t)}},Se=class extends L{visit(e,t=null){var s;return(s=e.visitThisReceiver)==null?void 0:s.call(e,this,t)}},V=class extends w{constructor(e,t,s){super(e,t),this.expressions=s}visit(e,t=null){return e.visitChain(this,t)}},Q=class extends w{constructor(e,t,s,n,i){super(e,t),this.condition=s,this.trueExp=n,this.falseExp=i}visit(e,t=null){return e.visitConditional(this,t)}},b=class extends W{constructor(e,t,s,n,i){super(e,t,s),this.receiver=n,this.name=i}visit(e,t=null){return e.visitPropertyRead(this,t)}},H=class extends W{constructor(e,t,s,n,i,a){super(e,t,s),this.receiver=n,this.name=i,this.value=a}visit(e,t=null){return e.visitPropertyWrite(this,t)}},z=class extends W{constructor(e,t,s,n,i){super(e,t,s),this.receiver=n,this.name=i}visit(e,t=null){return e.visitSafePropertyRead(this,t)}},q=class extends w{constructor(e,t,s,n){super(e,t),this.receiver=s,this.key=n}visit(e,t=null){return e.visitKeyedRead(this,t)}},j=class extends w{constructor(e,t,s,n){super(e,t),this.receiver=s,this.key=n}visit(e,t=null){return e.visitSafeKeyedRead(this,t)}},X=class extends w{constructor(e,t,s,n,i){super(e,t),this.receiver=s,this.key=n,this.value=i}visit(e,t=null){return e.visitKeyedWrite(this,t)}},Y=class extends W{constructor(e,t,s,n,i,a){super(e,t,a),this.exp=s,this.name=n,this.args=i}visit(e,t=null){return e.visitPipe(this,t)}},C=class extends w{constructor(e,t,s){super(e,t),this.value=s}visit(e,t=null){return e.visitLiteralPrimitive(this,t)}},Z=class extends w{constructor(e,t,s){super(e,t),this.expressions=s}visit(e,t=null){return e.visitLiteralArray(this,t)}},J=class extends w{constructor(e,t,s,n){super(e,t),this.keys=s,this.values=n}visit(e,t=null){return e.visitLiteralMap(this,t)}},me=class extends w{constructor(e,t,s,n){super(e,t),this.strings=s,this.expressions=n}visit(e,t=null){return e.visitInterpolation(this,t)}},A=class extends w{constructor(e,t,s,n,i){super(e,t),this.operation=s,this.left=n,this.right=i}visit(e,t=null){return e.visitBinary(this,t)}},B=class r extends A{static createMinus(e,t,s){return new r(e,t,"-",s,"-",new C(e,t,0),s)}static createPlus(e,t,s){return new r(e,t,"+",s,"-",s,new C(e,t,0))}constructor(e,t,s,n,i,a,h){super(e,t,i,a,h),this.operator=s,this.expr=n,this.left=null,this.right=null,this.operation=null}visit(e,t=null){return e.visitUnary!==void 0?e.visitUnary(this,t):e.visitBinary(this,t)}},ee=class extends w{constructor(e,t,s){super(e,t),this.expression=s}visit(e,t=null){return e.visitPrefixNot(this,t)}},te=class extends w{constructor(e,t,s){super(e,t),this.expression=s}visit(e,t=null){return e.visitNonNullAssert(this,t)}},re=class extends w{constructor(e,t,s,n,i){super(e,t),this.receiver=s,this.args=n,this.argumentSpan=i}visit(e,t=null){return e.visitCall(this,t)}},se=class extends w{constructor(e,t,s,n,i){super(e,t),this.receiver=s,this.args=n,this.argumentSpan=i}visit(e,t=null){return e.visitSafeCall(this,t)}},I=class{constructor(e,t){this.start=e,this.end=t}},R=class extends w{constructor(e,t,s,n,i){super(new P(0,t===null?0:t.length),new I(n,t===null?n:n+t.length)),this.ast=e,this.source=t,this.location=s,this.errors=i}visit(e,t=null){return e.visitASTWithSource?e.visitASTWithSource(this,t):this.ast.visit(e,t)}toString(){return`${this.source} in ${this.location}`}},T=class{constructor(e,t,s){this.sourceSpan=e,this.key=t,this.value=s}},ne=class{constructor(e,t,s){this.sourceSpan=e,this.key=t,this.value=s}},we=class{visit(e,t){e.visit(this,t)}visitUnary(e,t){this.visit(e.expr,t)}visitBinary(e,t){this.visit(e.left,t),this.visit(e.right,t)}visitChain(e,t){this.visitAll(e.expressions,t)}visitConditional(e,t){this.visit(e.condition,t),this.visit(e.trueExp,t),this.visit(e.falseExp,t)}visitPipe(e,t){this.visit(e.exp,t),this.visitAll(e.args,t)}visitImplicitReceiver(e,t){}visitThisReceiver(e,t){}visitInterpolation(e,t){this.visitAll(e.expressions,t)}visitKeyedRead(e,t){this.visit(e.receiver,t),this.visit(e.key,t)}visitKeyedWrite(e,t){this.visit(e.receiver,t),this.visit(e.key,t),this.visit(e.value,t)}visitLiteralArray(e,t){this.visitAll(e.expressions,t)}visitLiteralMap(e,t){this.visitAll(e.values,t)}visitLiteralPrimitive(e,t){}visitPrefixNot(e,t){this.visit(e.expression,t)}visitNonNullAssert(e,t){this.visit(e.expression,t)}visitPropertyRead(e,t){this.visit(e.receiver,t)}visitPropertyWrite(e,t){this.visit(e.receiver,t),this.visit(e.value,t)}visitSafePropertyRead(e,t){this.visit(e.receiver,t)}visitSafeKeyedRead(e,t){this.visit(e.receiver,t),this.visit(e.key,t)}visitCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitSafeCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitAll(e,t){for(let s of e)this.visit(s,t)}};var qe;(function(r){r[r.DEFAULT=0]="DEFAULT",r[r.LITERAL_ATTR=1]="LITERAL_ATTR",r[r.ANIMATION=2]="ANIMATION",r[r.TWO_WAY=3]="TWO_WAY"})(qe||(qe={}));function je(r){return r>=9&&r<=32||r==160}function K(r){return 48<=r&&r<=57}function Xe(r){return r>=97&&r<=122||r>=65&&r<=90}function Le(r){return r===39||r===34||r===96}var f;(function(r){r[r.Character=0]="Character",r[r.Identifier=1]="Identifier",r[r.PrivateIdentifier=2]="PrivateIdentifier",r[r.Keyword=3]="Keyword",r[r.String=4]="String",r[r.Operator=5]="Operator",r[r.Number=6]="Number",r[r.Error=7]="Error"})(f||(f={}));var pr=["var","let","as","null","undefined","true","false","if","else","this"],le=class{tokenize(e){let t=new Be(e),s=[],n=t.scanToken();for(;n!=null;)s.push(n),n=t.scanToken();return s}},O=class{constructor(e,t,s,n,i){this.index=e,this.end=t,this.type=s,this.numValue=n,this.strValue=i}isCharacter(e){return this.type==f.Character&&this.numValue==e}isNumber(){return this.type==f.Number}isString(){return this.type==f.String}isOperator(e){return this.type==f.Operator&&this.strValue==e}isIdentifier(){return this.type==f.Identifier}isPrivateIdentifier(){return this.type==f.PrivateIdentifier}isKeyword(){return this.type==f.Keyword}isKeywordLet(){return this.type==f.Keyword&&this.strValue=="let"}isKeywordAs(){return this.type==f.Keyword&&this.strValue=="as"}isKeywordNull(){return this.type==f.Keyword&&this.strValue=="null"}isKeywordUndefined(){return this.type==f.Keyword&&this.strValue=="undefined"}isKeywordTrue(){return this.type==f.Keyword&&this.strValue=="true"}isKeywordFalse(){return this.type==f.Keyword&&this.strValue=="false"}isKeywordThis(){return this.type==f.Keyword&&this.strValue=="this"}isError(){return this.type==f.Error}toNumber(){return this.type==f.Number?this.numValue:-1}toString(){switch(this.type){case f.Character:case f.Identifier:case f.Keyword:case f.Operator:case f.PrivateIdentifier:case f.String:case f.Error:return this.strValue;case f.Number:return this.numValue.toString();default:return null}}};function tt(r,e,t){return new O(r,e,f.Character,t,String.fromCharCode(t))}function ur(r,e,t){return new O(r,e,f.Identifier,0,t)}function lr(r,e,t){return new O(r,e,f.PrivateIdentifier,0,t)}function xr(r,e,t){return new O(r,e,f.Keyword,0,t)}function be(r,e,t){return new O(r,e,f.Operator,0,t)}function dr(r,e,t){return new O(r,e,f.String,0,t)}function fr(r,e,t){return new O(r,e,f.Number,t,"")}function vr(r,e,t){return new O(r,e,f.Error,0,t)}var Ce=new O(-1,-1,f.Character,0,""),Be=class{constructor(e){this.input=e,this.peek=0,this.index=-1,this.length=e.length,this.advance()}advance(){this.peek=++this.index>=this.length?0:this.input.charCodeAt(this.index)}scanToken(){let e=this.input,t=this.length,s=this.peek,n=this.index;for(;s<=32;)if(++n>=t){s=0;break}else s=e.charCodeAt(n);if(this.peek=s,this.index=n,n>=t)return null;if(rt(s))return this.scanIdentifier();if(K(s))return this.scanNumber(n);let i=n;switch(s){case 46:return this.advance(),K(this.peek)?this.scanNumber(i):tt(i,this.index,46);case 40:case 41:case 123:case 125:case 91:case 93:case 44:case 58:case 59:return this.scanCharacter(i,s);case 39:case 34:return this.scanString();case 35:return this.scanPrivateIdentifier();case 43:case 45:case 42:case 47:case 37:case 94:return this.scanOperator(i,String.fromCharCode(s));case 63:return this.scanQuestion(i);case 60:case 62:return this.scanComplexOperator(i,String.fromCharCode(s),61,"=");case 33:case 61:return this.scanComplexOperator(i,String.fromCharCode(s),61,"=",61,"=");case 38:return this.scanComplexOperator(i,"&",38,"&");case 124:return this.scanComplexOperator(i,"|",124,"|");case 160:for(;je(this.peek);)this.advance();return this.scanToken()}return this.advance(),this.error(`Unexpected character [${String.fromCharCode(s)}]`,0)}scanCharacter(e,t){return this.advance(),tt(e,this.index,t)}scanOperator(e,t){return this.advance(),be(e,this.index,t)}scanComplexOperator(e,t,s,n,i,a){this.advance();let h=t;return this.peek==s&&(this.advance(),h+=n),i!=null&&this.peek==i&&(this.advance(),h+=a),be(e,this.index,h)}scanIdentifier(){let e=this.index;for(this.advance();st(this.peek);)this.advance();let t=this.input.substring(e,this.index);return pr.indexOf(t)>-1?xr(e,this.index,t):ur(e,this.index,t)}scanPrivateIdentifier(){let e=this.index;if(this.advance(),!rt(this.peek))return this.error("Invalid character [#]",-1);for(;st(this.peek);)this.advance();let t=this.input.substring(e,this.index);return lr(e,this.index,t)}scanNumber(e){let t=this.index===e,s=!1;for(this.advance();;){if(!K(this.peek))if(this.peek===95){if(!K(this.input.charCodeAt(this.index-1))||!K(this.input.charCodeAt(this.index+1)))return this.error("Invalid numeric separator",0);s=!0}else if(this.peek===46)t=!1;else if(gr(this.peek)){if(this.advance(),Sr(this.peek)&&this.advance(),!K(this.peek))return this.error("Invalid exponent",-1);t=!1}else break;this.advance()}let n=this.input.substring(e,this.index);s&&(n=n.replace(/_/g,""));let i=t?wr(n):parseFloat(n);return fr(e,this.index,i)}scanString(){let e=this.index,t=this.peek;this.advance();let s="",n=this.index,i=this.input;for(;this.peek!=t;)if(this.peek==92){s+=i.substring(n,this.index);let h;if(this.advance(),this.peek==117){let g=i.substring(this.index+1,this.index+5);if(/^[0-9a-f]+$/i.test(g))h=parseInt(g,16);else return this.error(`Invalid unicode escape [\\u${g}]`,0);for(let d=0;d<5;d++)this.advance()}else h=mr(this.peek),this.advance();s+=String.fromCharCode(h),n=this.index}else{if(this.peek==0)return this.error("Unterminated quote",0);this.advance()}let a=i.substring(n,this.index);return this.advance(),dr(e,this.index,s+a)}scanQuestion(e){this.advance();let t="?";return(this.peek===63||this.peek===46)&&(t+=this.peek===46?".":"?",this.advance()),be(e,this.index,t)}error(e,t){let s=this.index+t;return vr(s,this.index,`Lexer Error: ${e} at column ${s} in expression [${this.input}]`)}};function rt(r){return 97<=r&&r<=122||65<=r&&r<=90||r==95||r==36}function st(r){return Xe(r)||K(r)||r==95||r==36}function gr(r){return r==101||r==69}function Sr(r){return r==45||r==43}function mr(r){switch(r){case 110:return 10;case 102:return 12;case 114:return 13;case 116:return 9;case 118:return 11;default:return r}}function wr(r){let e=parseInt(r);if(isNaN(e))throw new Error("Invalid integer literal when parsing "+r);return e}var yr=[/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];function ot(r,e){if(e!=null&&!(Array.isArray(e)&&e.length==2))throw new Error(`Expected '${r}' to be an array, [start, end].`);if(e!=null){let t=e[0],s=e[1];yr.forEach(n=>{if(n.test(t)||n.test(s))throw new Error(`['${t}', '${s}'] contains unusable interpolation symbol.`)})}}var _e=class r{static fromArray(e){return e?(ot("interpolation",e),new r(e[0],e[1])):F}constructor(e,t){this.start=e,this.end=t}},F=new _e("{{","}}");var Me=class{constructor(e,t,s){this.strings=e,this.expressions=t,this.offsets=s}},Fe=class{constructor(e,t,s){this.templateBindings=e,this.warnings=t,this.errors=s}},fe=class{constructor(e){this._lexer=e,this.errors=[]}parseAction(e,t,s,n,i=F){this._checkNoInterpolation(e,s,i);let a=this._stripComments(e),h=this._lexer.tokenize(a),g=1;t&&(g|=2);let d=new U(e,s,n,h,g,this.errors,0).parseChain();return new R(d,e,s,n,this.errors)}parseBinding(e,t,s,n=F){let i=this._parseBindingAst(e,t,s,n);return new R(i,e,t,s,this.errors)}checkSimpleExpression(e){let t=new Ue;return e.visit(t),t.errors}parseSimpleBinding(e,t,s,n=F){let i=this._parseBindingAst(e,t,s,n),a=this.checkSimpleExpression(i);return a.length>0&&this._reportError(`Host binding expression cannot contain ${a.join(" ")}`,e,t),new R(i,e,t,s,this.errors)}_reportError(e,t,s,n){this.errors.push(new G(e,t,s,n))}_parseBindingAst(e,t,s,n){this._checkNoInterpolation(e,t,n);let i=this._stripComments(e),a=this._lexer.tokenize(i);return new U(e,t,s,a,0,this.errors,0).parseChain()}parseTemplateBindings(e,t,s,n,i){let a=this._lexer.tokenize(t);return new U(t,s,i,a,0,this.errors,0).parseTemplateBindings({source:e,span:new I(n,n+e.length)})}parseInterpolation(e,t,s,n,i=F){let{strings:a,expressions:h,offsets:g}=this.splitInterpolation(e,t,n,i);if(h.length===0)return null;let d=[];for(let y=0;y<h.length;++y){let E=h[y].text,S=this._stripComments(E),o=this._lexer.tokenize(S),u=new U(e,t,s,o,0,this.errors,g[y]).parseChain();d.push(u)}return this.createInterpolationAst(a.map(y=>y.text),d,e,t,s)}parseInterpolationExpression(e,t,s){let n=this._stripComments(e),i=this._lexer.tokenize(n),a=new U(e,t,s,i,0,this.errors,0).parseChain(),h=["",""];return this.createInterpolationAst(h,[a],e,t,s)}createInterpolationAst(e,t,s,n,i){let a=new P(0,s.length),h=new me(a,a.toAbsolute(i),e,t);return new R(h,s,n,i,this.errors)}splitInterpolation(e,t,s,n=F){let i=[],a=[],h=[],g=s?Er(s):null,d=0,y=!1,E=!1,{start:S,end:o}=n;for(;d<e.length;)if(y){let u=d,v=u+S.length,c=this._getInterpolationEndIndex(e,o,v);if(c===-1){y=!1,E=!0;break}let x=c+o.length,l=e.substring(v,c);l.trim().length===0&&this._reportError("Blank expressions are not allowed in interpolated strings",e,`at column ${d} in`,t),a.push({text:l,start:u,end:x});let k=((g==null?void 0:g.get(u))??u)+S.length;h.push(k),d=x,y=!1}else{let u=d;d=e.indexOf(S,d),d===-1&&(d=e.length);let v=e.substring(u,d);i.push({text:v,start:u,end:d}),y=!0}if(!y)if(E){let u=i[i.length-1];u.text+=e.substring(d),u.end=e.length}else i.push({text:e.substring(d),start:d,end:e.length});return new Me(i,a,h)}wrapLiteralPrimitive(e,t,s){let n=new P(0,e==null?0:e.length);return new R(new C(n,n.toAbsolute(s),e),e,t,s,this.errors)}_stripComments(e){let t=this._commentStart(e);return t!=null?e.substring(0,t):e}_commentStart(e){let t=null;for(let s=0;s<e.length-1;s++){let n=e.charCodeAt(s),i=e.charCodeAt(s+1);if(n===47&&i==47&&t==null)return s;t===n?t=null:t==null&&Le(n)&&(t=n)}return null}_checkNoInterpolation(e,t,{start:s,end:n}){let i=-1,a=-1;for(let h of this._forEachUnquotedChar(e,0))if(i===-1)e.startsWith(s)&&(i=h);else if(a=this._getInterpolationEndIndex(e,n,h),a>-1)break;i>-1&&a>-1&&this._reportError(`Got interpolation (${s}${n}) where expression was expected`,e,`at column ${i} in`,t)}_getInterpolationEndIndex(e,t,s){for(let n of this._forEachUnquotedChar(e,s)){if(e.startsWith(t,n))return n;if(e.startsWith("//",n))return e.indexOf(t,n)}return-1}*_forEachUnquotedChar(e,t){let s=null,n=0;for(let i=t;i<e.length;i++){let a=e[i];Le(e.charCodeAt(i))&&(s===null||s===a)&&n%2===0?s=s===null?a:null:s===null&&(yield i),n=a==="\\"?n+1:0}}},pe;(function(r){r[r.None=0]="None",r[r.Writable=1]="Writable"})(pe||(pe={}));var U=class{constructor(e,t,s,n,i,a,h){this.input=e,this.location=t,this.absoluteOffset=s,this.tokens=n,this.parseFlags=i,this.errors=a,this.offset=h,this.rparensExpected=0,this.rbracketsExpected=0,this.rbracesExpected=0,this.context=pe.None,this.sourceSpanCache=new Map,this.index=0}peek(e){let t=this.index+e;return t<this.tokens.length?this.tokens[t]:Ce}get next(){return this.peek(0)}get atEOF(){return this.index>=this.tokens.length}get inputIndex(){return this.atEOF?this.currentEndIndex:this.next.index+this.offset}get currentEndIndex(){return this.index>0?this.peek(-1).end+this.offset:this.tokens.length===0?this.input.length+this.offset:this.next.index+this.offset}get currentAbsoluteOffset(){return this.absoluteOffset+this.inputIndex}span(e,t){let s=this.currentEndIndex;if(t!==void 0&&t>this.currentEndIndex&&(s=t),e>s){let n=s;s=e,e=n}return new P(e,s)}sourceSpan(e,t){let s=`${e}@${this.inputIndex}:${t}`;return this.sourceSpanCache.has(s)||this.sourceSpanCache.set(s,this.span(e,t).toAbsolute(this.absoluteOffset)),this.sourceSpanCache.get(s)}advance(){this.index++}withContext(e,t){this.context|=e;let s=t();return this.context^=e,s}consumeOptionalCharacter(e){return this.next.isCharacter(e)?(this.advance(),!0):!1}peekKeywordLet(){return this.next.isKeywordLet()}peekKeywordAs(){return this.next.isKeywordAs()}expectCharacter(e){this.consumeOptionalCharacter(e)||this.error(`Missing expected ${String.fromCharCode(e)}`)}consumeOptionalOperator(e){return this.next.isOperator(e)?(this.advance(),!0):!1}expectOperator(e){this.consumeOptionalOperator(e)||this.error(`Missing expected operator ${e}`)}prettyPrintToken(e){return e===Ce?"end of input":`token ${e}`}expectIdentifierOrKeyword(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier or keyword"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier or keyword`),null):(this.advance(),e.toString())}expectIdentifierOrKeywordOrString(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()&&!e.isString()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier, keyword or string"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier, keyword, or string`),""):(this.advance(),e.toString())}parseChain(){let e=[],t=this.inputIndex;for(;this.index<this.tokens.length;){let s=this.parsePipe();if(e.push(s),this.consumeOptionalCharacter(59))for(this.parseFlags&1||this.error("Binding expression cannot contain chained expression");this.consumeOptionalCharacter(59););else if(this.index<this.tokens.length){let n=this.index;if(this.error(`Unexpected token '${this.next}'`),this.index===n)break}}if(e.length===0){let s=this.offset,n=this.offset+this.input.length;return new $(this.span(s,n),this.sourceSpan(s,n))}return e.length==1?e[0]:new V(this.span(t),this.sourceSpan(t),e)}parsePipe(){let e=this.inputIndex,t=this.parseExpression();if(this.consumeOptionalOperator("|")){this.parseFlags&1&&this.error("Cannot have a pipe in an action expression");do{let s=this.inputIndex,n=this.expectIdentifierOrKeyword(),i,a;n!==null?i=this.sourceSpan(s):(n="",a=this.next.index!==-1?this.next.index:this.input.length+this.offset,i=new P(a,a).toAbsolute(this.absoluteOffset));let h=[];for(;this.consumeOptionalCharacter(58);)h.push(this.parseExpression());t=new Y(this.span(e),this.sourceSpan(e,a),t,n,h,i)}while(this.consumeOptionalOperator("|"))}return t}parseExpression(){return this.parseConditional()}parseConditional(){let e=this.inputIndex,t=this.parseLogicalOr();if(this.consumeOptionalOperator("?")){let s=this.parsePipe(),n;if(this.consumeOptionalCharacter(58))n=this.parsePipe();else{let i=this.inputIndex,a=this.input.substring(e,i);this.error(`Conditional expression ${a} requires all 3 expressions`),n=new $(this.span(e),this.sourceSpan(e))}return new Q(this.span(e),this.sourceSpan(e),t,s,n)}else return t}parseLogicalOr(){let e=this.inputIndex,t=this.parseLogicalAnd();for(;this.consumeOptionalOperator("||");){let s=this.parseLogicalAnd();t=new A(this.span(e),this.sourceSpan(e),"||",t,s)}return t}parseLogicalAnd(){let e=this.inputIndex,t=this.parseNullishCoalescing();for(;this.consumeOptionalOperator("&&");){let s=this.parseNullishCoalescing();t=new A(this.span(e),this.sourceSpan(e),"&&",t,s)}return t}parseNullishCoalescing(){let e=this.inputIndex,t=this.parseEquality();for(;this.consumeOptionalOperator("??");){let s=this.parseEquality();t=new A(this.span(e),this.sourceSpan(e),"??",t,s)}return t}parseEquality(){let e=this.inputIndex,t=this.parseRelational();for(;this.next.type==f.Operator;){let s=this.next.strValue;switch(s){case"==":case"===":case"!=":case"!==":this.advance();let n=this.parseRelational();t=new A(this.span(e),this.sourceSpan(e),s,t,n);continue}break}return t}parseRelational(){let e=this.inputIndex,t=this.parseAdditive();for(;this.next.type==f.Operator;){let s=this.next.strValue;switch(s){case"<":case">":case"<=":case">=":this.advance();let n=this.parseAdditive();t=new A(this.span(e),this.sourceSpan(e),s,t,n);continue}break}return t}parseAdditive(){let e=this.inputIndex,t=this.parseMultiplicative();for(;this.next.type==f.Operator;){let s=this.next.strValue;switch(s){case"+":case"-":this.advance();let n=this.parseMultiplicative();t=new A(this.span(e),this.sourceSpan(e),s,t,n);continue}break}return t}parseMultiplicative(){let e=this.inputIndex,t=this.parsePrefix();for(;this.next.type==f.Operator;){let s=this.next.strValue;switch(s){case"*":case"%":case"/":this.advance();let n=this.parsePrefix();t=new A(this.span(e),this.sourceSpan(e),s,t,n);continue}break}return t}parsePrefix(){if(this.next.type==f.Operator){let e=this.inputIndex,t=this.next.strValue,s;switch(t){case"+":return this.advance(),s=this.parsePrefix(),B.createPlus(this.span(e),this.sourceSpan(e),s);case"-":return this.advance(),s=this.parsePrefix(),B.createMinus(this.span(e),this.sourceSpan(e),s);case"!":return this.advance(),s=this.parsePrefix(),new ee(this.span(e),this.sourceSpan(e),s)}}return this.parseCallChain()}parseCallChain(){let e=this.inputIndex,t=this.parsePrimary();for(;;)if(this.consumeOptionalCharacter(46))t=this.parseAccessMember(t,e,!1);else if(this.consumeOptionalOperator("?."))this.consumeOptionalCharacter(40)?t=this.parseCall(t,e,!0):t=this.consumeOptionalCharacter(91)?this.parseKeyedReadOrWrite(t,e,!0):this.parseAccessMember(t,e,!0);else if(this.consumeOptionalCharacter(91))t=this.parseKeyedReadOrWrite(t,e,!1);else if(this.consumeOptionalCharacter(40))t=this.parseCall(t,e,!1);else if(this.consumeOptionalOperator("!"))t=new te(this.span(e),this.sourceSpan(e),t);else return t}parsePrimary(){let e=this.inputIndex;if(this.consumeOptionalCharacter(40)){this.rparensExpected++;let t=this.parsePipe();return this.rparensExpected--,this.expectCharacter(41),t}else{if(this.next.isKeywordNull())return this.advance(),new C(this.span(e),this.sourceSpan(e),null);if(this.next.isKeywordUndefined())return this.advance(),new C(this.span(e),this.sourceSpan(e),void 0);if(this.next.isKeywordTrue())return this.advance(),new C(this.span(e),this.sourceSpan(e),!0);if(this.next.isKeywordFalse())return this.advance(),new C(this.span(e),this.sourceSpan(e),!1);if(this.next.isKeywordThis())return this.advance(),new Se(this.span(e),this.sourceSpan(e));if(this.consumeOptionalCharacter(91)){this.rbracketsExpected++;let t=this.parseExpressionList(93);return this.rbracketsExpected--,this.expectCharacter(93),new Z(this.span(e),this.sourceSpan(e),t)}else{if(this.next.isCharacter(123))return this.parseLiteralMap();if(this.next.isIdentifier())return this.parseAccessMember(new L(this.span(e),this.sourceSpan(e)),e,!1);if(this.next.isNumber()){let t=this.next.toNumber();return this.advance(),new C(this.span(e),this.sourceSpan(e),t)}else if(this.next.isString()){let t=this.next.toString();return this.advance(),new C(this.span(e),this.sourceSpan(e),t)}else return this.next.isPrivateIdentifier()?(this._reportErrorForPrivateIdentifier(this.next,null),new $(this.span(e),this.sourceSpan(e))):this.index>=this.tokens.length?(this.error(`Unexpected end of expression: ${this.input}`),new $(this.span(e),this.sourceSpan(e))):(this.error(`Unexpected token ${this.next}`),new $(this.span(e),this.sourceSpan(e)))}}}parseExpressionList(e){let t=[];do if(!this.next.isCharacter(e))t.push(this.parsePipe());else break;while(this.consumeOptionalCharacter(44));return t}parseLiteralMap(){let e=[],t=[],s=this.inputIndex;if(this.expectCharacter(123),!this.consumeOptionalCharacter(125)){this.rbracesExpected++;do{let n=this.inputIndex,i=this.next.isString(),a=this.expectIdentifierOrKeywordOrString();if(e.push({key:a,quoted:i}),i)this.expectCharacter(58),t.push(this.parsePipe());else if(this.consumeOptionalCharacter(58))t.push(this.parsePipe());else{let h=this.span(n),g=this.sourceSpan(n);t.push(new b(h,g,g,new L(h,g),a))}}while(this.consumeOptionalCharacter(44)&&!this.next.isCharacter(125));this.rbracesExpected--,this.expectCharacter(125)}return new J(this.span(s),this.sourceSpan(s),e,t)}parseAccessMember(e,t,s){let n=this.inputIndex,i=this.withContext(pe.Writable,()=>{let g=this.expectIdentifierOrKeyword()??"";return g.length===0&&this.error("Expected identifier for property access",e.span.end),g}),a=this.sourceSpan(n),h;if(s)this.consumeOptionalAssignment()?(this.error("The '?.' operator cannot be used in the assignment"),h=new $(this.span(t),this.sourceSpan(t))):h=new z(this.span(t),this.sourceSpan(t),a,e,i);else if(this.consumeOptionalAssignment()){if(!(this.parseFlags&1))return this.error("Bindings cannot contain assignments"),new $(this.span(t),this.sourceSpan(t));let g=this.parseConditional();h=new H(this.span(t),this.sourceSpan(t),a,e,i,g)}else h=new b(this.span(t),this.sourceSpan(t),a,e,i);return h}parseCall(e,t,s){let n=this.inputIndex;this.rparensExpected++;let i=this.parseCallArguments(),a=this.span(n,this.inputIndex).toAbsolute(this.absoluteOffset);this.expectCharacter(41),this.rparensExpected--;let h=this.span(t),g=this.sourceSpan(t);return s?new se(h,g,e,i,a):new re(h,g,e,i,a)}consumeOptionalAssignment(){return this.parseFlags&2&&this.next.isOperator("!")&&this.peek(1).isOperator("=")?(this.advance(),this.advance(),!0):this.consumeOptionalOperator("=")}parseCallArguments(){if(this.next.isCharacter(41))return[];let e=[];do e.push(this.parsePipe());while(this.consumeOptionalCharacter(44));return e}expectTemplateBindingKey(){let e="",t=!1,s=this.currentAbsoluteOffset;do e+=this.expectIdentifierOrKeywordOrString(),t=this.consumeOptionalOperator("-"),t&&(e+="-");while(t);return{source:e,span:new I(s,s+e.length)}}parseTemplateBindings(e){let t=[];for(t.push(...this.parseDirectiveKeywordBindings(e));this.index<this.tokens.length;){let s=this.parseLetBinding();if(s)t.push(s);else{let n=this.expectTemplateBindingKey(),i=this.parseAsBinding(n);i?t.push(i):(n.source=e.source+n.source.charAt(0).toUpperCase()+n.source.substring(1),t.push(...this.parseDirectiveKeywordBindings(n)))}this.consumeStatementTerminator()}return new Fe(t,[],this.errors)}parseKeyedReadOrWrite(e,t,s){return this.withContext(pe.Writable,()=>{this.rbracketsExpected++;let n=this.parsePipe();if(n instanceof $&&this.error("Key access cannot be empty"),this.rbracketsExpected--,this.expectCharacter(93),this.consumeOptionalOperator("="))if(s)this.error("The '?.' operator cannot be used in the assignment");else{let i=this.parseConditional();return new X(this.span(t),this.sourceSpan(t),e,n,i)}else return s?new j(this.span(t),this.sourceSpan(t),e,n):new q(this.span(t),this.sourceSpan(t),e,n);return new $(this.span(t),this.sourceSpan(t))})}parseDirectiveKeywordBindings(e){let t=[];this.consumeOptionalCharacter(58);let s=this.getDirectiveBoundTarget(),n=this.currentAbsoluteOffset,i=this.parseAsBinding(e);i||(this.consumeStatementTerminator(),n=this.currentAbsoluteOffset);let a=new I(e.span.start,n);return t.push(new ne(a,e,s)),i&&t.push(i),t}getDirectiveBoundTarget(){if(this.next===Ce||this.peekKeywordAs()||this.peekKeywordLet())return null;let e=this.parsePipe(),{start:t,end:s}=e.span,n=this.input.substring(t,s);return new R(e,n,this.location,this.absoluteOffset+t,this.errors)}parseAsBinding(e){if(!this.peekKeywordAs())return null;this.advance();let t=this.expectTemplateBindingKey();this.consumeStatementTerminator();let s=new I(e.span.start,this.currentAbsoluteOffset);return new T(s,t,e)}parseLetBinding(){if(!this.peekKeywordLet())return null;let e=this.currentAbsoluteOffset;this.advance();let t=this.expectTemplateBindingKey(),s=null;this.consumeOptionalOperator("=")&&(s=this.expectTemplateBindingKey()),this.consumeStatementTerminator();let n=new I(e,this.currentAbsoluteOffset);return new T(n,t,s)}consumeStatementTerminator(){this.consumeOptionalCharacter(59)||this.consumeOptionalCharacter(44)}error(e,t=null){this.errors.push(new G(e,this.input,this.locationText(t),this.location)),this.skip()}locationText(e=null){return e==null&&(e=this.index),e<this.tokens.length?`at column ${this.tokens[e].index+1} in`:"at the end of the expression"}_reportErrorForPrivateIdentifier(e,t){let s=`Private identifiers are not supported. Unexpected private identifier: ${e}`;t!==null&&(s+=`, ${t}`),this.error(s)}skip(){let e=this.next;for(;this.index<this.tokens.length&&!e.isCharacter(59)&&!e.isOperator("|")&&(this.rparensExpected<=0||!e.isCharacter(41))&&(this.rbracesExpected<=0||!e.isCharacter(125))&&(this.rbracketsExpected<=0||!e.isCharacter(93))&&(!(this.context&pe.Writable)||!e.isOperator("="));)this.next.isError()&&this.errors.push(new G(this.next.toString(),this.input,this.locationText(),this.location)),this.advance(),e=this.next}},Ue=class extends we{constructor(){super(...arguments),this.errors=[]}visitPipe(){this.errors.push("pipes")}};function Er(r){let e=new Map,t=0,s=0,n=0;for(;n<r.length;){let i=r[n];if(i.type===9){let[a,h]=i.parts;t+=h.length,s+=a.length}else{let a=i.parts.reduce((h,g)=>h+g.length,0);s+=a,t+=a}e.set(s,t),n++}return e}var ht="angular-estree-parser",Ge="NgEstreeParser",De=0,Ae=[ht,De];function pt(){return new fe(new le)}function Oe(r,e){let t=pt(),{astInput:s,comments:n}=Cr(r,t),{ast:i,errors:a}=e(s,t);return vt(a),{ast:i,comments:n}}function ut(r){return Oe(r,(e,t)=>t.parseBinding(e,...Ae))}function lt(r){return Oe(r,(e,t)=>t.parseSimpleBinding(e,...Ae))}function xt(r){return Oe(r,(e,t)=>t.parseAction(e,!1,...Ae))}function dt(r){return Oe(r,(e,t)=>{let s=t.parseInterpolationExpression(e,...Ae);return s.ast=s.ast.expressions[0],s})}function ft(r){let e=pt(),{templateBindings:t,errors:s}=e.parseTemplateBindings(Ge,r,ht,De,De);return vt(s),t}function vt(r){if(r.length!==0){let[{message:e}]=r;throw new SyntaxError(e.replace(/^Parser Error: | at column \d+ in [^]*$/g,""))}}function Cr(r,e){let t=e._commentStart(r);return t===null?{astInput:r,comments:[]}:{astInput:r.slice(0,t),comments:[{type:"Comment",value:r.slice(t+2),sourceSpan:{start:t,end:r.length}}]}}function gt(r){return r instanceof B?"Unary":r instanceof A?"Binary":r instanceof Y?"BindingPipe":r instanceof re?"Call":r instanceof V?"Chain":r instanceof Q?"Conditional":r instanceof $?"EmptyExpr":r instanceof L?"ImplicitReceiver":r instanceof q?"KeyedRead":r instanceof j?"SafeKeyedRead":r instanceof X?"KeyedWrite":r instanceof Z?"LiteralArray":r instanceof J?"LiteralMap":r instanceof C?"LiteralPrimitive":r instanceof te?"NonNullAssert":r instanceof ee?"PrefixNot":r instanceof b?"PropertyRead":r instanceof H?"PropertyWrite":r instanceof se?"SafeCall":r instanceof z?"SafePropertyRead":r.type}function ct({start:r,end:e},t){let s=r,n=e;for(;n!==s&&/\s/.test(t[n-1]);)n--;for(;s!==n&&/\s/.test(t[s]);)s++;return{start:s,end:n}}function Ar({start:r,end:e},t){let s=r,n=e;for(;n!==t.length&&/\s/.test(t[n]);)n++;for(;s!==0&&/\s/.test(t[s-1]);)s--;return{start:s,end:n}}function Or(r,e){return e[r.start-1]==="("&&e[r.end]===")"?{start:r.start-1,end:r.end+1}:r}function St(r,e,t){let s=0,n={start:r.start,end:r.end};for(;;){let i=Ar(n,e),a=Or(i,e);if(i.start===a.start&&i.end===a.end)break;n.start=a.start,n.end=a.end,s++}return{hasParens:(t?s-1:s)!==0,outerSpan:ct(t?{start:n.start+1,end:n.end-1}:n,e),innerSpan:ct(r,e)}}function mt(r){return typeof r=="string"?e=>e===r:e=>r.test(e)}function wt(r,e,t){let s=mt(e);for(let n=t;n>=0;n--){let i=r[n];if(s(i))return n}throw new Error(`Cannot find front char ${e} from index ${t} in ${JSON.stringify(r)}`)}function yt(r,e,t){let s=mt(e);for(let n=t;n<r.length;n++){let i=r[n];if(s(i))return n}throw new Error(`Cannot find character ${e} from index ${t} in ${JSON.stringify(r)}`)}function Et(r){return r.slice(0,1).toLowerCase()+r.slice(1)}var ve=class{text;constructor(e){this.text=e}getCharacterIndex(e,t){return yt(this.text,e,t)}getCharacterLastIndex(e,t){return wt(this.text,e,t)}};var Nr=(r,e,t)=>{if(!(r&&e==null))return Array.isArray(e)||typeof e=="string"?e[t<0?e.length+t:t]:e.at(t)},Ne=Nr;var D=(r,e,t=!1)=>{let s=gt(r);switch(s){case"Unary":{let{operator:o,expr:u}=r,v=n(u);return a("UnaryExpression",{prefix:!0,argument:v,operator:o},r.sourceSpan,{hasParentParens:t})}case"Binary":{let{left:o,operation:u,right:v}=r,c=n(o),x=n(v);return a(u==="&&"||u==="||"||u==="??"?"LogicalExpression":"BinaryExpression",{left:c,right:x,operator:u},{start:E(c),end:S(x)},{hasParentParens:t})}case"BindingPipe":{let{exp:o,name:u,args:v}=r,c=n(o),x=e.getCharacterIndex(/\S/,e.getCharacterIndex("|",S(c))+1),l=a("Identifier",{name:u},{start:x,end:x+u.length}),p=v.map(n);return a("NGPipeExpression",{left:c,right:l,arguments:p},{start:E(c),end:S(p.length===0?l:Ne(!1,p,-1))},{hasParentParens:t})}case"Chain":{let{expressions:o}=r;return a("NGChainedExpression",{expressions:o.map(n)},r.sourceSpan,{hasParentParens:t})}case"Comment":{let{value:o}=r;return a("CommentLine",{value:o},r.sourceSpan,{processSpan:!1})}case"Conditional":{let{condition:o,trueExp:u,falseExp:v}=r,c=n(o),x=n(u),l=n(v);return a("ConditionalExpression",{test:c,consequent:x,alternate:l},{start:E(c),end:S(l)},{hasParentParens:t})}case"EmptyExpr":return a("NGEmptyExpression",{},r.sourceSpan,{hasParentParens:t});case"ImplicitReceiver":return a("ThisExpression",{},r.sourceSpan,{hasParentParens:t});case"KeyedRead":case"SafeKeyedRead":{let o=s==="SafeKeyedRead",{key:u}=r,v=Object.prototype.hasOwnProperty.call(r,"receiver")?r.receiver:r.obj,c=n(u);return h(v,c,{computed:!0,optional:o},{end:r.sourceSpan.end,hasParentParens:t})}case"LiteralArray":{let{expressions:o}=r;return a("ArrayExpression",{elements:o.map(n)},r.sourceSpan,{hasParentParens:t})}case"LiteralMap":{let{keys:o,values:u}=r,v=u.map(x=>n(x)),c=o.map(({key:x,quoted:l},p)=>{let k=v[p],m=E(k),N=S(k),ue=e.getCharacterIndex(/\S/,p===0?r.sourceSpan.start+1:e.getCharacterIndex(",",S(v[p-1]))+1),It=m===ue?N:e.getCharacterLastIndex(/\S/,e.getCharacterLastIndex(":",m-1)-1)+1,He={start:ue,end:It},ge=l?a("StringLiteral",{value:x},He):a("Identifier",{name:x},He),kt=ge.end<ge.start||ue===m;return a("ObjectProperty",{key:ge,value:k,shorthand:kt,computed:!1},{start:E(ge),end:N})});return a("ObjectExpression",{properties:c},r.sourceSpan,{hasParentParens:t})}case"LiteralPrimitive":{let{value:o}=r;switch(typeof o){case"boolean":return a("BooleanLiteral",{value:o},r.sourceSpan,{hasParentParens:t});case"number":return a("NumericLiteral",{value:o},r.sourceSpan,{hasParentParens:t});case"object":return a("NullLiteral",{},r.sourceSpan,{hasParentParens:t});case"string":return a("StringLiteral",{value:o},r.sourceSpan,{hasParentParens:t});case"undefined":return a("Identifier",{name:"undefined"},r.sourceSpan,{hasParentParens:t});default:throw new Error(`Unexpected LiteralPrimitive value type ${typeof o}`)}}case"Call":case"SafeCall":{let o=s==="SafeCall",{receiver:u,args:v}=r,c=v.length===1?[i(v[0])]:v.map(n),x=n(u),l=d(x),p=o||l?"OptionalCallExpression":"CallExpression";return a(p,{callee:x,arguments:c,optional:p==="OptionalCallExpression"?o:void 0},{start:E(x),end:r.sourceSpan.end},{hasParentParens:t})}case"NonNullAssert":{let{expression:o}=r,u=n(o);return a("TSNonNullExpression",{expression:u},{start:E(u),end:r.sourceSpan.end},{hasParentParens:t})}case"PrefixNot":{let{expression:o}=r,u=n(o);return a("UnaryExpression",{prefix:!0,operator:"!",argument:u},{start:r.sourceSpan.start,end:S(u)},{hasParentParens:t})}case"PropertyRead":case"SafePropertyRead":{let o=s==="SafePropertyRead",{receiver:u,name:v}=r,c=e.getCharacterLastIndex(/\S/,r.sourceSpan.end-1)+1,x=a("Identifier",{name:v},{start:c-v.length,end:c},g(u)?{hasParentParens:t}:{});return h(u,x,{computed:!1,optional:o},{hasParentParens:t})}case"KeyedWrite":{let{key:o,value:u}=r,v=Object.prototype.hasOwnProperty.call(r,"receiver")?r.receiver:r.obj,c=n(o),x=n(u),l=h(v,c,{computed:!0,optional:!1},{end:e.getCharacterIndex("]",S(c))+1});return a("AssignmentExpression",{left:l,operator:"=",right:x},{start:E(l),end:S(x)},{hasParentParens:t})}case"PropertyWrite":{let{receiver:o,name:u,value:v}=r,c=n(v),x=e.getCharacterLastIndex(/\S/,e.getCharacterLastIndex("=",E(c)-1)-1)+1,l=a("Identifier",{name:u},{start:x-u.length,end:x}),p=h(o,l,{computed:!1,optional:!1});return a("AssignmentExpression",{left:p,operator:"=",right:c},{start:E(p),end:S(c)},{hasParentParens:t})}default:throw new Error(`Unexpected node ${s}`)}function n(o){return D(o,e)}function i(o){return D(o,e,!0)}function a(o,u,v,{processSpan:c=!0,hasParentParens:x=!1}={}){let l={type:o,...Ie(v,e,c,x),...u};switch(o){case"NumericLiteral":{let p=l;p.extra={...p.extra,raw:e.text.slice(p.start,p.end),rawValue:p.value};break}case"StringLiteral":{let p=l;p.extra={...p.extra,raw:e.text.slice(p.start,p.end),rawValue:p.value};break}case"ObjectProperty":{let p=l;p.shorthand&&(p.extra={...p.extra,shorthand:p.shorthand});break}}return l}function h(o,u,v,{end:c=S(u),hasParentParens:x=!1}={}){if(g(o)||o.sourceSpan.start===u.start)return u;let l=n(o),p=d(l);return a(v.optional||p?"OptionalMemberExpression":"MemberExpression",{object:l,property:u,computed:v.computed,...v.optional?{optional:!0}:p?{optional:!1}:null},{start:E(l),end:c},{hasParentParens:x})}function g(o){return o.sourceSpan.start>=o.sourceSpan.end||/^\s+$/.test(e.text.slice(o.sourceSpan.start,o.sourceSpan.end))}function d(o){return(o.type==="OptionalCallExpression"||o.type==="OptionalMemberExpression")&&!y(o)}function y(o){return o.extra&&o.extra.parenthesized}function E(o){return y(o)?o.extra.parenStart:o.start}function S(o){return y(o)?o.extra.parenEnd:o.end}};function Ie(r,e,t=!1,s=!1){if(!t){let{start:g,end:d}=r;return{start:g,end:d,range:[g,d]}}let{outerSpan:n,innerSpan:{start:i,end:a},hasParens:h}=St(r,e.text,s);return{start:i,end:a,range:[i,a],...h&&{extra:{parenthesized:!0,parenStart:n.start,parenEnd:n.end}}}}function $t(r,e){r.forEach(o);let[t]=r,{key:s}=t,n=e.text.slice(t.sourceSpan.start,t.sourceSpan.end).trim().length===0?r.slice(1):r,i=[],a=null;for(let c=0;c<n.length;c++){let x=n[c];if(a&&E(a)&&S(x)&&x.value&&x.value.source===a.key.source){let l=d("NGMicrosyntaxKey",{name:x.key.source},x.key.span),p=(N,ue)=>({...N,...Ie({start:N.start,end:ue},e)}),k=N=>({...p(N,l.end),alias:l}),m=i.pop();if(m.type==="NGMicrosyntaxExpression")i.push(k(m));else if(m.type==="NGMicrosyntaxKeyedExpression"){let N=k(m.expression);i.push(p({...m,expression:N},N.end))}else throw new Error(`Unexpected type ${m.type}`)}else i.push(h(x,c));a=x}return d("NGMicrosyntax",{body:i},i.length===0?r[0].sourceSpan:{start:i[0].start,end:Ne(!1,i,-1).end});function h(c,x){if(E(c)){let{key:l,value:p}=c;return p?x===0?d("NGMicrosyntaxExpression",{expression:g(p.ast),alias:null},p.sourceSpan):d("NGMicrosyntaxKeyedExpression",{key:d("NGMicrosyntaxKey",{name:y(l.source)},l.span),expression:d("NGMicrosyntaxExpression",{expression:g(p.ast),alias:null},p.sourceSpan)},{start:l.span.start,end:p.sourceSpan.end}):d("NGMicrosyntaxKey",{name:y(l.source)},l.span)}else{let{key:l,sourceSpan:p}=c;if(/^let\s$/.test(e.text.slice(p.start,p.start+4))){let{value:m}=c;return d("NGMicrosyntaxLet",{key:d("NGMicrosyntaxKey",{name:l.source},l.span),value:m?d("NGMicrosyntaxKey",{name:m.source},m.span):null},{start:p.start,end:m?m.span.end:l.span.end})}else{let m=v(c);return d("NGMicrosyntaxAs",{key:d("NGMicrosyntaxKey",{name:m.source},m.span),alias:d("NGMicrosyntaxKey",{name:l.source},l.span)},{start:m.span.start,end:l.span.end})}}}function g(c){return D(c,e)}function d(c,x,l,p=!0){return{type:c,...Ie(l,e,p),...x}}function y(c){return Et(c.slice(s.source.length))}function E(c){return c instanceof ne}function S(c){return c instanceof T}function o(c){u(c.key.span),S(c)&&c.value&&u(c.value.span)}function u(c){if(e.text[c.start]!=='"'&&e.text[c.start]!=="'")return;let x=e.text[c.start],l=!1;for(let p=c.start+1;p<e.text.length;p++)switch(e.text[p]){case x:if(!l){c.end=p+1;return}default:l=!1;break;case"\\":l=!l;break}}function v(c){if(!c.value||c.value.source!==Ge)return c.value;let x=e.getCharacterIndex(/\S/,c.sourceSpan.start);return{source:"$implicit",span:{start:x,end:x}}}}function ke(r){return e=>{let{ast:t,comments:s}=r(e),n=new ve(e),i=D(t,n);return i.comments=s.map(a=>D(a,n)),i}}var Ct=ke(ut),hs=ke(lt),At=ke(dt),We=ke(xt),Ot=r=>$t(ft(r),new ve(r));function Ve(r){var s,n,i;let e=((s=r.range)==null?void 0:s[0])??r.start,t=(i=((n=r.declaration)==null?void 0:n.decorators)??r.decorators)==null?void 0:i[0];return t?Math.min(Ve(t),e):e}function Nt(r){var e;return((e=r.range)==null?void 0:e[1])??r.end}function Re(r){return{astFormat:"estree",parse(e){let t=r(e);return{type:"NGRoot",node:r===We&&t.type!=="NGChainedExpression"?{...t,type:"NGChainedExpression",expressions:[t]}:t}},locStart:Ve,locEnd:Nt}}var Ir=Re(We),kr=Re(Ct),Rr=Re(At),Pr=Re(Ot);return bt(Lr);});