// Generated by CoffeeScript 2.4.1
(function() {
  var NodeType, XMLDocumentFragment, XMLNode;

  XMLNode = require('./XMLNode');

  NodeType = require('./NodeType');

  // Represents a  CDATA node
  module.exports = XMLDocumentFragment = class XMLDocumentFragment extends XMLNode {
    // Initializes a new instance of `XMLDocumentFragment`

    constructor() {
      super(null);
      this.name = "#document-fragment";
      this.type = NodeType.DocumentFragment;
    }

  };

}).call(this);
