#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最简单的启动脚本
直接导入并运行Flask应用
"""

import os
import sys
from pathlib import Path

def main():
    print("=" * 60)
    print("🦴 骨振识息系统 - 后端服务")
    print("=" * 60)
    
    # 设置项目路径
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 后端目录: {backend_dir}")
    
    # 检查后端目录是否存在
    if not backend_dir.exists():
        print(f"❌ 后端目录不存在: {backend_dir}")
        return 1
    
    # 切换到后端目录
    original_cwd = os.getcwd()
    os.chdir(backend_dir)
    print(f"📁 当前工作目录: {os.getcwd()}")
    
    # 添加后端目录到Python路径
    if str(backend_dir) not in sys.path:
        sys.path.insert(0, str(backend_dir))
    
    try:
        print("📦 导入Flask应用...")
        
        # 设置环境变量
        os.environ['FLASK_APP'] = 'app.py'
        os.environ['FLASK_ENV'] = 'development'
        
        # 导入Flask应用
        import app
        
        print("✅ Flask应用导入成功")
        print(f"📍 服务地址: http://localhost:5000")
        print("💡 API测试: 在另一个终端运行 'python backend/test_api.py'")
        print("🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        if hasattr(app, 'app'):
            flask_app = app.app
        else:
            flask_app = app
            
        flask_app.run(
            host='127.0.0.1',
            port=5000,
            debug=True,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有依赖已安装:")
        print("pip install flask flask-cors numpy scipy torch matplotlib werkzeug")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
        sys.exit(0)
