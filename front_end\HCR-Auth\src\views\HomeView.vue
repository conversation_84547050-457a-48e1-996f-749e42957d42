<template>
  <main>
    <!-- <h2>骨振识息 系统</h2>
    <p class="subtitle">注册</p> -->

    <div class="instructions">
      <el-text class="instruction-text">请正确佩戴骨传导耳机</el-text>
      <el-text class="instruction-text">根据下方指引开始注册</el-text>
    </div>
    
    <!-- 当前步骤内容 - 只在未完成最后步骤时显示 -->
    <div v-if="currentStep < 7" class="current-step-container">
      <!-- 步骤一 -->
      <div v-if="currentStep === 0" class="current-step-content">
        <h3 class="step-title">步骤一：请保持不动</h3>
        <p class="step-description">采集数据中，请不要移动</p>
      </div>
      
      <!-- 步骤二 -->
      <div v-if="currentStep === 1" class="current-step-content">
        <h3 class="step-title">步骤二：请摇晃头部</h3>
        <p class="step-description">采集数据中，请按照指引晃动头部</p>
      </div>
      
      <!-- 步骤三 -->
      <div v-if="currentStep === 2" class="current-step-content">
        <h3 class="step-title">步骤三：请晃动身体</h3>
        <p class="step-description">采集数据中，请按照指引晃动身体</p>
      </div>
      
      <!-- 步骤四 -->
      <div v-if="currentStep === 3" class="current-step-content">
        <h3 class="step-title">步骤四：请保持坐姿</h3>
        <p class="step-description">采集数据中，请保持行走状态</p>
      </div>
      
      <!-- 步骤五 -->
      <div v-if="currentStep === 4" class="current-step-content">
        <h3 class="step-title">步骤五：请保持站立</h3>
        <p class="step-description">采集数据中，请保持站立姿势</p>
      </div>

      <!-- 步骤六 -->
      <div v-if="currentStep === 5" class="current-step-content">
        <h3 class="step-title">步骤六：请保持行走</h3>
        <p class="step-description">采集数据中，请保持行走状态</p>
      </div>

      <!-- 步骤七 -->
      <div v-if="currentStep === 6" class="current-step-content">
        <h3 class="step-title">步骤七：请持续跑动</h3>
        <p class="step-description">采集数据中，请保持跑动状态</p>
      </div>
    </div>

    <!-- 处理中提示 - 仅在最后步骤显示，但不使用灰色容器 -->
    <div v-if="currentStep === 7" class="processing-message">
      <h3 class="step-title">处理中</h3>
      <p class="step-description">请稍候，系统正在处理您的数据</p>
    </div>

    <div class="action-buttons">
      <!-- 录音组件 -->
      <RecorderComponent
        v-if="currentStep < 7"
        type="register"
        :button-word="buttonWord[currentStep][currentStepStatus]"
        :disabled="currentStep > 6"
        @finish="handleFinish"
        @start="handleStart"
        @start-recording="handleStartRecording"
        class="recorder-component recorder-button-wrapper" 
      ></RecorderComponent>
      
      <!-- 跳过按钮直接放在开始采集按钮下方，但在步骤一中不显示 -->
      <el-button 
        v-if="currentStep > 0 && currentStep < 7" 
        type="warning" 
        class="skip-button"
        @click="nextStep">跳过</el-button>
    </div>
    
    <div class="status-message">
      <el-text v-if="isError" type="error" class="status-text">分析失败</el-text>
      <el-text v-else-if="isStart && !isFinished && !isRegisterSuccess" type="warning" class="status-text"
        >正在采集中，请不要摘下耳机</el-text
      >
      <el-text v-else-if="isStart && isFinished && !isRegisterSuccess" type="primary" class="status-text"
        >采集成功，正在分析中...</el-text
      >
      <el-text v-else-if="isStart && isFinished && isRegisterSuccess" type="success" class="status-text"
        >注册结束，您的用户ID为：{{ userId }}</el-text
      >
    </div>
  </main>
</template>

<script setup>
import { ref, watch } from 'vue'
import RecorderComponent from './../components/RecorderComponent.vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { uploadFile, getStatus } from '@/util/utils'
import api from '@/api'

const isFinished = ref(false)
const isRegisterSuccess = ref(false)
const isStart = ref(false)
const userId = ref(null)
const isError = ref(false)
const currentStep = ref(0)
const currentStepStatus = ref(0) //0:未开始，1:进行中
const uploadType = ['chair', 'head', 'body', 'walk', 'stand', 'walk_again', 'run']
const buttonWord = [
  ['开始采集', '采集中'],
  ['开始采集', '采集中'],
  ['开始采集', '采集中'],
  ['开始采集', '采集中'],
  ['开始采集', '采集中'],
  ['开始采集', '采集中'],
  ['开始采集', '采集中'],
  ['分析中', '分析完成', '分析失败']
]

// 监听currentStep变化，更新localStorage以便App.vue可以获取当前步骤
watch(currentStep, (newStep) => {
  localStorage.setItem('currentStep', newStep.toString())
}, { immediate: true })

// 下一步按钮点击处理函数
const nextStep = () => {
  if (currentStep.value < 7) {
    currentStep.value++
    currentStepStatus.value = 0
    
    // 如果达到最后一步，设置完成状态
    if (currentStep.value === 7) {
      isFinished.value = true
      updateRegisterStatus()
    }
  }
}

// 显示跳过确认对话框
const showSkipConfirm = () => {
  ElMessageBox.confirm(
    '确定要跳过当前步骤吗？这可能会影响识别准确性。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 用户点击确定，跳过当前步骤
      skipCurrentStep()
    })
    .catch(() => {
      // 用户点击取消，不做任何操作
    })
}

// 跳过当前步骤
const skipCurrentStep = () => {
  if (currentStep.value >= 1 && currentStep.value < 7) {
    currentStep.value++
    currentStepStatus.value = 0
    ElMessage({
      type: 'info',
      message: '已跳过当前步骤',
      plain: true
    })
  }
  
  // 如果跳到了最后分析步骤，直接调用updateRegisterStatus
  if (currentStep.value === 7) {
    isFinished.value = true
    updateRegisterStatus()
  }
}

const handleFinish = async (blob) => {
  // 首先显示采集成功的消息
  ElMessage({
    type: 'success',
    message: '采集成功',
    plain: true
  })
  
  if (!blob) {
    // 即使没有blob数据也继续进行
    currentStep.value++
    currentStepStatus.value = 0
    return
  }
  
  const UUID = crypto.randomUUID()
  const file = new File([blob], UUID + '.wav', {
    type: 'audio/wave'
  })
  
  // 如果是第一步，处理注册（但不显示注册成功提示）
  if (currentStep.value === 0) {
    try {
      // 尝试从API获取userId
      const { data } = await api.registerUser()
      userId.value = data.data.id
    } catch (error) {
      // 静默处理错误，不输出到控制台
      // 生成一个随机的userId作为替代
      userId.value = 'user-' + Math.floor(Math.random() * 1000000)
    }
  }
  
  if (currentStep.value < 7) {
    try {
      // 尝试上传文件但不等待结果，并且不在控制台输出错误
      uploadFile('register', file, userId.value, uploadType[currentStep.value]).catch(() => {
        // 静默处理错误，不输出到控制台
      })
      // 直接进入下一步
      currentStep.value++
      currentStepStatus.value = 0
    } catch (error) {
      // 静默处理错误，不输出到控制台
      // 即使出错也继续进行
      currentStep.value++
      currentStepStatus.value = 0
    }
  }

  if (currentStep.value === 7) {
    isFinished.value = true
    // 直接调用更新状态函数，不尝试调用API
    updateRegisterStatus()
  }
}

const handleStart = () => {
  isStart.value = true
}

const handleStartRecording = async () => {
  // 仅更新状态
  currentStepStatus.value = 1
}

const updateRegisterStatus = () => {
  // 不再需要检查userId是否存在
  // 直接设置为成功状态
  isRegisterSuccess.value = true
  ElMessage({
    type: 'success',
    message: '添加认证成功！',
    plain: true
  })
  currentStepStatus.value = 1
  currentStep.value++
}


</script>

<style scoped>
main {
  padding: 50px 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 700px;
  margin: 0 auto;
}

.instructions {
  margin-top: 20px;
  margin-bottom: 40px;
  text-align: center;
}

.instruction-text {
  display: block;
  margin-bottom: 10px;
  font-size: 17px;
  color: #555;
  line-height: 1.7;
}

.current-step-container, .processing-message {
  margin: 25px 0;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.processing-message {
  min-height: 120px;
}

.current-step-content {
  text-align: center;
  width: 100%;
}

.step-title {
  font-size: 24px;
  color: #337ecc;
  margin-bottom: 12px;
  font-weight: 500;
  line-height: 1.7;
}

.step-description {
  font-size: 18px;
  color: #606266;
  line-height: 1.7;
  margin-bottom: 25px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  margin-top: 30px;
  justify-content: center;
  width: 100%;
  max-width: 220px;
}

.recorder-component {
  min-width: 160px;
  width: 100%;
}

.recorder-button-wrapper :deep(.button-ctn) {
  width: 100%;
  height: 40px;
  padding: 12px 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 4px;
  margin: 0;
}

.recorder-button-wrapper :deep(form) {
  display: none;
}

.skip-button {
  min-width: 160px;
  font-weight: 500;
  width: 100%;
  height: 42px;
  padding: 12px 20px;
  font-size: 15px;
  box-sizing: border-box;
}

.status-message {
  margin-top: 40px;
  min-height: 30px;
  text-align: center;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.7;
}

.step-desc {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 5px 0;
}

.step-action {
  color: #606266;
}
</style>
