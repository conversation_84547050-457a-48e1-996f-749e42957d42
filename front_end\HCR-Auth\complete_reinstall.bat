@echo off
chcp 65001 >nul
title Frontend Complete Reinstall

echo ================================================================
echo HCR-Auth Frontend Complete Reinstall
echo ================================================================
echo.

echo Current directory: %CD%
echo.

echo Checking environment...
node --version
npm --version
echo.

echo Stopping Node processes...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo.

echo Complete cleanup...
if exist node_modules (
    echo Removing node_modules...
    rmdir /s /q node_modules
)
if exist package-lock.json (
    echo Removing package-lock.json...
    del package-lock.json
)
if exist yarn.lock (
    echo Removing yarn.lock...
    del yarn.lock
)
echo Cleanup completed
echo.

echo 🔧 重置npm配置...
npm config delete registry
npm config delete cache
npm config set registry https://registry.npmmirror.com
npm config set cache "%USERPROFILE%\npm-cache"
npm config set fund false
npm config set audit false
echo ✅ npm配置完成
echo.

echo 📦 恢复原始package.json...
if exist package_original.json (
    copy package_original.json package.json >nul
    echo ✅ 已恢复原始package.json
) else (
    echo ⚠️  未找到备份，使用当前package.json
)
echo.

echo 📦 开始完整安装...
echo 这次会安装所有依赖，包括可选的原生模块...
echo 请耐心等待，可能需要几分钟...
echo.

REM 方法1：标准安装
echo 🔄 方法1: 标准安装...
npm install

if %errorlevel% equ 0 (
    echo ✅ 标准安装成功！
    goto test_dev
)

echo ❌ 标准安装失败，尝试方法2...
echo.

REM 方法2：强制重新构建
echo 🔄 方法2: 强制重新构建...
npm install --force

if %errorlevel% equ 0 (
    echo ✅ 强制安装成功！
    goto test_dev
)

echo ❌ 强制安装失败，尝试方法3...
echo.

REM 方法3：使用官方源
echo 🔄 方法3: 使用官方源...
npm config set registry https://registry.npmjs.org/
npm install

if %errorlevel% equ 0 (
    echo ✅ 官方源安装成功！
    goto test_dev
)

echo ❌ 所有npm方法都失败，尝试yarn...
echo.

REM 方法4：使用yarn
echo 🔄 方法4: 尝试使用yarn...
where yarn >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装yarn...
    npm install -g yarn
)

yarn install

if %errorlevel% equ 0 (
    echo ✅ yarn安装成功！
    goto test_yarn_dev
)

echo ❌ 所有方法都失败了
goto failed

:test_dev
echo.
echo 🧪 测试npm run dev...
timeout /t 2 /nobreak >nul
npm run dev
goto end

:test_yarn_dev
echo.
echo 🧪 测试yarn dev...
timeout /t 2 /nobreak >nul
yarn dev
goto end

:failed
echo.
echo ❌ 所有安装方法都失败了
echo.
echo 💡 可能的解决方案:
echo 1. 检查网络连接
echo 2. 尝试使用手机热点
echo 3. 关闭杀毒软件重试
echo 4. 以管理员身份运行PowerShell重试
echo 5. 或者使用在线IDE（如CodeSandbox）
echo.
echo 🔧 手动尝试:
echo npm cache clean --force
echo npm install --legacy-peer-deps
echo.

:end
pause
