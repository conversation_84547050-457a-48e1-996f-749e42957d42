{"version": 3, "file": "snapStorePublisher.js", "sourceRoot": "", "sources": ["../src/snapStorePublisher.ts"], "names": [], "mappings": ";;;AAAA,+CAAgD;AAEhD,6BAA4B;AAE5B,2CAAuC;AAEvC,MAAa,kBAAmB,SAAQ,qBAAS;IAG/C,YACE,OAAuB,EACf,OAAyB;QAEjC,KAAK,CAAC,OAAO,CAAC,CAAA;QAFN,YAAO,GAAP,OAAO,CAAkB;QAJ1B,iBAAY,GAAG,WAAW,CAAA;IAOnC,CAAC;IAED,MAAM,CAAC,IAAgB;QACrB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QAEpD,MAAM,IAAI,GAAG,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAE9C,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACpC,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,QAAQ,GAAG,CAAC,MAAM,CAAC,CAAA;QACrB,CAAC;aAAM,CAAC;YACN,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAChC,CAAC;QACH,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QAC1B,CAAC;QAED,OAAO,IAAA,gCAAiB,EAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAED,QAAQ;QACN,OAAO,YAAY,CAAA;IACrB,CAAC;CACF;AAlCD,gDAkCC", "sourcesContent": ["import { executeAppBuilder } from \"builder-util\"\nimport { SnapStoreOptions } from \"builder-util-runtime/out/publishOptions\"\nimport * as path from \"path\"\nimport { PublishContext, UploadTask } from \".\"\nimport { Publisher } from \"./publisher\"\n\nexport class SnapStorePublisher extends Publisher {\n  readonly providerName = \"snapStore\"\n\n  constructor(\n    context: PublishContext,\n    private options: SnapStoreOptions\n  ) {\n    super(context)\n  }\n\n  upload(task: UploadTask): Promise<any> {\n    this.createProgressBar(path.basename(task.file), -1)\n\n    const args = [\"publish-snap\", \"-f\", task.file]\n\n    let channels = this.options.channels\n    if (channels == null) {\n      channels = [\"edge\"]\n    } else {\n      if (typeof channels === \"string\") {\n        channels = channels.split(\",\")\n      }\n    }\n\n    for (const channel of channels) {\n      args.push(\"-c\", channel)\n    }\n\n    return executeAppBuilder(args)\n  }\n\n  toString(): string {\n    return \"Snap Store\"\n  }\n}\n"]}