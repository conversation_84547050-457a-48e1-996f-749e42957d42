# PowerShell script to fix frontend issues
# Run with: PowerShell -ExecutionPolicy Bypass -File fix_frontend.ps1

Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "HCR-Auth Frontend Fix Script" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

# Check current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Green

# Check if package.json exists
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found!" -ForegroundColor Red
    Write-Host "Please run this script in the front_end/HCR-Auth directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found package.json" -ForegroundColor Green
Write-Host ""

# Check Node.js and npm versions
Write-Host "Checking environment..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js or npm not found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

# Stop any running Node processes
Write-Host "Stopping Node processes..." -ForegroundColor Yellow
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "npm" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Write-Host "Done" -ForegroundColor Green
Write-Host ""

# Complete cleanup
Write-Host "Complete cleanup..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "Removing node_modules..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
}
if (Test-Path "package-lock.json") {
    Write-Host "Removing package-lock.json..." -ForegroundColor Yellow
    Remove-Item -Force "package-lock.json" -ErrorAction SilentlyContinue
}
if (Test-Path "yarn.lock") {
    Write-Host "Removing yarn.lock..." -ForegroundColor Yellow
    Remove-Item -Force "yarn.lock" -ErrorAction SilentlyContinue
}
Write-Host "Cleanup completed" -ForegroundColor Green
Write-Host ""

# Configure npm
Write-Host "Configuring npm..." -ForegroundColor Yellow
npm config set registry https://registry.npmmirror.com
npm config set cache "$env:USERPROFILE\npm-cache"
npm config set fund false
npm config set audit false
Write-Host "npm configured" -ForegroundColor Green
Write-Host ""

# Restore original package.json if backup exists
if (Test-Path "package_original.json") {
    Write-Host "Restoring original package.json..." -ForegroundColor Yellow
    Copy-Item "package_original.json" "package.json" -Force
    Write-Host "Original package.json restored" -ForegroundColor Green
} else {
    Write-Host "No backup found, using current package.json" -ForegroundColor Yellow
}
Write-Host ""

# Try multiple installation methods
$installSuccess = $false

# Method 1: Standard install
Write-Host "Method 1: Standard install..." -ForegroundColor Cyan
try {
    npm install
    if ($LASTEXITCODE -eq 0) {
        $installSuccess = $true
        Write-Host "Standard install successful!" -ForegroundColor Green
    }
} catch {
    Write-Host "Standard install failed" -ForegroundColor Red
}

# Method 2: Force install
if (-not $installSuccess) {
    Write-Host ""
    Write-Host "Method 2: Force install..." -ForegroundColor Cyan
    try {
        npm install --force
        if ($LASTEXITCODE -eq 0) {
            $installSuccess = $true
            Write-Host "Force install successful!" -ForegroundColor Green
        }
    } catch {
        Write-Host "Force install failed" -ForegroundColor Red
    }
}

# Method 3: Legacy peer deps
if (-not $installSuccess) {
    Write-Host ""
    Write-Host "Method 3: Legacy peer deps..." -ForegroundColor Cyan
    try {
        npm install --legacy-peer-deps
        if ($LASTEXITCODE -eq 0) {
            $installSuccess = $true
            Write-Host "Legacy peer deps install successful!" -ForegroundColor Green
        }
    } catch {
        Write-Host "Legacy peer deps install failed" -ForegroundColor Red
    }
}

# Method 4: Official registry
if (-not $installSuccess) {
    Write-Host ""
    Write-Host "Method 4: Official registry..." -ForegroundColor Cyan
    npm config set registry https://registry.npmjs.org/
    try {
        npm install
        if ($LASTEXITCODE -eq 0) {
            $installSuccess = $true
            Write-Host "Official registry install successful!" -ForegroundColor Green
        }
    } catch {
        Write-Host "Official registry install failed" -ForegroundColor Red
    }
}

# Method 5: Try yarn
if (-not $installSuccess) {
    Write-Host ""
    Write-Host "Method 5: Trying yarn..." -ForegroundColor Cyan
    
    # Check if yarn is installed
    try {
        $yarnVersion = yarn --version
        Write-Host "Yarn version: $yarnVersion" -ForegroundColor Green
    } catch {
        Write-Host "Installing yarn..." -ForegroundColor Yellow
        npm install -g yarn
    }
    
    try {
        yarn install
        if ($LASTEXITCODE -eq 0) {
            $installSuccess = $true
            Write-Host "Yarn install successful!" -ForegroundColor Green
            
            # Test yarn dev
            Write-Host ""
            Write-Host "Testing yarn dev..." -ForegroundColor Cyan
            Write-Host "Starting development server..." -ForegroundColor Yellow
            yarn dev
            exit 0
        }
    } catch {
        Write-Host "Yarn install failed" -ForegroundColor Red
    }
}

Write-Host ""
if ($installSuccess) {
    Write-Host "Installation successful!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Testing npm run dev..." -ForegroundColor Cyan
    Write-Host "Starting development server..." -ForegroundColor Yellow
    Write-Host "If successful, you should see Vite server starting..." -ForegroundColor Yellow
    Write-Host ""
    
    # Test dev server
    npm run dev
} else {
    Write-Host "All installation methods failed!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible solutions:" -ForegroundColor Yellow
    Write-Host "1. Check network connection" -ForegroundColor White
    Write-Host "2. Try using mobile hotspot" -ForegroundColor White
    Write-Host "3. Run as Administrator" -ForegroundColor White
    Write-Host "4. Disable antivirus temporarily" -ForegroundColor White
    Write-Host "5. Try downgrading Node.js to LTS version" -ForegroundColor White
    Write-Host ""
    Write-Host "Manual commands to try:" -ForegroundColor Yellow
    Write-Host "npm cache clean --force" -ForegroundColor White
    Write-Host "npm install --legacy-peer-deps --force" -ForegroundColor White
}

Read-Host "Press Enter to exit"
