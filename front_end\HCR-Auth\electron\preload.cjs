// electron/preload.cjs
console.log('[Preload] Preload script successfully loaded.');
console.log('[Preload] Current URL:', window.location.href);
console.log('[Preload] Document readyState:', document.readyState);

window.addEventListener('DOMContentLoaded', () => {
  console.log('[Preload] DOMContentLoaded event fired.');
  console.log('[Preload] Document title:', document.title);
  console.log('[Preload] Document body content:', document.body.innerHTML);
  
  // 检查是否有 Vue 应用的根元素
  const app = document.getElementById('app');
  if (app) {
    console.log('[Preload] Found #app element');
  } else {
    console.log('[Preload] ERROR: #app element not found!');
  }
  
  // 检查脚本标签
  const scripts = document.querySelectorAll('script');
  console.log('[Preload] Found', scripts.length, 'script tags');
  scripts.forEach((script, index) => {
    console.log(`[Preload] Script ${index}: src="${script.src || 'inline'}" type="${script.type}"`);
  });
});

window.addEventListener('load', () => {
  console.log('[Preload] Load event fired - all resources loaded.');
});

window.addEventListener('error', (event) => {
  console.error('[Preload] Error occurred:', event.error, event.filename, event.lineno);
}); 