# -*- coding:utf-8 -*-
from matplotlib.mlab import psd, csd
import numpy as np
import scipy.io.wavfile as wav
from scipy import signal
from scipy.signal import resample
import matplotlib.pyplot as plt

def zeroMeanNorm(data):
    mean = np.mean(data)
    std = np.std(data)
    for i in range(len(data)):
        data[i] = (data[i]-mean)/std
    return data



def read_date(txt_root):
    f = open(txt_root) #需要读取内容的文件路径, 如果路径中含有单\的话,需要在最前面添加r进行字符转义
    line = f.readline()
    data_list = []
    while line:
        data_split = line.split()
        temp = list(map(float, data_split))
        if(len(temp) == 0):
            pass
        else:
            data_list.append(temp)
        line = f.readline()
    f.close()

    data_matrix = np.asmatrix(data_list)
    # print("data_matrix:", data_matrix)
    dataX = data_matrix[:, 0]
    dataY = data_matrix[:, 1]
    dataZ = data_matrix[:, 2]

    dataX_ = np.matrix.tolist(dataX.T)
    dataX = dataX_[0]
    dataY_ = np.matrix.tolist(dataY.T)
    dataY = dataY_[0]
    dataZ_ = np.matrix.tolist(dataZ.T)
    dataZ = dataZ_[0]

    return dataX, dataY, dataZ

def calcuTransFunction(transmittedSignal, receivedSignal, Fs, threshFrequency):

    # print(len(rawMusicData), len(vibData))
    # vibData = zeroMeanNorm(vibData) #零均值归一化
    Pxx, fxx = csd(transmittedSignal, transmittedSignal, Fs=Fs, NFFT=128, noverlap=64) #求自功率谱密度，得到的是一个实数
    Pxx = [np.abs(x) for x in Pxx] #求幅度
    # plt.plot(fxx, Pxx)
    # plt.show()
    Pxy, fxy = csd(transmittedSignal, receivedSignal, Fs=Fs, NFFT=128, noverlap=64) #求交叉功率谱密度，得到的是一个复数
    Pxy = [np.abs(x) for x in Pxy] #求幅度
    # plt.plot(fxx, Pxy)
    # plt.show()
    tfestimate = [] #计算转换函数
    fre = []
    for i in range(len(fxx)):
        if threshFrequency <= fxx[i]: #这里限制一下频率范围，因为20Hz以下是噪声
            tfestimate.append(Pxy[i]/Pxx[i]) #计算转换函数
            fre.append(fxx[i])
    return fre, tfestimate


if __name__ == '__main__':

    input_file = "50_800_W.wav"
    frameRate = 467
    # 读取原始音频文件
    original_sampling_rate, audio_data = wav.read(input_file)
    # 计算采样率的比例
    sampling_rate_ratio = frameRate / original_sampling_rate
    # 计算目标音频数据的长度
    target_length = int(len(audio_data) * sampling_rate_ratio)
    # 执行音频重采样
    resampled_audio = resample(audio_data, target_length)
    resampled_audio = zeroMeanNorm(resampled_audio)

    dataX, dataY, dataZ = read_date("L_1.txt")
    # print("type(dataZ):", type(dataZ))
    current_axis = dataZ
    current_axis = zeroMeanNorm(current_axis)

    # print("len(resampled_audio):", len(resampled_audio))

    fre, tf = calcuTransFunction(resampled_audio, current_axis, frameRate)
    print("len(tf):", len(tf))

    #作图
    # 接下来要把得到的点作在时序图上
    # 作总的交叉相关性图
    # index = []
    # for i in range(len(tf)):
    #     index.append(i)
    plt.plot(fre, tf)
    plt.show()
