# -*- coding:utf-8 -*-

if __name__ == '__main__':

    import numpy as np
    from scipy.signal import butter, lfilter

    # 定义高通滤波器参数
    cutoff_freq = 20  # 截止频率
    sampling_rate = 467  # 采样率
    filter_order = 4  # 滤波器阶数

    # 指定原始文档路径
    file_path = "path/to/your/file.txt"

    # 读取原始文档的数据
    data = np.loadtxt(file_path, delimiter=",")

    # 获取数据的列数
    num_cols = data.shape[1]

    # 设计高通滤波器
    nyquist_freq = 0.5 * sampling_rate
    cutoff = cutoff_freq / nyquist_freq
    b, a = butter(filter_order, cutoff, btype='highpass')

    # 对每列数据应用高通滤波器
    filtered_data = np.zeros_like(data)
    for col in range(num_cols):
        filtered_data[:, col] = lfilter(b, a, data[:, col])

    # 将滤波后的数据写回原始文档
    np.savetxt(file_path, filtered_data, delimiter=",", fmt='%.2f')

