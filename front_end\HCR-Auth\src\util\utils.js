import api from '@/api'

export const debounce = (func, wait) => {
  let timeout
  return function (...args) {
    const context = this
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => {
      timeout = null
      func.apply(context, args)
    }, wait)
  }
}

export const uploadFile = async (type, file, userId,uploadType,fake) => {
  try {
    let response
    if (type === 'register') {
      response = await api.uploadFile(file,userId,uploadType)
    } else if (type === 'login') {
      response = await api.loginUser(file, userId,fake)
    } else {
      throw new Error('Invalid type')
    }
    return response
  } catch (error) {
    throw new Error(error)
  }
}

export const getStatus = async (type, userId, statusId) => {
  let request
  if (type === 'register') {
    request = api.getUserRegisterStatus
  } else if (type === 'login') {
    request = api.getUserLoginStatus
  } else {
    throw new Error('Invalid type')
  }
  
  return new Promise((resolve, reject) => {
    const interval = setInterval(async () => {
      try {
        let { data } = await request(userId, statusId)
        if (data.data.status === 'analyzing') {
          // 正在分析中
        } else if (data.data.status === 'success') {
          clearInterval(interval)
          resolve(data.data) // 解析 Promise 并返回数据
        } else if (data.data.status === 'failed') {
          clearInterval(interval)
          reject(new Error('Failed to process the request')) // 拒绝 Promise 并返回错误
        } else {
          clearInterval(interval)
          reject(new Error('Unknown error occurred')) // 拒绝 Promise 并返回错误
        }
      } catch (error) {
        clearInterval(interval)
        reject(new Error(error)) // 拒绝 Promise 并返回错误
      }
    }, 1000)
  })
}
