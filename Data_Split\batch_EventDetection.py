# -*- coding:utf-8 -*-

import scipy.io.wavfile as wav
from scipy.signal import resample
from Event_Detection import read_date, signal_event_detection
import os


def check_difference(lst):
    for i in range(len(lst) - 1):
        if lst[i+1] - lst[i] <= 600:
            print("两个数值为:", lst[i], lst[i+1])
            raise ValueError("数值差不满足要求")
    # print("数值差满足要求")

def store_segment(event_Index, big_filename, store_path, L_or_R, startName):
    # index_list = [1, 100, 1000, 5000, 10000]  # 索引列表
    # filename = 'L_lift.txt'  # 大的 txt 文件名
    # 逐个处理每个索引值
    for i, index in enumerate(event_Index):
        start_index = index - 1  # 起始索引（注意索引从 0 开始）
        end_index = start_index + frameRate + int(frameRate/2)  # 结束索引（不包含该行
        # 生成小的 txt 文件名
        os.makedirs(store_path, exist_ok=True)
        small_filename = f"{store_path}/{L_or_R}_{i + startName}.txt"
        # print("small_filename:", small_filename)
        # 打开大的 txt 文件和小的 txt 文件
        with open(big_filename, 'r') as large_file, open(small_filename, 'w') as small_file:
            # 从大的 txt 文件中读取指定行数的数据
            lines = large_file.readlines()[start_index:end_index]
            # 将数据写入小的 txt 文件
            small_file.writelines(lines)


def uni_store(resampled_audio, frameRate, source_filename, target_filename, big_txt, L_or_R, startName):
    # 读取数据
    dataX, dataY, dataZ = read_date(source_filename + "/" + big_txt)
    # print("type(dataZ):", type(dataZ))
    # current_axis = dataZ[:-2000]
    current_axis = dataZ
    # print("current_axis:", current_axis)

    print("source_filename:", source_filename + "/" + big_txt)
    event_Index = signal_event_detection(current_axis, resampled_audio, frameRate, 55)

    #如果连续两个点的间隔<400, 则报异常
    try:
        check_difference(event_Index)
    except ValueError as e:
        print(e)

    # print("event_Index:", event_Index)
    # 存储数据
    big_filename = source_filename + "/" + big_txt
    store_path = target_filename
    store_segment(event_Index, big_filename, store_path, L_or_R, startName)


if __name__ == '__main__':

    input_file = "50_800_W.wav"
    # frameRate = 467
    frameRate = 467
    # 读取原始音频文件
    original_sampling_rate, audio_data = wav.read(input_file)
    # 计算采样率的比例
    sampling_rate_ratio = frameRate / original_sampling_rate
    # 计算目标音频数据的长度
    target_length = int(len(audio_data) * sampling_rate_ratio)
    # 执行音频重采样
    resampled_audio = resample(audio_data, target_length)

    UsernameList = ['1','4','5','17','20']
    for Username in UsernameList:
    # UserNum = "20"
        source_filename = "/Earphone_IMU/DataSetOrigin/掐头去尾的数据/dataSet/补充数据集/head_movement/" + Username
        target_filename = "/Earphone_IMU/DataSetPreprocess/head_movement/segment/static/" + Username

        uni_store(resampled_audio, frameRate, source_filename, target_filename, "static_L.txt", "L", 1)

        uni_store(resampled_audio, frameRate, source_filename, target_filename, "static_R.txt", "R", 1)

    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "L1.txt", "L", 1)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "L2.txt", "L", 51)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "L3.txt", "L", 101)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "L4.txt", "L", 151)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "L5.txt", "L", 201)
    #
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "R1.txt", "R", 1)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "R2.txt", "R", 51)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "R3.txt", "R", 101)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "R4.txt", "R", 151)
    # uni_store(resampled_audio, frameRate, source_filename, target_filename, "R5.txt", "R", 201)

    # # 读取数据
    # dataX, dataY, dataZ = read_date(source_filename + "/L2.txt")
    # current_axis = dataZ
    # event_Index = signal_event_detection(current_axis, resampled_audio, frameRate, 55)
    # # 存储数据
    # big_filename = source_filename + "/L2.txt"
    # store_path = target_filename
    # L_or_R = "L"
    # startName = 50
    # store_segment(big_filename, store_path, L_or_R, startName)