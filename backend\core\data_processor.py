# -*- coding: utf-8 -*-
"""
数据处理模块
整合信号分割、特征提取等功能
"""

import os
import numpy as np
import scipy.io.wavfile as wav
from scipy.signal import resample, butter, lfilter
from pathlib import Path
import tempfile

from ..utils.config import Config
from ..utils.logger import setup_logger
from .signal_processing import EventDetection, FeatureExtraction

logger = setup_logger()

class DataProcessor:
    def __init__(self):
        self.frame_rate = Config.FRAME_RATE
        self.threshold_frequency = Config.THRESHOLD_FREQUENCY
        self.cycle_num = Config.CYCLE_NUM
        
        # 初始化信号处理组件
        self.event_detector = EventDetection()
        self.feature_extractor = FeatureExtraction()
        
        # 加载参考音频模板
        self.reference_audio = self._load_reference_audio()
    
    def _load_reference_audio(self):
        """加载参考音频模板"""
        try:
            # 查找参考音频文件
            reference_files = [
                Config.BASE_DIR.parent / "Data_Split" / "50_800_W.wav",
                Config.BASE_DIR.parent / "Data_Split" / "50_800_50W.wav",
                Config.BASE_DIR / "data" / "reference" / "template.wav"
            ]
            
            for ref_file in reference_files:
                if ref_file.exists():
                    logger.info(f"Loading reference audio: {ref_file}")
                    original_sampling_rate, audio_data = wav.read(str(ref_file))
                    
                    # 重采样到目标采样率
                    sampling_rate_ratio = self.frame_rate / original_sampling_rate
                    target_length = int(len(audio_data) * sampling_rate_ratio)
                    resampled_audio = resample(audio_data, target_length)
                    
                    return self._zero_mean_norm(resampled_audio)
            
            # 如果没有找到参考音频，生成一个默认的
            logger.warning("No reference audio found, generating default template")
            return self._generate_default_template()
            
        except Exception as e:
            logger.error(f"Error loading reference audio: {e}")
            return self._generate_default_template()
    
    def _generate_default_template(self):
        """生成默认音频模板"""
        # 生成一个简单的正弦波作为默认模板
        duration = 2.0  # 2秒
        t = np.linspace(0, duration, int(self.frame_rate * duration))
        frequency = 50  # 50Hz
        template = np.sin(2 * np.pi * frequency * t)
        return self._zero_mean_norm(template)
    
    def _zero_mean_norm(self, data):
        """零均值归一化"""
        data = np.array(data, dtype=float)
        mean_val = np.mean(data)
        std_val = np.std(data)
        if std_val == 0:
            return data - mean_val
        return (data - mean_val) / std_val
    
    def _high_pass_filter(self, data, cutoff_freq, sampling_rate):
        """高通滤波器"""
        filter_order = 4
        nyquist_freq = 0.5 * sampling_rate
        cutoff = cutoff_freq / nyquist_freq
        b, a = butter(filter_order, cutoff, btype='highpass')
        filtered_data = lfilter(b, a, data)
        return filtered_data
    
    def _read_acceleration_data(self, file_path):
        """读取加速度数据文件"""
        try:
            if file_path.endswith('.wav'):
                # 如果是音频文件，直接读取
                sampling_rate, audio_data = wav.read(file_path)
                
                # 重采样到目标采样率
                if sampling_rate != self.frame_rate:
                    sampling_rate_ratio = self.frame_rate / sampling_rate
                    target_length = int(len(audio_data) * sampling_rate_ratio)
                    audio_data = resample(audio_data, target_length)
                
                # 假设音频数据代表Z轴加速度
                return audio_data, audio_data, audio_data
            
            elif file_path.endswith('.txt'):
                # 如果是文本文件，按照原有格式读取
                data = np.loadtxt(file_path)
                if data.shape[1] >= 3:
                    return data[:, 0], data[:, 1], data[:, 2]  # X, Y, Z轴
                else:
                    # 如果只有一列数据，复制到三个轴
                    single_axis = data[:, 0] if len(data.shape) > 1 else data
                    return single_axis, single_axis, single_axis
            
            else:
                raise ValueError(f"Unsupported file format: {file_path}")
                
        except Exception as e:
            logger.error(f"Error reading acceleration data from {file_path}: {e}")
            raise
    
    def process_registration_data(self, user_id, user_files):
        """处理注册数据"""
        try:
            logger.info(f"Processing registration data for user {user_id}")
            
            all_features = []
            
            # 处理每个数据类型的文件
            for data_type, file_info in user_files.items():
                file_path = file_info['path']
                logger.info(f"Processing {data_type} file: {file_path}")
                
                # 读取加速度数据
                dataX, dataY, dataZ = self._read_acceleration_data(file_path)
                
                # 数据预处理
                dataX = self._zero_mean_norm(dataX)
                dataY = self._zero_mean_norm(dataY)
                dataZ = self._zero_mean_norm(dataZ)
                
                # 高通滤波
                dataX = self._high_pass_filter(dataX, self.threshold_frequency, self.frame_rate)
                dataY = self._high_pass_filter(dataY, self.threshold_frequency, self.frame_rate)
                dataZ = self._high_pass_filter(dataZ, self.threshold_frequency, self.frame_rate)
                
                # 事件检测和信号分割
                event_indices = self.event_detector.detect_events(
                    dataZ, self.reference_audio, self.frame_rate, self.cycle_num
                )
                
                # 特征提取
                features = self.feature_extractor.extract_features(
                    dataX, dataY, dataZ, self.reference_audio, self.frame_rate
                )
                
                all_features.extend(features)
            
            logger.info(f"Extracted {len(all_features)} features for user {user_id}")
            return np.array(all_features)
            
        except Exception as e:
            logger.error(f"Error processing registration data for user {user_id}: {e}")
            raise
    
    def process_login_data(self, file_path):
        """处理登录验证数据"""
        try:
            logger.info(f"Processing login data: {file_path}")
            
            # 读取加速度数据
            dataX, dataY, dataZ = self._read_acceleration_data(file_path)
            
            # 数据预处理
            dataX = self._zero_mean_norm(dataX)
            dataY = self._zero_mean_norm(dataY)
            dataZ = self._zero_mean_norm(dataZ)
            
            # 高通滤波
            dataX = self._high_pass_filter(dataX, self.threshold_frequency, self.frame_rate)
            dataY = self._high_pass_filter(dataY, self.threshold_frequency, self.frame_rate)
            dataZ = self._high_pass_filter(dataZ, self.threshold_frequency, self.frame_rate)
            
            # 特征提取
            features = self.feature_extractor.extract_features(
                dataX, dataY, dataZ, self.reference_audio, self.frame_rate
            )
            
            logger.info(f"Extracted {len(features)} features for login verification")
            return np.array(features)
            
        except Exception as e:
            logger.error(f"Error processing login data: {e}")
            raise
    
    def save_processed_data(self, user_id, data, data_type):
        """保存处理后的数据"""
        try:
            # 创建用户数据目录
            user_data_dir = Config.UPLOAD_FOLDER / user_id / "processed"
            os.makedirs(user_data_dir, exist_ok=True)
            
            # 保存数据
            file_path = user_data_dir / f"{data_type}_features.npy"
            np.save(str(file_path), data)
            
            logger.info(f"Saved processed data for user {user_id}: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Error saving processed data: {e}")
            raise
    
    def load_processed_data(self, user_id, data_type):
        """加载处理后的数据"""
        try:
            file_path = Config.UPLOAD_FOLDER / user_id / "processed" / f"{data_type}_features.npy"
            
            if not file_path.exists():
                return None
            
            data = np.load(str(file_path))
            logger.info(f"Loaded processed data for user {user_id}: {file_path}")
            return data
            
        except Exception as e:
            logger.error(f"Error loading processed data: {e}")
            return None
