@echo off
chcp 65001 >nul
title 前端依赖安装

echo ================================================================
echo 🎨 HCR-Auth 前端依赖安装
echo ================================================================
echo.

echo 📍 当前目录: %CD%
echo.

echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Node.js，请安装Node.js 16+
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
echo.

echo 📁 进入前端目录...
cd /d "E:\HCR\front_end\HCR-Auth"
if not exist package.json (
    echo ❌ 未找到package.json文件
    echo 当前目录: %CD%
    pause
    exit /b 1
)

echo ✅ 找到package.json文件
echo.

echo 🧹 清理旧的安装...
if exist node_modules (
    echo 删除 node_modules...
    rmdir /s /q node_modules
)
if exist package-lock.json (
    echo 删除 package-lock.json...
    del package-lock.json
)

echo 🔧 清理npm缓存...
npm cache clean --force

echo.
echo 📦 开始安装依赖（使用国内镜像）...
echo 这可能需要几分钟时间，请耐心等待...
echo.

npm install --registry=https://registry.npmmirror.com --no-optional

if errorlevel 1 (
    echo.
    echo ❌ 安装失败，尝试备用方案...
    echo.
    echo 🔄 尝试使用官方源...
    npm install --no-optional
    
    if errorlevel 1 (
        echo ❌ 安装仍然失败
        echo.
        echo 💡 建议：
        echo 1. 检查网络连接
        echo 2. 尝试使用手机热点
        echo 3. 或者跳过前端，只运行后端进行测试
        pause
        exit /b 1
    )
)

echo.
echo ✅ 前端依赖安装成功！
echo.
echo 🚀 启动前端开发服务器...
echo 📍 前端地址: http://localhost:5173
echo 🛑 按 Ctrl+C 停止服务
echo.

npm run dev

pause
