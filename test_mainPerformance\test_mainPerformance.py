# -*- coding:utf-8 -*-

import torch
import random
import torch.optim as optim
import numpy as np
from torch import nn

from OriginDataSet import getOriginDataset, getIllegal, GetSubset
from SiameseDatasets import IncrementalDataset, TripletMNIST
from torch.utils.data import DataLoader
from torch.optim import lr_scheduler

from losses import ContrastiveLoss, TripletLoss
from networks import SiameseNet
from trainer import fit


def count_samples_above_threshold(lst, t):
    count = 0
    for sample in lst:
        if sample > t:
            count += 1
    return count

if __name__ == '__main__':


    # embedding_net = torch.load("embedding_net.pth")
    # model = torch.load("model.pth")

    # 设置增量学习超参数
    batchSize_list = [8]
    lr_list = [0.001]
    n_epochs_list = [40]
    trainSampleNum = 10

    # Threshold_list = [0.46]
    thresh = 0.46

    seed_list = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000]

    for seed in seed_list:
        for lr in lr_list:
            for n_epochs in n_epochs_list:
                for batchSize in  batchSize_list:

                    # FAR_list = []
                    # FRR_list = []
                    # BAC_list = []
                    distance_le = []
                    distance_ile = []

                    for i in range(1, 46):

                        embedding_net = torch.load("embedding_net.pth")
                        embedding_net.train()  # 设置网络为可训练状态
                        for name, module in embedding_net.named_modules():
                            if isinstance(module, nn.Dropout):
                                module.p = 0  # 修改 dropRate 的值为0

                        UserID = i
                        # print("UserID:", UserID)
                        distance_le_curr = []
                        distance_ile_curr = []

                        # 构造默认数据集
                        Defult_User = getOriginDataset('/root/project/Paper2/dataset', startUser=46, endUser=61)
                        # 构造用于预训练的数据集
                        User = getOriginDataset('/root/project/Paper2/dataset', startUser=UserID, endUser=UserID + 1)
                        # 构造非法数据集
                        Illegal_User = getIllegal('/root/project/Paper2/dataset', startUser=1, endUser=46,
                                                  legalUser=UserID)  # 将'/path/to/dataset'替换为你的数据集路径

                        # 训练、测试集划分
                        train_indices = []
                        test_indices = []
                        indices = [j for j, label in enumerate(User.labels) if label == UserID]
                        # print("indices:", indices)
                        random.seed(seed)
                        train_indices.extend(random.sample(indices, trainSampleNum))  # 随机选择1trainSampleNum个样本作为训练集

                        # samples, labels = getAugmentSample(UserID, train_indices, resampled_audio, frameRate)
                        # train_User = getIncreData(train=True, sampleList=samples, labelList=labels)
                        # '''这里的train_indices作为样本的下标，对原始样本使用数据增强，构建数据集'''
                        test_indices.extend([index for index in indices if index not in train_indices])  # 其余样本作为测试集
                        train_User = GetSubset(User, train_indices, train=True)
                        test_User = GetSubset(User, test_indices, train=False)

                        # 构造增量学习数据集
                        IncrementalTrain = IncrementalDataset(train_User, Defult_User,
                                                              UserID)  # Returns pairs of images and target same/different
                        # 加载增量学习数据集
                        cuda = torch.cuda.is_available()
                        kwargs = {'num_workers': 1, 'pin_memory': True} if cuda else {}
                        IncrementalTrainset_loader = DataLoader(IncrementalTrain, batch_size=batchSize, shuffle=False, **kwargs)
                        # 初始化模型
                        margin = 1.
                        model = SiameseNet(embedding_net)
                        if cuda:
                            # embedding_net.cuda()
                            model.cuda()

                        # 设置损失函数 优化器
                        loss_fn = ContrastiveLoss(margin)
                        # loss_fn = TripletLoss(margin)
                        optimizer = optim.Adam(model.parameters(), lr=lr)
                        scheduler = lr_scheduler.StepLR(optimizer, 20, gamma=0.1, last_epoch=-1)
                        log_interval = 50
                        # 训练
                        trainloss = fit(IncrementalTrainset_loader, model, loss_fn, optimizer, scheduler, n_epochs, cuda, log_interval)

                        # 开始测试模型
                        ''' 计算当前用户的模板 '''
                        temp_dataloader = DataLoader(train_User, batch_size=1)
                        output_list = []
                        with torch.no_grad():
                            for data in temp_dataloader:
                                sample, target = data
                                sample = sample.cuda()
                                output = embedding_net(sample)
                                output_list.append(output)
                        concatenated = torch.cat(output_list, dim=0)
                        summed = torch.sum(concatenated, dim=0)
                        template = summed / len(output_list)
                        template = template.unsqueeze(0)  # 扩张一个维度变成[1,32]
                        # print("template:", template)



                        '''开始测试(计算合法、非法样本的输出)'''
                        Illegal_dataloader = DataLoader(Illegal_User, batch_size=1)
                        legal_dataloader = DataLoader(test_User, batch_size=1)
                        embedding_net.eval()


                        with torch.no_grad():
                            for data in legal_dataloader:
                                samples, label = data
                                samples = samples.cuda()
                                outputs = embedding_net(samples)
                                # 计算template和当前output的欧氏距离
                                distance = torch.dist(template, outputs)
                                distance_le.append(distance)
                        with torch.no_grad():
                            for data in Illegal_dataloader:
                                samples, label = data
                                samples = samples.cuda()
                                outputs = embedding_net(samples)
                                # 计算template和当前output的欧氏距离
                                distance = torch.dist(template, outputs)
                                distance_ile.append(distance)

                        # 计算当前BAC
                        # FN = count_samples_above_threshold(distance_le_curr, thresh)
                        # TP = len(distance_le_curr) - FN
                        # TN = count_samples_above_threshold(distance_ile_curr, thresh)
                        # FP = len(distance_ile_curr) - TN
                        # FAR = FP / (FP + TN)
                        # FRR = FN / (TP + FN)
                        # TPR = TP / (TP + FN)
                        # TNR = TN / (TN + FP)
                        # BAC = 1 / 2 * (TPR + TNR)

                        # message = '\nUserID: {}, FAR: {}, FRR: {}, BAC: {}'.format(UserID, FAR, FRR, BAC)
                        # print(message)


                    # 计算总体BAC
                    FN = count_samples_above_threshold(distance_le, thresh)
                    TP = len(distance_le) - FN
                    TN = count_samples_above_threshold(distance_ile, thresh)
                    FP = len(distance_ile) - TN
                    FAR = FP /(FP+TN)
                    FRR = FN /(TP+FN)
                    TPR = TP /(TP+FN)
                    TNR = TN /(TN+FP)
                    BAC = 1/2 * (TPR + TNR)

                    #     FAR_list.append(FAR)
                    #     FRR_list.append(FRR)
                    #     BAC_list.append(BAC)
                    #
                    # FAR = np.mean(FAR_list)
                    # FRR = np.mean(FRR_list)
                    # BAC = np.mean(BAC_list)

                    message = '\nbatchSize: {}, lr: {}, n_epochs: {}, seed: {}, FAR: {}, FRR: {}, BAC: {}'.format(batchSize, lr, n_epochs, seed, FAR, FRR, BAC)
                    print(message)







